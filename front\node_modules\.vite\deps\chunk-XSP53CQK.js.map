{"version": 3, "sources": ["../../@material/web/progress/internal/circular-progress.ts", "../../@material/web/progress/internal/circular-progress-styles.ts", "../../@material/web/progress/circular-progress.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {html} from 'lit';\n\nimport {Progress} from './progress.js';\n\n/**\n * A circular progress component.\n */\nexport class CircularProgress extends Progress {\n  protected override renderIndicator() {\n    if (this.indeterminate) {\n      return this.renderIndeterminateContainer();\n    }\n\n    return this.renderDeterminateContainer();\n  }\n\n  // Determinate mode is rendered with an svg so the progress arc can be\n  // easily animated via stroke-dashoffset.\n  private renderDeterminateContainer() {\n    const dashOffset = (1 - this.value / this.max) * 100;\n    // note, dash-array/offset are relative to Setting `pathLength` but\n    // Chrome seems to render this inaccurately and using a large viewbox helps.\n    return html`\n      <svg viewBox=\"0 0 4800 4800\">\n        <circle class=\"track\" pathLength=\"100\"></circle>\n        <circle\n          class=\"active-track\"\n          pathLength=\"100\"\n          stroke-dashoffset=${dashOffset}></circle>\n      </svg>\n    `;\n  }\n\n  // Indeterminate mode rendered with 2 bordered-divs. The borders are\n  // clipped into half circles by their containers. The divs are then carefully\n  // animated to produce changes to the spinner arc size.\n  // This approach has 4.5x the FPS of rendering via svg on Chrome 111.\n  // See https://lit.dev/playground/#gist=febb773565272f75408ab06a0eb49746.\n  private renderIndeterminateContainer() {\n    return html` <div class=\"spinner\">\n      <div class=\"left\">\n        <div class=\"circle\"></div>\n      </div>\n      <div class=\"right\">\n        <div class=\"circle\"></div>\n      </div>\n    </div>`;\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n// Generated stylesheet for ./progress/internal/circular-progress-styles.css.\nimport {css} from 'lit';\nexport const styles = css`:host{--_active-indicator-color: var(--md-circular-progress-active-indicator-color, var(--md-sys-color-primary, #6750a4));--_active-indicator-width: var(--md-circular-progress-active-indicator-width, 10);--_four-color-active-indicator-four-color: var(--md-circular-progress-four-color-active-indicator-four-color, var(--md-sys-color-tertiary-container, #ffd8e4));--_four-color-active-indicator-one-color: var(--md-circular-progress-four-color-active-indicator-one-color, var(--md-sys-color-primary, #6750a4));--_four-color-active-indicator-three-color: var(--md-circular-progress-four-color-active-indicator-three-color, var(--md-sys-color-tertiary, #7d5260));--_four-color-active-indicator-two-color: var(--md-circular-progress-four-color-active-indicator-two-color, var(--md-sys-color-primary-container, #eaddff));--_size: var(--md-circular-progress-size, 48px);display:inline-flex;vertical-align:middle;width:var(--_size);height:var(--_size);position:relative;align-items:center;justify-content:center;contain:strict;content-visibility:auto}.progress{flex:1;align-self:stretch;margin:4px}.progress,.spinner,.left,.right,.circle,svg,.track,.active-track{position:absolute;inset:0}svg{transform:rotate(-90deg)}circle{cx:50%;cy:50%;r:calc(50%*(1 - var(--_active-indicator-width)/100));stroke-width:calc(var(--_active-indicator-width)*1%);stroke-dasharray:100;fill:rgba(0,0,0,0)}.active-track{transition:stroke-dashoffset 500ms cubic-bezier(0, 0, 0.2, 1);stroke:var(--_active-indicator-color)}.track{stroke:rgba(0,0,0,0)}.progress.indeterminate{animation:linear infinite linear-rotate;animation-duration:1568.2352941176ms}.spinner{animation:infinite both rotate-arc;animation-duration:5332ms;animation-timing-function:cubic-bezier(0.4, 0, 0.2, 1)}.left{overflow:hidden;inset:0 50% 0 0}.right{overflow:hidden;inset:0 0 0 50%}.circle{box-sizing:border-box;border-radius:50%;border:solid calc(var(--_active-indicator-width)/100*(var(--_size) - 8px));border-color:var(--_active-indicator-color) var(--_active-indicator-color) rgba(0,0,0,0) rgba(0,0,0,0);animation:expand-arc;animation-iteration-count:infinite;animation-fill-mode:both;animation-duration:1333ms,5332ms;animation-timing-function:cubic-bezier(0.4, 0, 0.2, 1)}.four-color .circle{animation-name:expand-arc,four-color}.left .circle{rotate:135deg;inset:0 -100% 0 0}.right .circle{rotate:100deg;inset:0 0 0 -100%;animation-delay:-666.5ms,0ms}@media(forced-colors: active){.active-track{stroke:CanvasText}.circle{border-color:CanvasText CanvasText Canvas Canvas}}@keyframes expand-arc{0%{transform:rotate(265deg)}50%{transform:rotate(130deg)}100%{transform:rotate(265deg)}}@keyframes rotate-arc{12.5%{transform:rotate(135deg)}25%{transform:rotate(270deg)}37.5%{transform:rotate(405deg)}50%{transform:rotate(540deg)}62.5%{transform:rotate(675deg)}75%{transform:rotate(810deg)}87.5%{transform:rotate(945deg)}100%{transform:rotate(1080deg)}}@keyframes linear-rotate{to{transform:rotate(360deg)}}@keyframes four-color{0%{border-top-color:var(--_four-color-active-indicator-one-color);border-right-color:var(--_four-color-active-indicator-one-color)}15%{border-top-color:var(--_four-color-active-indicator-one-color);border-right-color:var(--_four-color-active-indicator-one-color)}25%{border-top-color:var(--_four-color-active-indicator-two-color);border-right-color:var(--_four-color-active-indicator-two-color)}40%{border-top-color:var(--_four-color-active-indicator-two-color);border-right-color:var(--_four-color-active-indicator-two-color)}50%{border-top-color:var(--_four-color-active-indicator-three-color);border-right-color:var(--_four-color-active-indicator-three-color)}65%{border-top-color:var(--_four-color-active-indicator-three-color);border-right-color:var(--_four-color-active-indicator-three-color)}75%{border-top-color:var(--_four-color-active-indicator-four-color);border-right-color:var(--_four-color-active-indicator-four-color)}90%{border-top-color:var(--_four-color-active-indicator-four-color);border-right-color:var(--_four-color-active-indicator-four-color)}100%{border-top-color:var(--_four-color-active-indicator-one-color);border-right-color:var(--_four-color-active-indicator-one-color)}}\n`;\n", "/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {CircularProgress} from './internal/circular-progress.js';\nimport {styles} from './internal/circular-progress-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-circular-progress': MdCircularProgress;\n  }\n}\n\n/**\n * @summary Circular progress indicators display progress by animating along an\n * invisible circular track in a clockwise direction. They can be applied\n * directly to a surface, such as a button or card.\n *\n * @description\n * Progress indicators inform users about the status of ongoing processes.\n * - Determinate indicators display how long a process will take.\n * - Indeterminate indicators express an unspecified amount of wait time.\n *\n * @final\n * @suppress {visibility}\n */\n@customElement('md-circular-progress')\nexport class MdCircularProgress extends CircularProgress {\n  static override styles: CSSResultOrNative[] = [styles];\n}\n"], "mappings": ";;;;;;;;;;;;;AAaM,IAAO,mBAAP,cAAgC,SAAQ;EACzB,kBAAe;AAChC,QAAI,KAAK,eAAe;AACtB,aAAO,KAAK,6BAA4B;IAC1C;AAEA,WAAO,KAAK,2BAA0B;EACxC;;;EAIQ,6BAA0B;AAChC,UAAM,cAAc,IAAI,KAAK,QAAQ,KAAK,OAAO;AAGjD,WAAO;;;;;;8BAMmB,UAAU;;;EAGtC;;;;;;EAOQ,+BAA4B;AAClC,WAAO;;;;;;;;EAQT;;;;AC9CK,IAAM,SAAS;;;;ACyBf,IAAM,qBAAN,MAAMA,4BAA2B,iBAAgB;;AACtC,mBAAA,SAA8B,CAAC,MAAM;AAD1C,qBAAkB,WAAA;EAD9B,cAAc,sBAAsB;GACxB,kBAAkB;", "names": ["MdCircularProgress"]}