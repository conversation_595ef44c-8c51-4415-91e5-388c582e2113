{"version": 3, "sources": ["../../@material/web/button/internal/text-button.ts", "../../@material/web/button/internal/text-styles.ts", "../../@material/web/button/text-button.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {Button} from './button.js';\n\n/**\n * A text button component.\n */\nexport class TextButton extends Button {}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n// Generated stylesheet for ./button/internal/text-styles.css.\nimport {css} from 'lit';\nexport const styles = css`:host{--_container-height: var(--md-text-button-container-height, 40px);--_disabled-label-text-color: var(--md-text-button-disabled-label-text-color, var(--md-sys-color-on-surface, #1d1b20));--_disabled-label-text-opacity: var(--md-text-button-disabled-label-text-opacity, 0.38);--_focus-label-text-color: var(--md-text-button-focus-label-text-color, var(--md-sys-color-primary, #6750a4));--_hover-label-text-color: var(--md-text-button-hover-label-text-color, var(--md-sys-color-primary, #6750a4));--_hover-state-layer-color: var(--md-text-button-hover-state-layer-color, var(--md-sys-color-primary, #6750a4));--_hover-state-layer-opacity: var(--md-text-button-hover-state-layer-opacity, 0.08);--_label-text-color: var(--md-text-button-label-text-color, var(--md-sys-color-primary, #6750a4));--_label-text-font: var(--md-text-button-label-text-font, var(--md-sys-typescale-label-large-font, var(--md-ref-typeface-plain, Roboto)));--_label-text-line-height: var(--md-text-button-label-text-line-height, var(--md-sys-typescale-label-large-line-height, 1.25rem));--_label-text-size: var(--md-text-button-label-text-size, var(--md-sys-typescale-label-large-size, 0.875rem));--_label-text-weight: var(--md-text-button-label-text-weight, var(--md-sys-typescale-label-large-weight, var(--md-ref-typeface-weight-medium, 500)));--_pressed-label-text-color: var(--md-text-button-pressed-label-text-color, var(--md-sys-color-primary, #6750a4));--_pressed-state-layer-color: var(--md-text-button-pressed-state-layer-color, var(--md-sys-color-primary, #6750a4));--_pressed-state-layer-opacity: var(--md-text-button-pressed-state-layer-opacity, 0.12);--_disabled-icon-color: var(--md-text-button-disabled-icon-color, var(--md-sys-color-on-surface, #1d1b20));--_disabled-icon-opacity: var(--md-text-button-disabled-icon-opacity, 0.38);--_focus-icon-color: var(--md-text-button-focus-icon-color, var(--md-sys-color-primary, #6750a4));--_hover-icon-color: var(--md-text-button-hover-icon-color, var(--md-sys-color-primary, #6750a4));--_icon-color: var(--md-text-button-icon-color, var(--md-sys-color-primary, #6750a4));--_icon-size: var(--md-text-button-icon-size, 18px);--_pressed-icon-color: var(--md-text-button-pressed-icon-color, var(--md-sys-color-primary, #6750a4));--_container-shape-start-start: var(--md-text-button-container-shape-start-start, var(--md-text-button-container-shape, var(--md-sys-shape-corner-full, 9999px)));--_container-shape-start-end: var(--md-text-button-container-shape-start-end, var(--md-text-button-container-shape, var(--md-sys-shape-corner-full, 9999px)));--_container-shape-end-end: var(--md-text-button-container-shape-end-end, var(--md-text-button-container-shape, var(--md-sys-shape-corner-full, 9999px)));--_container-shape-end-start: var(--md-text-button-container-shape-end-start, var(--md-text-button-container-shape, var(--md-sys-shape-corner-full, 9999px)));--_leading-space: var(--md-text-button-leading-space, 12px);--_trailing-space: var(--md-text-button-trailing-space, 12px);--_with-leading-icon-leading-space: var(--md-text-button-with-leading-icon-leading-space, 12px);--_with-leading-icon-trailing-space: var(--md-text-button-with-leading-icon-trailing-space, 16px);--_with-trailing-icon-leading-space: var(--md-text-button-with-trailing-icon-leading-space, 16px);--_with-trailing-icon-trailing-space: var(--md-text-button-with-trailing-icon-trailing-space, 12px);--_container-color: none;--_disabled-container-color: none;--_disabled-container-opacity: 0}\n`;\n", "/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {styles as sharedStyles} from './internal/shared-styles.js';\nimport {TextButton} from './internal/text-button.js';\nimport {styles as textStyles} from './internal/text-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-text-button': MdTextButton;\n  }\n}\n\n/**\n * @summary Buttons help people take action, such as sending an email, sharing a\n * document, or liking a comment.\n *\n * @description\n * __Emphasis:__ Low emphasis – For optional or supplementary actions with the\n * least amount of prominence\n *\n * __Rationale:__ Text buttons have less visual prominence, so should be used\n * for low emphasis actions, such as an alternative option.\n *\n * __Example usages:__\n * - Learn more\n * - View all\n * - Change account\n * - Turn on\n *\n * @final\n * @suppress {visibility}\n */\n@customElement('md-text-button')\nexport class MdTextButton extends TextButton {\n  static override styles: CSSResultOrNative[] = [sharedStyles, textStyles];\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAWM,IAAO,aAAP,cAA0B,OAAM;;;;ACJ/B,IAAMA,UAAS;;;;ACiCf,IAAM,eAAN,MAAMC,sBAAqB,WAAU;;AAC1B,aAAA,SAA8B,CAAC,QAAcC,OAAU;AAD5D,eAAY,WAAA;EADxB,cAAc,gBAAgB;GAClB,YAAY;", "names": ["styles", "MdTextButton", "styles"]}