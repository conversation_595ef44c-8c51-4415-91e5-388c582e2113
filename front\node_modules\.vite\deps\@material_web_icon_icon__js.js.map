{"version": 3, "sources": ["../../@material/web/icon/internal/icon.ts", "../../@material/web/icon/internal/icon-styles.ts", "../../@material/web/icon/icon.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2022 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {html, LitElement} from 'lit';\n\n/**\n * TODO(b/265336902): add docs\n */\nexport class Icon extends LitElement {\n  protected override render() {\n    return html`<slot></slot>`;\n  }\n\n  override connectedCallback() {\n    super.connectedCallback();\n    const ariaHidden = this.getAttribute('aria-hidden');\n    if (ariaHidden === 'false') {\n      // Allow the user to set `aria-hidden=\"false\"` to create an icon that is\n      // announced by screenreaders.\n      this.removeAttribute('aria-hidden');\n      return;\n    }\n\n    // Needed for VoiceOver, which will create a \"group\" if the element is a\n    // sibling to other content.\n    this.setAttribute('aria-hidden', 'true');\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n// Generated stylesheet for ./icon/internal/icon-styles.css.\nimport {css} from 'lit';\nexport const styles = css`:host{font-size:var(--md-icon-size, 24px);width:var(--md-icon-size, 24px);height:var(--md-icon-size, 24px);color:inherit;font-variation-settings:inherit;font-weight:400;font-family:var(--md-icon-font, Material Symbols Outlined);display:inline-flex;font-style:normal;place-items:center;place-content:center;line-height:1;overflow:hidden;letter-spacing:normal;text-transform:none;user-select:none;white-space:nowrap;word-wrap:normal;flex-shrink:0;-webkit-font-smoothing:antialiased;text-rendering:optimizeLegibility;-moz-osx-font-smoothing:grayscale}::slotted(svg){fill:currentColor}::slotted(*){height:100%;width:100%}\n`;\n", "/**\n * @license\n * Copyright 2022 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {Icon} from './internal/icon.js';\nimport {styles} from './internal/icon-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-icon': MdIcon;\n  }\n}\n\n/**\n * @final\n * @suppress {visibility}\n */\n@customElement('md-icon')\nexport class MdIcon extends Icon {\n  /** @nocollapse */\n  static override styles: CSSResultOrNative[] = [styles];\n}\n"], "mappings": ";;;;;;;;;;;;;;AAWM,IAAO,OAAP,cAAoB,WAAU;EACf,SAAM;AACvB,WAAO;EACT;EAES,oBAAiB;AACxB,UAAM,kBAAiB;AACvB,UAAM,aAAa,KAAK,aAAa,aAAa;AAClD,QAAI,eAAe,SAAS;AAG1B,WAAK,gBAAgB,aAAa;AAClC;IACF;AAIA,SAAK,aAAa,eAAe,MAAM;EACzC;;;;ACtBK,IAAM,SAAS;;;;ACgBf,IAAM,SAAN,MAAMA,gBAAe,KAAI;;AAEd,OAAA,SAA8B,CAAC,MAAM;AAF1C,SAAM,WAAA;EADlB,cAAc,SAAS;GACX,MAAM;", "names": ["MdIcon"]}