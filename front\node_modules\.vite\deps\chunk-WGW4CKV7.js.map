{"version": 3, "sources": ["../../@material/web/internal/motion/animation.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\n/**\n * Easing functions to use for web animations.\n *\n * **NOTE:** `EASING.EMPHASIZED` is approximated with unknown accuracy.\n *\n * TODO(b/241113345): replace with tokens\n */\nexport const EASING = {\n  STANDARD: 'cubic-bezier(0.2, 0, 0, 1)',\n  STANDARD_ACCELERATE: 'cubic-bezier(.3,0,1,1)',\n  STANDARD_DECELERATE: 'cubic-bezier(0,0,0,1)',\n  EMPHASIZED: 'cubic-bezier(.3,0,0,1)',\n  EMPHASIZED_ACCELERATE: 'cubic-bezier(.3,0,.8,.15)',\n  EMPHASIZED_DECELERATE: 'cubic-bezier(.05,.7,.1,1)',\n} as const;\n\n/**\n * A signal that is used for abortable tasks.\n */\nexport interface AnimationSignal {\n  /**\n   * Starts the abortable task. Any previous tasks started with this instance\n   * will be aborted.\n   *\n   * @return An `AbortSignal` for the current task.\n   */\n  start(): AbortSignal;\n  /**\n   * Complete the current task.\n   */\n  finish(): void;\n}\n\n/**\n * Creates an `AnimationSignal` that can be used to cancel a previous task.\n *\n * @example\n * class MyClass {\n *   private labelAnimationSignal = createAnimationSignal();\n *\n *   private async animateLabel() {\n *     // Start of the task. Previous tasks will be canceled.\n *     const signal = this.labelAnimationSignal.start();\n *\n *     // Do async work...\n *     if (signal.aborted) {\n *       // Use AbortSignal to check if a request was made to abort after some\n *       // asynchronous work.\n *       return;\n *     }\n *\n *     const animation = this.animate(...);\n *     // Add event listeners to be notified when the task should be canceled.\n *     signal.addEventListener('abort', () => {\n *       animation.cancel();\n *     });\n *\n *     animation.addEventListener('finish', () => {\n *       // Tell the signal that the current task is finished.\n *       this.labelAnimationSignal.finish();\n *     });\n *   }\n * }\n *\n * @return An `AnimationSignal`.\n */\nexport function createAnimationSignal(): AnimationSignal {\n  // The current animation's AbortController\n  let animationAbortController: AbortController | null = null;\n\n  return {\n    start() {\n      // Tell the previous animation to cancel.\n      animationAbortController?.abort();\n      // Set up a new AbortController for the current animation.\n      animationAbortController = new AbortController();\n      // Provide the AbortSignal so that the caller can check aborted status\n      // and add listeners.\n      return animationAbortController.signal;\n    },\n    finish() {\n      animationAbortController = null;\n    },\n  };\n}\n\n/**\n * Returns a function which can be used to throttle function calls\n * mapped to a key via a given function that should produce a promise that\n * determines the throttle amount (defaults to requestAnimationFrame).\n */\nexport function createThrottle() {\n  const stack = new Set();\n  return async (\n    key = '',\n    cb: (...args: unknown[]) => unknown,\n    timeout = async () => {\n      await new Promise(requestAnimationFrame);\n    },\n  ) => {\n    if (!stack.has(key)) {\n      stack.add(key);\n      await timeout();\n      if (stack.has(key)) {\n        stack.delete(key);\n        cb();\n      }\n    }\n  };\n}\n\n/**\n * Parses an number in milliseconds from a css time value\n */\nexport function msFromTimeCSSValue(value: string) {\n  const match = value.trim().match(/([\\d.]+)(\\s*s$)?/);\n  const time = match?.[1];\n  const seconds = match?.[2];\n  return Number(time ?? 0) * (seconds ? 1000 : 1);\n}\n"], "mappings": ";AAaO,IAAM,SAAS;EACpB,UAAU;EACV,qBAAqB;EACrB,qBAAqB;EACrB,YAAY;EACZ,uBAAuB;EACvB,uBAAuB;;AAqDnB,SAAU,wBAAqB;AAEnC,MAAI,2BAAmD;AAEvD,SAAO;IACL,QAAK;AAEH,gCAA0B,MAAK;AAE/B,iCAA2B,IAAI,gBAAe;AAG9C,aAAO,yBAAyB;IAClC;IACA,SAAM;AACJ,iCAA2B;IAC7B;;AAEJ;", "names": []}