{"version": 3, "sources": ["../../@material/web/labs/card/internal/card.ts", "../../@material/web/labs/card/internal/shared-styles.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport '../../../elevation/elevation.js';\n\nimport {html, LitElement} from 'lit';\n\n/**\n * A card component.\n */\nexport class Card extends LitElement {\n  protected override render() {\n    return html`\n      <md-elevation part=\"elevation\"></md-elevation>\n      <div class=\"background\"></div>\n      <slot></slot>\n      <div class=\"outline\"></div>\n    `;\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n// Generated stylesheet for ./labs/card/internal/shared-styles.css.\nimport {css} from 'lit';\nexport const styles = css`:host{border-radius:var(--_container-shape);box-sizing:border-box;display:flex;flex-direction:column;position:relative;z-index:0}md-elevation,.background,.outline{border-radius:inherit;inset:0;pointer-events:none;position:absolute}.background{background:var(--_container-color);z-index:-1}.outline{border:1px solid rgba(0,0,0,0);z-index:1}md-elevation{z-index:-1;--md-elevation-level: var(--_container-elevation);--md-elevation-shadow-color: var(--_container-shadow-color)}slot{border-radius:inherit}\n`;\n"], "mappings": ";;;;;;;AAaM,IAAO,OAAP,cAAoB,WAAU;EACf,SAAM;AACvB,WAAO;;;;;;EAMT;;;;ACdK,IAAM,SAAS;;", "names": []}