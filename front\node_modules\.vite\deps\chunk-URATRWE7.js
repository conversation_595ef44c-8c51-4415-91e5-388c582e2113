import {
  mixinFocusable
} from "./chunk-WFPLX3BF.js";
import {
  EASING
} from "./chunk-WGW4CKV7.js";
import {
  classMap
} from "./chunk-SZQCPKZF.js";
import {
  __decorate,
  property,
  query,
  queryAssignedElements,
  queryAssignedNodes,
  state
} from "./chunk-PZNDE6JX.js";
import {
  LitElement,
  html,
  isServer,
  nothing
} from "./chunk-4GZ3EDRH.js";

// node_modules/@material/web/tabs/internal/tab.js
var _a;
var INDICATOR = Symbol("indicator");
var ANIMATE_INDICATOR = Symbol("animateIndicator");
var tabBaseClass = mixinFocusable(LitElement);
var Tab = class extends tabBaseClass {
  /**
   * @deprecated use `active`
   */
  get selected() {
    return this.active;
  }
  set selected(active) {
    this.active = active;
  }
  constructor() {
    super();
    this.isTab = true;
    this.active = false;
    this.hasIcon = false;
    this.iconOnly = false;
    this.fullWidthIndicator = false;
    this.internals = // Cast needed for closure
    this.attachInternals();
    if (!isServer) {
      this.internals.role = "tab";
      this.addEventListener("keydown", this.handleKeydown.bind(this));
    }
  }
  render() {
    const indicator = html`<div class="indicator"></div>`;
    return html`<div
      class="button"
      role="presentation"
      @click=${this.handleContentClick}>
      <md-focus-ring part="focus-ring" inward .control=${this}></md-focus-ring>
      <md-elevation part="elevation"></md-elevation>
      <md-ripple .control=${this}></md-ripple>
      <div
        class="content ${classMap(this.getContentClasses())}"
        role="presentation">
        <slot name="icon" @slotchange=${this.handleIconSlotChange}></slot>
        <slot @slotchange=${this.handleSlotChange}></slot>
        ${this.fullWidthIndicator ? nothing : indicator}
      </div>
      ${this.fullWidthIndicator ? indicator : nothing}
    </div>`;
  }
  getContentClasses() {
    return {
      "has-icon": this.hasIcon,
      "has-label": !this.iconOnly
    };
  }
  updated() {
    this.internals.ariaSelected = String(this.active);
  }
  async handleKeydown(event) {
    await 0;
    if (event.defaultPrevented) {
      return;
    }
    if (event.key === "Enter" || event.key === " ") {
      event.preventDefault();
      this.click();
    }
  }
  handleContentClick(event) {
    event.stopPropagation();
    this.click();
  }
  [(_a = INDICATOR, ANIMATE_INDICATOR)](previousTab) {
    if (!this[INDICATOR]) {
      return;
    }
    this[INDICATOR].getAnimations().forEach((a) => {
      a.cancel();
    });
    const frames = this.getKeyframes(previousTab);
    if (frames !== null) {
      this[INDICATOR].animate(frames, {
        duration: 250,
        easing: EASING.EMPHASIZED
      });
    }
  }
  getKeyframes(previousTab) {
    const reduceMotion = shouldReduceMotion();
    if (!this.active) {
      return reduceMotion ? [{ "opacity": 1 }, { "transform": "none" }] : null;
    }
    const from = {};
    const fromRect = previousTab[INDICATOR]?.getBoundingClientRect() ?? {};
    const fromPos = fromRect.left;
    const fromExtent = fromRect.width;
    const toRect = this[INDICATOR].getBoundingClientRect();
    const toPos = toRect.left;
    const toExtent = toRect.width;
    const scale = fromExtent / toExtent;
    if (!reduceMotion && fromPos !== void 0 && toPos !== void 0 && !isNaN(scale)) {
      from["transform"] = `translateX(${(fromPos - toPos).toFixed(4)}px) scaleX(${scale.toFixed(4)})`;
    } else {
      from["opacity"] = 0;
    }
    return [from, { "transform": "none" }];
  }
  handleSlotChange() {
    this.iconOnly = false;
    for (const node of this.assignedDefaultNodes) {
      const hasTextContent = node.nodeType === Node.TEXT_NODE && !!node.wholeText.match(/\S/);
      if (node.nodeType === Node.ELEMENT_NODE || hasTextContent) {
        return;
      }
    }
    this.iconOnly = true;
  }
  handleIconSlotChange() {
    this.hasIcon = this.assignedIcons.length > 0;
  }
};
__decorate([
  property({ type: Boolean, reflect: true, attribute: "md-tab" })
], Tab.prototype, "isTab", void 0);
__decorate([
  property({ type: Boolean, reflect: true })
], Tab.prototype, "active", void 0);
__decorate([
  property({ type: Boolean })
], Tab.prototype, "selected", null);
__decorate([
  property({ type: Boolean, attribute: "has-icon" })
], Tab.prototype, "hasIcon", void 0);
__decorate([
  property({ type: Boolean, attribute: "icon-only" })
], Tab.prototype, "iconOnly", void 0);
__decorate([
  query(".indicator")
], Tab.prototype, _a, void 0);
__decorate([
  state()
], Tab.prototype, "fullWidthIndicator", void 0);
__decorate([
  queryAssignedNodes({ flatten: true })
], Tab.prototype, "assignedDefaultNodes", void 0);
__decorate([
  queryAssignedElements({ slot: "icon", flatten: true })
], Tab.prototype, "assignedIcons", void 0);
function shouldReduceMotion() {
  return window.matchMedia("(prefers-reduced-motion: reduce)").matches;
}

export {
  ANIMATE_INDICATOR,
  Tab
};
/*! Bundled license information:

@material/web/tabs/internal/tab.js:
  (**
   * @license
   * Copyright 2023 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   *)
*/
//# sourceMappingURL=chunk-URATRWE7.js.map
