{"version": 3, "sources": ["../../@material/web/labs/navigationbar/internal/navigation-bar.ts", "../../@material/web/labs/navigationbar/internal/navigation-bar-styles.ts", "../../@material/web/labs/navigationbar/navigation-bar.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2022 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport '../../../elevation/elevation.js';\n\nimport {html, LitElement, nothing, PropertyValues} from 'lit';\nimport {property, queryAssignedElements} from 'lit/decorators.js';\n\nimport {ARIAMixinStrict} from '../../../internal/aria/aria.js';\nimport {mixinDelegatesAria} from '../../../internal/aria/delegate.js';\nimport {isRtl} from '../../../internal/controller/is-rtl.js';\nimport {NavigationTab} from '../../navigationtab/internal/navigation-tab.js';\n\nimport {NavigationTabInteractionEvent} from './constants.js';\nimport {NavigationBarState} from './state.js';\n\n// Separate variable needed for closure.\nconst navigationBarBaseClass = mixinDelegatesAria(LitElement);\n\n/**\n * b/265346501 - add docs\n *\n * @fires navigation-bar-activated {CustomEvent<tab: NavigationTab, activeIndex: number>}\n * Dispatched whenever the `activeIndex` changes. --bubbles --composed\n */\nexport class NavigationBar\n  extends navigationBarBaseClass\n  implements NavigationBarState\n{\n  @property({type: Number, attribute: 'active-index'}) activeIndex = 0;\n\n  @property({type: Boolean, attribute: 'hide-inactive-labels'})\n  hideInactiveLabels = false;\n\n  tabs: NavigationTab[] = [];\n\n  @queryAssignedElements({flatten: true})\n  private readonly tabsElement!: NavigationTab[];\n\n  protected override render() {\n    // Needed for closure conformance\n    const {ariaLabel} = this as ARIAMixinStrict;\n    return html`<div\n      class=\"md3-navigation-bar\"\n      role=\"tablist\"\n      aria-label=${ariaLabel || nothing}\n      @keydown=\"${this.handleKeydown}\"\n      @navigation-tab-interaction=\"${this.handleNavigationTabInteraction}\"\n      @navigation-tab-rendered=${this.handleNavigationTabConnected}\n      ><md-elevation part=\"elevation\"></md-elevation\n      ><div class=\"md3-navigation-bar__tabs-slot-container\"><slot></slot></div\n    ></div>`;\n  }\n\n  protected override updated(changedProperties: PropertyValues<NavigationBar>) {\n    if (changedProperties.has('activeIndex')) {\n      this.onActiveIndexChange(this.activeIndex);\n      this.dispatchEvent(\n        new CustomEvent('navigation-bar-activated', {\n          detail: {\n            tab: this.tabs[this.activeIndex],\n            activeIndex: this.activeIndex,\n          },\n          bubbles: true,\n          composed: true,\n        }),\n      );\n    }\n\n    if (changedProperties.has('hideInactiveLabels')) {\n      this.onHideInactiveLabelsChange(this.hideInactiveLabels);\n    }\n\n    if (changedProperties.has('tabs')) {\n      this.onHideInactiveLabelsChange(this.hideInactiveLabels);\n      this.onActiveIndexChange(this.activeIndex);\n    }\n  }\n\n  override firstUpdated(changedProperties: PropertyValues) {\n    super.firstUpdated(changedProperties);\n    this.layout();\n  }\n\n  layout() {\n    if (!this.tabsElement) return;\n    const navTabs: NavigationTab[] = [];\n    for (const node of this.tabsElement) {\n      navTabs.push(node);\n    }\n    this.tabs = navTabs;\n  }\n\n  private handleNavigationTabConnected(event: CustomEvent) {\n    const target = event.target as NavigationTab;\n    if (this.tabs.indexOf(target) === -1) {\n      this.layout();\n    }\n  }\n\n  private handleNavigationTabInteraction(event: NavigationTabInteractionEvent) {\n    this.activeIndex = this.tabs.indexOf(event.detail.state as NavigationTab);\n  }\n\n  private handleKeydown(event: KeyboardEvent) {\n    const key = event.key;\n    const focusedTabIndex = this.tabs.findIndex((tab) => {\n      return tab.matches(':focus-within');\n    });\n    const isRTL = isRtl(this);\n    const maxIndex = this.tabs.length - 1;\n\n    if (key === 'Enter' || key === ' ') {\n      this.activeIndex = focusedTabIndex;\n      return;\n    }\n\n    if (key === 'Home') {\n      this.tabs[0].focus();\n      return;\n    }\n\n    if (key === 'End') {\n      this.tabs[maxIndex].focus();\n      return;\n    }\n\n    const toNextTab =\n      (key === 'ArrowRight' && !isRTL) || (key === 'ArrowLeft' && isRTL);\n    if (toNextTab && focusedTabIndex === maxIndex) {\n      this.tabs[0].focus();\n      return;\n    }\n    if (toNextTab) {\n      this.tabs[focusedTabIndex + 1].focus();\n      return;\n    }\n\n    const toPreviousTab =\n      (key === 'ArrowLeft' && !isRTL) || (key === 'ArrowRight' && isRTL);\n    if (toPreviousTab && focusedTabIndex === 0) {\n      this.tabs[maxIndex].focus();\n      return;\n    }\n    if (toPreviousTab) {\n      this.tabs[focusedTabIndex - 1].focus();\n      return;\n    }\n  }\n\n  private onActiveIndexChange(value: number) {\n    if (!this.tabs[value]) {\n      throw new Error('NavigationBar: activeIndex is out of bounds.');\n    }\n    for (let i = 0; i < this.tabs.length; i++) {\n      this.tabs[i].active = i === value;\n    }\n  }\n\n  private onHideInactiveLabelsChange(value: boolean) {\n    for (const tab of this.tabs) {\n      tab.hideInactiveLabel = value;\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n// Generated stylesheet for ./labs/navigationbar/internal/navigation-bar-styles.css.\nimport {css} from 'lit';\nexport const styles = css`:host{--_active-indicator-color: var(--md-navigation-bar-active-indicator-color, var(--md-sys-color-secondary-container, #e8def8));--_active-indicator-height: var(--md-navigation-bar-active-indicator-height, 32px);--_active-indicator-shape: var(--md-navigation-bar-active-indicator-shape, var(--md-sys-shape-corner-full, 9999px));--_active-indicator-width: var(--md-navigation-bar-active-indicator-width, 64px);--_active-focus-icon-color: var(--md-navigation-bar-active-focus-icon-color, var(--md-sys-color-on-secondary-container, #1d192b));--_active-focus-label-text-color: var(--md-navigation-bar-active-focus-label-text-color, var(--md-sys-color-on-surface, #1d1b20));--_active-focus-state-layer-color: var(--md-navigation-bar-active-focus-state-layer-color, var(--md-sys-color-on-surface, #1d1b20));--_active-hover-icon-color: var(--md-navigation-bar-active-hover-icon-color, var(--md-sys-color-on-secondary-container, #1d192b));--_active-hover-label-text-color: var(--md-navigation-bar-active-hover-label-text-color, var(--md-sys-color-on-surface, #1d1b20));--_active-hover-state-layer-color: var(--md-navigation-bar-active-hover-state-layer-color, var(--md-sys-color-on-surface, #1d1b20));--_active-icon-color: var(--md-navigation-bar-active-icon-color, var(--md-sys-color-on-secondary-container, #1d192b));--_active-label-text-color: var(--md-navigation-bar-active-label-text-color, var(--md-sys-color-on-surface, #1d1b20));--_active-label-text-weight: var(--md-navigation-bar-active-label-text-weight, var(--md-sys-typescale-label-medium-weight-prominent, var(--md-ref-typeface-weight-bold, 700)));--_active-pressed-icon-color: var(--md-navigation-bar-active-pressed-icon-color, var(--md-sys-color-on-secondary-container, #1d192b));--_active-pressed-label-text-color: var(--md-navigation-bar-active-pressed-label-text-color, var(--md-sys-color-on-surface, #1d1b20));--_active-pressed-state-layer-color: var(--md-navigation-bar-active-pressed-state-layer-color, var(--md-sys-color-on-surface, #1d1b20));--_container-color: var(--md-navigation-bar-container-color, var(--md-sys-color-surface-container, #f3edf7));--_container-elevation: var(--md-navigation-bar-container-elevation, 2);--_container-height: var(--md-navigation-bar-container-height, 80px);--_container-shape: var(--md-navigation-bar-container-shape, var(--md-sys-shape-corner-none, 0px));--_focus-state-layer-opacity: var(--md-navigation-bar-focus-state-layer-opacity, 0.12);--_hover-state-layer-opacity: var(--md-navigation-bar-hover-state-layer-opacity, 0.08);--_icon-size: var(--md-navigation-bar-icon-size, 24px);--_inactive-focus-icon-color: var(--md-navigation-bar-inactive-focus-icon-color, var(--md-sys-color-on-surface, #1d1b20));--_inactive-focus-label-text-color: var(--md-navigation-bar-inactive-focus-label-text-color, var(--md-sys-color-on-surface, #1d1b20));--_inactive-focus-state-layer-color: var(--md-navigation-bar-inactive-focus-state-layer-color, var(--md-sys-color-on-surface, #1d1b20));--_inactive-hover-icon-color: var(--md-navigation-bar-inactive-hover-icon-color, var(--md-sys-color-on-surface, #1d1b20));--_inactive-hover-label-text-color: var(--md-navigation-bar-inactive-hover-label-text-color, var(--md-sys-color-on-surface, #1d1b20));--_inactive-hover-state-layer-color: var(--md-navigation-bar-inactive-hover-state-layer-color, var(--md-sys-color-on-surface, #1d1b20));--_inactive-icon-color: var(--md-navigation-bar-inactive-icon-color, var(--md-sys-color-on-surface-variant, #49454f));--_inactive-label-text-color: var(--md-navigation-bar-inactive-label-text-color, var(--md-sys-color-on-surface-variant, #49454f));--_inactive-pressed-icon-color: var(--md-navigation-bar-inactive-pressed-icon-color, var(--md-sys-color-on-surface, #1d1b20));--_inactive-pressed-label-text-color: var(--md-navigation-bar-inactive-pressed-label-text-color, var(--md-sys-color-on-surface, #1d1b20));--_inactive-pressed-state-layer-color: var(--md-navigation-bar-inactive-pressed-state-layer-color, var(--md-sys-color-on-surface, #1d1b20));--_label-text-font: var(--md-navigation-bar-label-text-font, var(--md-sys-typescale-label-medium-font, var(--md-ref-typeface-plain, Roboto)));--_label-text-line-height: var(--md-navigation-bar-label-text-line-height, var(--md-sys-typescale-label-medium-line-height, 1rem));--_label-text-size: var(--md-navigation-bar-label-text-size, var(--md-sys-typescale-label-medium-size, 0.75rem));--_label-text-tracking: var(--md-navigation-bar-label-text-tracking, );--_label-text-type: var(--md-navigation-bar-label-text-type, var(--md-sys-typescale-label-medium-weight, var(--md-ref-typeface-weight-medium, 500)) var(--md-sys-typescale-label-medium-size, 0.75rem) / var(--md-sys-typescale-label-medium-line-height, 1rem) var(--md-sys-typescale-label-medium-font, var(--md-ref-typeface-plain, Roboto)));--_label-text-weight: var(--md-navigation-bar-label-text-weight, var(--md-sys-typescale-label-medium-weight, var(--md-ref-typeface-weight-medium, 500)));--_pressed-state-layer-opacity: var(--md-navigation-bar-pressed-state-layer-opacity, 0.12);--md-elevation-level: var(--_container-elevation);--md-elevation-shadow-color: var(--_container-shadow-color);width:100%}.md3-navigation-bar{display:flex;position:relative;width:100%;background-color:var(--_container-color);border-radius:var(--_container-shape);height:var(--_container-height)}.md3-navigation-bar .md3-navigation-bar__tabs-slot-container{display:inherit;width:inherit}md-elevation{transition-duration:280ms;z-index:0}\n`;\n", "/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {NavigationBar} from './internal/navigation-bar.js';\nimport {styles} from './internal/navigation-bar-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-navigation-bar': MdNavigationBar;\n  }\n}\n\n/**\n * @final\n * @suppress {visibility}\n */\n@customElement('md-navigation-bar')\nexport class MdNavigationBar extends NavigationBar {\n  static override styles: CSSResultOrNative[] = [styles];\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAoBA,IAAM,yBAAyB,mBAAmB,UAAU;AAQtD,IAAO,gBAAP,cACI,uBAAsB;EADhC,cAAA;;AAIuD,SAAA,cAAc;AAGnE,SAAA,qBAAqB;AAErB,SAAA,OAAwB,CAAA;EAkI1B;EA7HqB,SAAM;AAEvB,UAAM,EAAC,UAAS,IAAI;AACpB,WAAO;;;mBAGQ,aAAa,OAAO;kBACrB,KAAK,aAAa;qCACC,KAAK,8BAA8B;iCACvC,KAAK,4BAA4B;;;;EAIhE;EAEmB,QAAQ,mBAAgD;AACzE,QAAI,kBAAkB,IAAI,aAAa,GAAG;AACxC,WAAK,oBAAoB,KAAK,WAAW;AACzC,WAAK,cACH,IAAI,YAAY,4BAA4B;QAC1C,QAAQ;UACN,KAAK,KAAK,KAAK,KAAK,WAAW;UAC/B,aAAa,KAAK;;QAEpB,SAAS;QACT,UAAU;OACX,CAAC;IAEN;AAEA,QAAI,kBAAkB,IAAI,oBAAoB,GAAG;AAC/C,WAAK,2BAA2B,KAAK,kBAAkB;IACzD;AAEA,QAAI,kBAAkB,IAAI,MAAM,GAAG;AACjC,WAAK,2BAA2B,KAAK,kBAAkB;AACvD,WAAK,oBAAoB,KAAK,WAAW;IAC3C;EACF;EAES,aAAa,mBAAiC;AACrD,UAAM,aAAa,iBAAiB;AACpC,SAAK,OAAM;EACb;EAEA,SAAM;AACJ,QAAI,CAAC,KAAK;AAAa;AACvB,UAAM,UAA2B,CAAA;AACjC,eAAW,QAAQ,KAAK,aAAa;AACnC,cAAQ,KAAK,IAAI;IACnB;AACA,SAAK,OAAO;EACd;EAEQ,6BAA6B,OAAkB;AACrD,UAAM,SAAS,MAAM;AACrB,QAAI,KAAK,KAAK,QAAQ,MAAM,MAAM,IAAI;AACpC,WAAK,OAAM;IACb;EACF;EAEQ,+BAA+B,OAAoC;AACzE,SAAK,cAAc,KAAK,KAAK,QAAQ,MAAM,OAAO,KAAsB;EAC1E;EAEQ,cAAc,OAAoB;AACxC,UAAM,MAAM,MAAM;AAClB,UAAM,kBAAkB,KAAK,KAAK,UAAU,CAAC,QAAO;AAClD,aAAO,IAAI,QAAQ,eAAe;IACpC,CAAC;AACD,UAAM,QAAQ,MAAM,IAAI;AACxB,UAAM,WAAW,KAAK,KAAK,SAAS;AAEpC,QAAI,QAAQ,WAAW,QAAQ,KAAK;AAClC,WAAK,cAAc;AACnB;IACF;AAEA,QAAI,QAAQ,QAAQ;AAClB,WAAK,KAAK,CAAC,EAAE,MAAK;AAClB;IACF;AAEA,QAAI,QAAQ,OAAO;AACjB,WAAK,KAAK,QAAQ,EAAE,MAAK;AACzB;IACF;AAEA,UAAM,YACH,QAAQ,gBAAgB,CAAC,SAAW,QAAQ,eAAe;AAC9D,QAAI,aAAa,oBAAoB,UAAU;AAC7C,WAAK,KAAK,CAAC,EAAE,MAAK;AAClB;IACF;AACA,QAAI,WAAW;AACb,WAAK,KAAK,kBAAkB,CAAC,EAAE,MAAK;AACpC;IACF;AAEA,UAAM,gBACH,QAAQ,eAAe,CAAC,SAAW,QAAQ,gBAAgB;AAC9D,QAAI,iBAAiB,oBAAoB,GAAG;AAC1C,WAAK,KAAK,QAAQ,EAAE,MAAK;AACzB;IACF;AACA,QAAI,eAAe;AACjB,WAAK,KAAK,kBAAkB,CAAC,EAAE,MAAK;AACpC;IACF;EACF;EAEQ,oBAAoB,OAAa;AACvC,QAAI,CAAC,KAAK,KAAK,KAAK,GAAG;AACrB,YAAM,IAAI,MAAM,8CAA8C;IAChE;AACA,aAAS,IAAI,GAAG,IAAI,KAAK,KAAK,QAAQ,KAAK;AACzC,WAAK,KAAK,CAAC,EAAE,SAAS,MAAM;IAC9B;EACF;EAEQ,2BAA2B,OAAc;AAC/C,eAAW,OAAO,KAAK,MAAM;AAC3B,UAAI,oBAAoB;IAC1B;EACF;;AAtIqD,WAAA;EAApD,SAAS,EAAC,MAAM,QAAQ,WAAW,eAAc,CAAC;;AAGnD,WAAA;EADC,SAAS,EAAC,MAAM,SAAS,WAAW,uBAAsB,CAAC;;AAM3C,WAAA;EADhB,sBAAsB,EAAC,SAAS,KAAI,CAAC;;;;AChCjC,IAAM,SAAS;;;;ACgBf,IAAM,kBAAN,MAAMA,yBAAwB,cAAa;;AAChC,gBAAA,SAA8B,CAAC,MAAM;AAD1C,kBAAe,WAAA;EAD3B,cAAc,mBAAmB;GACrB,eAAe;", "names": ["MdNavigationBar"]}