{"version": 3, "sources": ["../../@material/web/menu/internal/menuitem/menu-item.ts", "../../@material/web/menu/menu-item.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2022 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport '../../../focus/md-focus-ring.js';\nimport '../../../labs/item/item.js';\nimport '../../../ripple/ripple.js';\n\nimport {html, LitElement, nothing, TemplateResult} from 'lit';\nimport {\n  property,\n  query,\n  queryAssignedElements,\n  queryAssignedNodes,\n} from 'lit/decorators.js';\nimport {ClassInfo, classMap} from 'lit/directives/class-map.js';\nimport {literal, html as staticHtml, StaticValue} from 'lit/static-html.js';\n\nimport {ARIAMixinStrict} from '../../../internal/aria/aria.js';\nimport {mixinDelegatesAria} from '../../../internal/aria/delegate.js';\nimport {\n  MenuItem,\n  MenuItemController,\n  type MenuItemType,\n} from '../controllers/menuItemController.js';\n\n// Separate variable needed for closure.\nconst menuItemBaseClass = mixinDelegatesAria(LitElement);\n\n/**\n * @fires close-menu {CustomEvent<{initiator: SelectOption, reason: Reason, itemPath: SelectOption[]}>}\n * Closes the encapsulating menu on closable interaction. --bubbles --composed\n */\nexport class MenuItemEl extends menuItemBaseClass implements MenuItem {\n  /** @nocollapse */\n  static override shadowRootOptions = {\n    ...LitElement.shadowRootOptions,\n    delegatesFocus: true,\n  };\n\n  /**\n   * Disables the item and makes it non-selectable and non-interactive.\n   */\n  @property({type: Boolean, reflect: true}) disabled = false;\n\n  /**\n   * Sets the behavior and role of the menu item, defaults to \"menuitem\".\n   */\n  @property() type: MenuItemType = 'menuitem';\n\n  /**\n   * Sets the underlying `HTMLAnchorElement`'s `href` resource attribute.\n   */\n  @property() href = '';\n\n  /**\n   * Sets the underlying `HTMLAnchorElement`'s `target` attribute when `href` is\n   * set.\n   */\n  @property() target: '_blank' | '_parent' | '_self' | '_top' | '' = '';\n\n  /**\n   * Keeps the menu open if clicked or keyboard selected.\n   */\n  @property({type: Boolean, attribute: 'keep-open'}) keepOpen = false;\n\n  /**\n   * Sets the item in the selected visual state when a submenu is opened.\n   */\n  @property({type: Boolean}) selected = false;\n\n  @query('.list-item') protected readonly listItemRoot!: HTMLElement | null;\n\n  @queryAssignedElements({slot: 'headline'})\n  protected readonly headlineElements!: HTMLElement[];\n  @queryAssignedElements({slot: 'supporting-text'})\n  protected readonly supportingTextElements!: HTMLElement[];\n  @queryAssignedNodes({slot: ''})\n  protected readonly defaultElements!: Node[];\n\n  /**\n   * The text that is selectable via typeahead. If not set, defaults to the\n   * innerText of the item slotted into the `\"headline\"` slot.\n   */\n  get typeaheadText() {\n    return this.menuItemController.typeaheadText;\n  }\n\n  @property({attribute: 'typeahead-text'})\n  set typeaheadText(text: string) {\n    this.menuItemController.setTypeaheadText(text);\n  }\n\n  private readonly menuItemController = new MenuItemController(this, {\n    getHeadlineElements: () => {\n      return this.headlineElements;\n    },\n    getSupportingTextElements: () => {\n      return this.supportingTextElements;\n    },\n    getDefaultElements: () => {\n      return this.defaultElements;\n    },\n    getInteractiveElement: () => this.listItemRoot,\n  });\n\n  protected override render() {\n    return this.renderListItem(html`\n      <md-item>\n        <div slot=\"container\">\n          ${this.renderRipple()} ${this.renderFocusRing()}\n        </div>\n        <slot name=\"start\" slot=\"start\"></slot>\n        <slot name=\"end\" slot=\"end\"></slot>\n        ${this.renderBody()}\n      </md-item>\n    `);\n  }\n\n  /**\n   * Renders the root list item.\n   *\n   * @param content the child content of the list item.\n   */\n  protected renderListItem(content: unknown) {\n    const isAnchor = this.type === 'link';\n    let tag: StaticValue;\n    switch (this.menuItemController.tagName) {\n      case 'a':\n        tag = literal`a`;\n        break;\n      case 'button':\n        tag = literal`button`;\n        break;\n      default:\n      case 'li':\n        tag = literal`li`;\n        break;\n    }\n\n    // TODO(b/265339866): announce \"button\"/\"link\" inside of a list item. Until\n    // then all are \"menuitem\" roles for correct announcement.\n    const target = isAnchor && !!this.target ? this.target : nothing;\n    return staticHtml`\n      <${tag}\n        id=\"item\"\n        tabindex=${this.disabled && !isAnchor ? -1 : 0}\n        role=${this.menuItemController.role}\n        aria-label=${(this as ARIAMixinStrict).ariaLabel || nothing}\n        aria-selected=${(this as ARIAMixinStrict).ariaSelected || nothing}\n        aria-checked=${(this as ARIAMixinStrict).ariaChecked || nothing}\n        aria-expanded=${(this as ARIAMixinStrict).ariaExpanded || nothing}\n        aria-haspopup=${(this as ARIAMixinStrict).ariaHasPopup || nothing}\n        class=\"list-item ${classMap(this.getRenderClasses())}\"\n        href=${this.href || nothing}\n        target=${target}\n        @click=${this.menuItemController.onClick}\n        @keydown=${this.menuItemController.onKeydown}\n      >${content}</${tag}>\n    `;\n  }\n\n  /**\n   * Handles rendering of the ripple element.\n   */\n  protected renderRipple(): TemplateResult | typeof nothing {\n    return html` <md-ripple\n      part=\"ripple\"\n      for=\"item\"\n      ?disabled=${this.disabled}></md-ripple>`;\n  }\n\n  /**\n   * Handles rendering of the focus ring.\n   */\n  protected renderFocusRing(): TemplateResult | typeof nothing {\n    return html` <md-focus-ring\n      part=\"focus-ring\"\n      for=\"item\"\n      inward></md-focus-ring>`;\n  }\n\n  /**\n   * Classes applied to the list item root.\n   */\n  protected getRenderClasses(): ClassInfo {\n    return {\n      'disabled': this.disabled,\n      'selected': this.selected,\n    };\n  }\n\n  /**\n   * Handles rendering the headline and supporting text.\n   */\n  protected renderBody() {\n    return html`\n      <slot></slot>\n      <slot name=\"overline\" slot=\"overline\"></slot>\n      <slot name=\"headline\" slot=\"headline\"></slot>\n      <slot name=\"supporting-text\" slot=\"supporting-text\"></slot>\n      <slot\n        name=\"trailing-supporting-text\"\n        slot=\"trailing-supporting-text\"></slot>\n    `;\n  }\n\n  override focus() {\n    // TODO(b/300334509): needed for some cases where delegatesFocus doesn't\n    // work programmatically like in FF and select-option\n    this.listItemRoot?.focus();\n  }\n}\n", "/**\n * @license\n * Copyright 2022 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {MenuItemEl} from './internal/menuitem/menu-item.js';\nimport {styles} from './internal/menuitem/menu-item-styles.js';\n\nexport {type MenuItem} from './internal/controllers/menuItemController.js';\nexport {type CloseMenuEvent} from './internal/controllers/shared.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-menu-item': MdMenuItem;\n  }\n}\n\n/**\n * @summary Menus display a list of choices on a temporary surface.\n *\n * @description\n * Menu items are the selectable choices within the menu. Menu items must\n * implement the `MenuItem` interface and also have the `md-menu-item`\n * attribute. Additionally menu items are list items so they must also have the\n * `md-list-item` attribute.\n *\n * Menu items can control a menu by selectively firing the `close-menu` and\n * `deselect-items` events.\n *\n * @final\n * @suppress {visibility}\n */\n@customElement('md-menu-item')\nexport class MdMenuItem extends MenuItemEl {\n  static override styles: CSSResultOrNative[] = [styles];\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BA,IAAM,oBAAoB,mBAAmB,UAAU;AAMjD,IAAO,aAAP,cAA0B,kBAAiB;EAAjD,cAAA;;AAU4C,SAAA,WAAW;AAKzC,SAAA,OAAqB;AAKrB,SAAA,OAAO;AAMP,SAAA,SAAuD;AAKhB,SAAA,WAAW;AAKnC,SAAA,WAAW;AAwBrB,SAAA,qBAAqB,IAAI,mBAAmB,MAAM;MACjE,qBAAqB,MAAK;AACxB,eAAO,KAAK;MACd;MACA,2BAA2B,MAAK;AAC9B,eAAO,KAAK;MACd;MACA,oBAAoB,MAAK;AACvB,eAAO,KAAK;MACd;MACA,uBAAuB,MAAM,KAAK;KACnC;EA4GH;;;;;EAhIE,IAAI,gBAAa;AACf,WAAO,KAAK,mBAAmB;EACjC;EAGA,IAAI,cAAc,MAAY;AAC5B,SAAK,mBAAmB,iBAAiB,IAAI;EAC/C;EAemB,SAAM;AACvB,WAAO,KAAK,eAAe;;;YAGnB,KAAK,aAAY,CAAE,IAAI,KAAK,gBAAe,CAAE;;;;UAI/C,KAAK,WAAU,CAAE;;KAEtB;EACH;;;;;;EAOU,eAAe,SAAgB;AACvC,UAAM,WAAW,KAAK,SAAS;AAC/B,QAAI;AACJ,YAAQ,KAAK,mBAAmB,SAAS;MACvC,KAAK;AACH,cAAM;AACN;MACF,KAAK;AACH,cAAM;AACN;MACF;MACA,KAAK;AACH,cAAM;AACN;IACJ;AAIA,UAAM,SAAS,YAAY,CAAC,CAAC,KAAK,SAAS,KAAK,SAAS;AACzD,WAAOA;SACF,GAAG;;mBAEO,KAAK,YAAY,CAAC,WAAW,KAAK,CAAC;eACvC,KAAK,mBAAmB,IAAI;qBACrB,KAAyB,aAAa,OAAO;wBAC1C,KAAyB,gBAAgB,OAAO;uBACjD,KAAyB,eAAe,OAAO;wBAC9C,KAAyB,gBAAgB,OAAO;wBAChD,KAAyB,gBAAgB,OAAO;2BAC9C,SAAS,KAAK,iBAAgB,CAAE,CAAC;eAC7C,KAAK,QAAQ,OAAO;iBAClB,MAAM;iBACN,KAAK,mBAAmB,OAAO;mBAC7B,KAAK,mBAAmB,SAAS;SAC3C,OAAO,KAAK,GAAG;;EAEtB;;;;EAKU,eAAY;AACpB,WAAO;;;kBAGO,KAAK,QAAQ;EAC7B;;;;EAKU,kBAAe;AACvB,WAAO;;;;EAIT;;;;EAKU,mBAAgB;AACxB,WAAO;MACL,YAAY,KAAK;MACjB,YAAY,KAAK;;EAErB;;;;EAKU,aAAU;AAClB,WAAO;;;;;;;;;EAST;EAES,QAAK;AAGZ,SAAK,cAAc,MAAK;EAC1B;;AAhLgB,WAAA,oBAAoB;EAClC,GAAG,WAAW;EACd,gBAAgB;;AAMwB,WAAA;EAAzC,SAAS,EAAC,MAAM,SAAS,SAAS,KAAI,CAAC;;AAK5B,WAAA;EAAX,SAAQ;;AAKG,WAAA;EAAX,SAAQ;;AAMG,WAAA;EAAX,SAAQ;;AAK0C,WAAA;EAAlD,SAAS,EAAC,MAAM,SAAS,WAAW,YAAW,CAAC;;AAKtB,WAAA;EAA1B,SAAS,EAAC,MAAM,QAAO,CAAC;;AAEe,WAAA;EAAvC,MAAM,YAAY;;AAGA,WAAA;EADlB,sBAAsB,EAAC,MAAM,WAAU,CAAC;;AAGtB,WAAA;EADlB,sBAAsB,EAAC,MAAM,kBAAiB,CAAC;;AAG7B,WAAA;EADlB,mBAAmB,EAAC,MAAM,GAAE,CAAC;;AAY9B,WAAA;EADC,SAAS,EAAC,WAAW,iBAAgB,CAAC;;;;ACrDlC,IAAM,aAAN,MAAMC,oBAAmB,WAAU;;AACxB,WAAA,SAA8B,CAAC,MAAM;AAD1C,aAAU,WAAA;EADtB,cAAc,cAAc;GAChB,UAAU;", "names": ["html", "MdMenuItem"]}