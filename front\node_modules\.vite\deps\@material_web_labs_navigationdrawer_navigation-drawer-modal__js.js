import {
  styles
} from "./chunk-ZP5NZYYW.js";
import {
  mixinDelegatesAria
} from "./chunk-GXE4MBY5.js";
import {
  classMap
} from "./chunk-SZQCPKZF.js";
import {
  __decorate,
  customElement,
  property
} from "./chunk-PZNDE6JX.js";
import {
  LitElement,
  css,
  html,
  nothing
} from "./chunk-4GZ3EDRH.js";
import "./chunk-5WRI5ZAA.js";

// node_modules/@material/web/labs/navigationdrawer/internal/navigation-drawer-modal.js
var navigationDrawerModalBaseClass = mixinDelegatesAria(LitElement);
var NavigationDrawerModal = class extends navigationDrawerModalBaseClass {
  constructor() {
    super(...arguments);
    this.opened = false;
    this.pivot = "end";
  }
  render() {
    const ariaExpanded = this.opened ? "true" : "false";
    const ariaHidden = !this.opened ? "true" : "false";
    const { ariaLabel, ariaModal } = this;
    return html`
      <div
        class="md3-navigation-drawer-modal__scrim ${this.getScrimClasses()}"
        @click="${this.handleScrimClick}">
      </div>
      <div
        aria-expanded=${ariaExpanded}
        aria-hidden=${ariaHidden}
        aria-label=${ariaLabel || nothing}
        aria-modal=${ariaModal || nothing}
        class="md3-navigation-drawer-modal ${this.getRenderClasses()}"
        @keydown="${this.handleKeyDown}"
        role="dialog"
        ><div class="md3-elevation-overlay"></div>
        <div class="md3-navigation-drawer-modal__slot-content">
          <slot></slot>
        </div>
      </div>
    `;
  }
  getScrimClasses() {
    return classMap({
      "md3-navigation-drawer-modal--scrim-visible": this.opened
    });
  }
  getRenderClasses() {
    return classMap({
      "md3-navigation-drawer-modal--opened": this.opened,
      "md3-navigation-drawer-modal--pivot-at-start": this.pivot === "start"
    });
  }
  updated(changedProperties) {
    if (changedProperties.has("opened")) {
      setTimeout(() => {
        this.dispatchEvent(new CustomEvent("navigation-drawer-changed", {
          detail: { opened: this.opened },
          bubbles: true,
          composed: true
        }));
      }, 250);
    }
  }
  handleKeyDown(event) {
    if (event.code === "Escape") {
      this.opened = false;
    }
  }
  handleScrimClick() {
    this.opened = !this.opened;
  }
};
__decorate([
  property({ type: Boolean })
], NavigationDrawerModal.prototype, "opened", void 0);
__decorate([
  property()
], NavigationDrawerModal.prototype, "pivot", void 0);

// node_modules/@material/web/labs/navigationdrawer/internal/navigation-drawer-modal-styles.js
var styles2 = css`:host{--_container-color: var(--md-navigation-drawer-modal-container-color, #fff);--_container-height: var(--md-navigation-drawer-modal-container-height, 100%);--_container-shape: var(--md-navigation-drawer-modal-container-shape, 0 16px 16px 0);--_container-width: var(--md-navigation-drawer-modal-container-width, 360px);--_divider-color: var(--md-navigation-drawer-modal-divider-color, #000);--_modal-container-elevation: var(--md-navigation-drawer-modal-modal-container-elevation, 1);--_scrim-color: var(--md-navigation-drawer-modal-scrim-color, );--_scrim-opacity: var(--md-navigation-drawer-modal-scrim-opacity, 0.04);--_standard-container-elevation: var(--md-navigation-drawer-modal-standard-container-elevation, 0);--md-elevation-level: var(--_modal-container-elevation)}.md3-navigation-drawer-modal{bottom:0;box-sizing:border-box;display:flex;justify-content:flex-end;overflow:hidden;position:absolute;top:0;inline-size:0;transition:inline-size .25s cubic-bezier(0.4, 0, 0.2, 1) 0s,visibility 0s cubic-bezier(0.4, 0, 0.2, 1) .25s}.md3-navigation-drawer-modal--opened{transition:inline-size .25s cubic-bezier(0.4, 0, 0.2, 1) 0s,visibility 0s cubic-bezier(0.4, 0, 0.2, 1) 0s}.md3-navigation-drawer-modal--pivot-at-start{justify-content:flex-start}.md3-navigation-drawer-modal__slot-content{display:flex;flex-direction:column;position:relative}.md3-navigation-drawer-modal__scrim{inset:0;opacity:0;position:absolute;visibility:hidden;background-color:var(--_scrim-color);transition:opacity .25s cubic-bezier(0.4, 0, 0.2, 1) 0s,visibility 0s cubic-bezier(0.4, 0, 0.2, 1) .25s}.md3-navigation-drawer-modal--scrim-visible{visibility:visible;opacity:var(--_scrim-opacity);transition:opacity .25s cubic-bezier(0.4, 0, 0.2, 1) 0s,visibility 0s cubic-bezier(0.4, 0, 0.2, 1) 0s}
`;

// node_modules/@material/web/labs/navigationdrawer/navigation-drawer-modal.js
var MdNavigationDrawerModal = class MdNavigationDrawerModal2 extends NavigationDrawerModal {
};
MdNavigationDrawerModal.styles = [styles, styles2];
MdNavigationDrawerModal = __decorate([
  customElement("md-navigation-drawer-modal")
], MdNavigationDrawerModal);
export {
  MdNavigationDrawerModal
};
/*! Bundled license information:

@material/web/labs/navigationdrawer/internal/navigation-drawer-modal.js:
@material/web/labs/navigationdrawer/navigation-drawer-modal.js:
  (**
   * @license
   * Copyright 2021 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   *)

@material/web/labs/navigationdrawer/internal/navigation-drawer-modal-styles.js:
  (**
   * @license
   * Copyright 2024 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   *)
*/
//# sourceMappingURL=@material_web_labs_navigationdrawer_navigation-drawer-modal__js.js.map
