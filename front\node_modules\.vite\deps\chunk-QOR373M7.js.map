{"version": 3, "sources": ["../../@material/web/textfield/internal/shared-styles.ts", "../../lit-html/src/directive-helpers.ts", "../../lit-html/src/directives/live.ts", "../../@material/web/internal/controller/string-converter.ts", "../../@material/web/labs/behaviors/validators/text-field-validator.ts", "../../@material/web/textfield/internal/text-field.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2024 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n// Generated stylesheet for ./textfield/internal/shared-styles.css.\nimport {css} from 'lit';\nexport const styles = css`:host{display:inline-flex;outline:none;resize:both;text-align:start;-webkit-tap-highlight-color:rgba(0,0,0,0)}.text-field,.field{width:100%}.text-field{display:inline-flex}.field{cursor:text}.disabled .field{cursor:default}.text-field,.textarea .field{resize:inherit}slot[name=container]{border-radius:inherit}.icon{color:currentColor;display:flex;align-items:center;justify-content:center;fill:currentColor;position:relative}.icon ::slotted(*){display:flex;position:absolute}[has-start] .icon.leading{font-size:var(--_leading-icon-size);height:var(--_leading-icon-size);width:var(--_leading-icon-size)}[has-end] .icon.trailing{font-size:var(--_trailing-icon-size);height:var(--_trailing-icon-size);width:var(--_trailing-icon-size)}.input-wrapper{display:flex}.input-wrapper>*{all:inherit;padding:0}.input{caret-color:var(--_caret-color);overflow-x:hidden;text-align:inherit}.input::placeholder{color:currentColor;opacity:1}.input::-webkit-calendar-picker-indicator{display:none}.input::-webkit-search-decoration,.input::-webkit-search-cancel-button{display:none}@media(forced-colors: active){.input{background:none}}.no-spinner .input::-webkit-inner-spin-button,.no-spinner .input::-webkit-outer-spin-button{display:none}.no-spinner .input[type=number]{-moz-appearance:textfield}:focus-within .input{caret-color:var(--_focus-caret-color)}.error:focus-within .input{caret-color:var(--_error-focus-caret-color)}.text-field:not(.disabled) .prefix{color:var(--_input-text-prefix-color)}.text-field:not(.disabled) .suffix{color:var(--_input-text-suffix-color)}.text-field:not(.disabled) .input::placeholder{color:var(--_input-text-placeholder-color)}.prefix,.suffix{text-wrap:nowrap;width:min-content}.prefix{padding-inline-end:var(--_input-text-prefix-trailing-space)}.suffix{padding-inline-start:var(--_input-text-suffix-leading-space)}\n`;\n", "/**\n * @license\n * Copyright 2020 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {\n  _$LH,\n  Part,\n  DirectiveParent,\n  CompiledTemplateResult,\n  MaybeCompiledTemplateResult,\n  UncompiledTemplateResult,\n} from './lit-html.js';\nimport {\n  DirectiveResult,\n  DirectiveClass,\n  PartInfo,\n  AttributePartInfo,\n} from './directive.js';\ntype Primitive = null | undefined | boolean | number | string | symbol | bigint;\n\nconst {_ChildPart: ChildPart} = _$LH;\n\ntype ChildPart = InstanceType<typeof ChildPart>;\n\nconst ENABLE_SHADYDOM_NOPATCH = true;\n\nconst wrap =\n  ENABLE_SHADYDOM_NOPATCH &&\n  window.ShadyDOM?.inUse &&\n  window.ShadyDOM?.noPatch === true\n    ? window.ShadyDOM!.wrap\n    : (node: Node) => node;\n\n/**\n * Tests if a value is a primitive value.\n *\n * See https://tc39.github.io/ecma262/#sec-typeof-operator\n */\nexport const isPrimitive = (value: unknown): value is Primitive =>\n  value === null || (typeof value != 'object' && typeof value != 'function');\n\nexport const TemplateResultType = {\n  HTML: 1,\n  SVG: 2,\n  MATHML: 3,\n} as const;\n\nexport type TemplateResultType =\n  (typeof TemplateResultType)[keyof typeof TemplateResultType];\n\ntype IsTemplateResult = {\n  (val: unknown): val is MaybeCompiledTemplateResult;\n  <T extends TemplateResultType>(\n    val: unknown,\n    type: T\n  ): val is UncompiledTemplateResult<T>;\n};\n\n/**\n * Tests if a value is a TemplateResult or a CompiledTemplateResult.\n */\nexport const isTemplateResult: IsTemplateResult = (\n  value: unknown,\n  type?: TemplateResultType\n): value is UncompiledTemplateResult =>\n  type === undefined\n    ? // This property needs to remain unminified.\n      (value as UncompiledTemplateResult)?.['_$litType$'] !== undefined\n    : (value as UncompiledTemplateResult)?.['_$litType$'] === type;\n\n/**\n * Tests if a value is a CompiledTemplateResult.\n */\nexport const isCompiledTemplateResult = (\n  value: unknown\n): value is CompiledTemplateResult => {\n  return (value as CompiledTemplateResult)?.['_$litType$']?.h != null;\n};\n\n/**\n * Tests if a value is a DirectiveResult.\n */\nexport const isDirectiveResult = (value: unknown): value is DirectiveResult =>\n  // This property needs to remain unminified.\n  (value as DirectiveResult)?.['_$litDirective$'] !== undefined;\n\n/**\n * Retrieves the Directive class for a DirectiveResult\n */\nexport const getDirectiveClass = (value: unknown): DirectiveClass | undefined =>\n  // This property needs to remain unminified.\n  (value as DirectiveResult)?.['_$litDirective$'];\n\n/**\n * Tests whether a part has only a single-expression with no strings to\n * interpolate between.\n *\n * Only AttributePart and PropertyPart can have multiple expressions.\n * Multi-expression parts have a `strings` property and single-expression\n * parts do not.\n */\nexport const isSingleExpression = (part: PartInfo) =>\n  (part as AttributePartInfo).strings === undefined;\n\nconst createMarker = () => document.createComment('');\n\n/**\n * Inserts a ChildPart into the given container ChildPart's DOM, either at the\n * end of the container ChildPart, or before the optional `refPart`.\n *\n * This does not add the part to the containerPart's committed value. That must\n * be done by callers.\n *\n * @param containerPart Part within which to add the new ChildPart\n * @param refPart Part before which to add the new ChildPart; when omitted the\n *     part added to the end of the `containerPart`\n * @param part Part to insert, or undefined to create a new part\n */\nexport const insertPart = (\n  containerPart: ChildPart,\n  refPart?: ChildPart,\n  part?: ChildPart\n): ChildPart => {\n  const container = wrap(containerPart._$startNode).parentNode!;\n\n  const refNode =\n    refPart === undefined ? containerPart._$endNode : refPart._$startNode;\n\n  if (part === undefined) {\n    const startNode = wrap(container).insertBefore(createMarker(), refNode);\n    const endNode = wrap(container).insertBefore(createMarker(), refNode);\n    part = new ChildPart(\n      startNode,\n      endNode,\n      containerPart,\n      containerPart.options\n    );\n  } else {\n    const endNode = wrap(part._$endNode!).nextSibling;\n    const oldParent = part._$parent;\n    const parentChanged = oldParent !== containerPart;\n    if (parentChanged) {\n      part._$reparentDisconnectables?.(containerPart);\n      // Note that although `_$reparentDisconnectables` updates the part's\n      // `_$parent` reference after unlinking from its current parent, that\n      // method only exists if Disconnectables are present, so we need to\n      // unconditionally set it here\n      part._$parent = containerPart;\n      // Since the _$isConnected getter is somewhat costly, only\n      // read it once we know the subtree has directives that need\n      // to be notified\n      let newConnectionState;\n      if (\n        part._$notifyConnectionChanged !== undefined &&\n        (newConnectionState = containerPart._$isConnected) !==\n          oldParent!._$isConnected\n      ) {\n        part._$notifyConnectionChanged(newConnectionState);\n      }\n    }\n    if (endNode !== refNode || parentChanged) {\n      let start: Node | null = part._$startNode;\n      while (start !== endNode) {\n        const n: Node | null = wrap(start!).nextSibling;\n        wrap(container).insertBefore(start!, refNode);\n        start = n;\n      }\n    }\n  }\n\n  return part;\n};\n\n/**\n * Sets the value of a Part.\n *\n * Note that this should only be used to set/update the value of user-created\n * parts (i.e. those created using `insertPart`); it should not be used\n * by directives to set the value of the directive's container part. Directives\n * should return a value from `update`/`render` to update their part state.\n *\n * For directives that require setting their part value asynchronously, they\n * should extend `AsyncDirective` and call `this.setValue()`.\n *\n * @param part Part to set\n * @param value Value to set\n * @param index For `AttributePart`s, the index to set\n * @param directiveParent Used internally; should not be set by user\n */\nexport const setChildPartValue = <T extends ChildPart>(\n  part: T,\n  value: unknown,\n  directiveParent: DirectiveParent = part\n): T => {\n  part._$setValue(value, directiveParent);\n  return part;\n};\n\n// A sentinel value that can never appear as a part value except when set by\n// live(). Used to force a dirty-check to fail and cause a re-render.\nconst RESET_VALUE = {};\n\n/**\n * Sets the committed value of a ChildPart directly without triggering the\n * commit stage of the part.\n *\n * This is useful in cases where a directive needs to update the part such\n * that the next update detects a value change or not. When value is omitted,\n * the next update will be guaranteed to be detected as a change.\n *\n * @param part\n * @param value\n */\nexport const setCommittedValue = (part: Part, value: unknown = RESET_VALUE) =>\n  (part._$committedValue = value);\n\n/**\n * Returns the committed value of a ChildPart.\n *\n * The committed value is used for change detection and efficient updates of\n * the part. It can differ from the value set by the template or directive in\n * cases where the template value is transformed before being committed.\n *\n * - `TemplateResult`s are committed as a `TemplateInstance`\n * - Iterables are committed as `Array<ChildPart>`\n * - All other types are committed as the template value or value returned or\n *   set by a directive.\n *\n * @param part\n */\nexport const getCommittedValue = (part: ChildPart) => part._$committedValue;\n\n/**\n * Removes a ChildPart from the DOM, including any of its content and markers.\n *\n * Note: The only difference between this and clearPart() is that this also\n * removes the part's start node. This means that the ChildPart must own its\n * start node, ie it must be a marker node specifically for this part and not an\n * anchor from surrounding content.\n *\n * @param part The Part to remove\n */\nexport const removePart = (part: ChildPart) => {\n  part._$clear();\n  part._$startNode.remove();\n};\n\nexport const clearPart = (part: ChildPart) => {\n  part._$clear();\n};\n", "/**\n * @license\n * Copyright 2020 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {AttributePart, noChange, nothing} from '../lit-html.js';\nimport {\n  directive,\n  Directive,\n  DirectiveParameters,\n  PartInfo,\n  PartType,\n} from '../directive.js';\nimport {isSingleExpression, setCommittedValue} from '../directive-helpers.js';\n\nclass LiveDirective extends Directive {\n  constructor(partInfo: PartInfo) {\n    super(partInfo);\n    if (\n      !(\n        partInfo.type === PartType.PROPERTY ||\n        partInfo.type === PartType.ATTRIBUTE ||\n        partInfo.type === PartType.BOOLEAN_ATTRIBUTE\n      )\n    ) {\n      throw new Error(\n        'The `live` directive is not allowed on child or event bindings'\n      );\n    }\n    if (!isSingleExpression(partInfo)) {\n      throw new Error('`live` bindings can only contain a single expression');\n    }\n  }\n\n  render(value: unknown) {\n    return value;\n  }\n\n  override update(part: AttributePart, [value]: DirectiveParameters<this>) {\n    if (value === noChange || value === nothing) {\n      return value;\n    }\n    const element = part.element;\n    const name = part.name;\n\n    if (part.type === PartType.PROPERTY) {\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      if (value === (element as any)[name]) {\n        return noChange;\n      }\n    } else if (part.type === PartType.BOOLEAN_ATTRIBUTE) {\n      if (!!value === element.hasAttribute(name)) {\n        return noChange;\n      }\n    } else if (part.type === PartType.ATTRIBUTE) {\n      if (element.getAttribute(name) === String(value)) {\n        return noChange;\n      }\n    }\n    // Resets the part's value, causing its dirty-check to fail so that it\n    // always sets the value.\n    setCommittedValue(part);\n    return value;\n  }\n}\n\n/**\n * Checks binding values against live DOM values, instead of previously bound\n * values, when determining whether to update the value.\n *\n * This is useful for cases where the DOM value may change from outside of\n * lit-html, such as with a binding to an `<input>` element's `value` property,\n * a content editable elements text, or to a custom element that changes it's\n * own properties or attributes.\n *\n * In these cases if the DOM value changes, but the value set through lit-html\n * bindings hasn't, lit-html won't know to update the DOM value and will leave\n * it alone. If this is not what you want--if you want to overwrite the DOM\n * value with the bound value no matter what--use the `live()` directive:\n *\n * ```js\n * html`<input .value=${live(x)}>`\n * ```\n *\n * `live()` performs a strict equality check against the live DOM value, and if\n * the new value is equal to the live value, does nothing. This means that\n * `live()` should not be used when the binding will cause a type conversion. If\n * you use `live()` with an attribute binding, make sure that only strings are\n * passed in, or the binding will update every render.\n */\nexport const live = directive(LiveDirective);\n\n/**\n * The type of the class that powers this directive. Necessary for naming the\n * directive's return type.\n */\nexport type {LiveDirective};\n", "/**\n * @license\n * Copyright 2022 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nexport const stringConverter = {\n  fromAttribute(value: string | null): string {\n    return value ?? '';\n  },\n  toAttribute(value: string): string | null {\n    return value || null;\n  },\n};\n", "/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {Validator} from './validator.js';\n\n/**\n * Constraint validation for a text field.\n */\nexport interface TextFieldState {\n  /**\n   * The input or textarea state to validate.\n   */\n  state: InputState | TextAreaState;\n\n  /**\n   * The `<input>` or `<textarea>` that is rendered on the page.\n   *\n   * `minlength` and `maxlength` validation do not apply until a user has\n   * interacted with the control and the element is internally marked as dirty.\n   * This is a spec quirk, the two properties behave differently from other\n   * constraint validation.\n   *\n   * This means we need an actual rendered element instead of a virtual one,\n   * since the virtual element will never be marked as dirty.\n   *\n   * This can be `null` if the element has not yet rendered, and the validator\n   * will fall back to virtual elements for other constraint validation\n   * properties, which do apply even if the control is not dirty.\n   */\n  renderedControl: HTMLInputElement | HTMLTextAreaElement | null;\n}\n\n/**\n * Constraint validation properties for an `<input>`.\n */\nexport interface InputState extends SharedInputAndTextAreaState {\n  /**\n   * The `<input>` type.\n   *\n   * Not all constraint validation properties apply to every type. See\n   * https://developer.mozilla.org/en-US/docs/Web/HTML/Constraint_validation#validation-related_attributes\n   * for which properties will apply to which types.\n   */\n  readonly type: string;\n\n  /**\n   * The regex pattern a value must match.\n   */\n  readonly pattern: string;\n\n  /**\n   * The minimum value.\n   */\n  readonly min: string;\n\n  /**\n   * The maximum value.\n   */\n  readonly max: string;\n\n  /**\n   * The step interval of the value.\n   */\n  readonly step: string;\n}\n\n/**\n * Constraint validation properties for a `<textarea>`.\n */\nexport interface TextAreaState extends SharedInputAndTextAreaState {\n  /**\n   * The type, must be \"textarea\" to inform the validator to use `<textarea>`\n   * instead of `<input>`.\n   */\n  readonly type: 'textarea';\n}\n\n/**\n * Constraint validation properties shared between an `<input>` and\n * `<textarea>`.\n */\ninterface SharedInputAndTextAreaState {\n  /**\n   * The current value.\n   */\n  readonly value: string;\n\n  /**\n   * Whether the textarea is required.\n   */\n  readonly required: boolean;\n\n  /**\n   * The minimum length of the value.\n   */\n  readonly minLength: number;\n\n  /**\n   * The maximum length of the value.\n   */\n  readonly maxLength: number;\n}\n\n/**\n * A validator that provides constraint validation that emulates `<input>` and\n * `<textarea>` validation.\n */\nexport class TextFieldValidator extends Validator<TextFieldState> {\n  private inputControl?: HTMLInputElement;\n  private textAreaControl?: HTMLTextAreaElement;\n\n  protected override computeValidity({state, renderedControl}: TextFieldState) {\n    let inputOrTextArea = renderedControl;\n    if (isInputState(state) && !inputOrTextArea) {\n      // Get cached <input> or create it.\n      inputOrTextArea = this.inputControl || document.createElement('input');\n      // Cache the <input> to re-use it next time.\n      this.inputControl = inputOrTextArea;\n    } else if (!inputOrTextArea) {\n      // Get cached <textarea> or create it.\n      inputOrTextArea =\n        this.textAreaControl || document.createElement('textarea');\n      // Cache the <textarea> to re-use it next time.\n      this.textAreaControl = inputOrTextArea;\n    }\n\n    // Set this variable so we can check it for input-specific properties.\n    const input = isInputState(state)\n      ? (inputOrTextArea as HTMLInputElement)\n      : null;\n\n    // Set input's \"type\" first, since this can change the other properties\n    if (input) {\n      input.type = state.type;\n    }\n\n    if (inputOrTextArea.value !== state.value) {\n      // Only programmatically set the value if there's a difference. When using\n      // the rendered control, the value will always be up to date. Setting the\n      // property (even if it's the same string) will reset the internal <input>\n      // dirty flag, making minlength and maxlength validation reset.\n      inputOrTextArea.value = state.value;\n    }\n\n    inputOrTextArea.required = state.required;\n\n    // The following IDLAttribute properties will always hydrate an attribute,\n    // even if set to a the default value ('' or -1). The presence of the\n    // attribute triggers constraint validation, so we must remove the attribute\n    // when empty.\n    if (input) {\n      const inputState = state as InputState;\n      if (inputState.pattern) {\n        input.pattern = inputState.pattern;\n      } else {\n        input.removeAttribute('pattern');\n      }\n\n      if (inputState.min) {\n        input.min = inputState.min;\n      } else {\n        input.removeAttribute('min');\n      }\n\n      if (inputState.max) {\n        input.max = inputState.max;\n      } else {\n        input.removeAttribute('max');\n      }\n\n      if (inputState.step) {\n        input.step = inputState.step;\n      } else {\n        input.removeAttribute('step');\n      }\n    }\n\n    // Use -1 to represent no minlength and maxlength, which is what the\n    // platform input returns. However, it will throw an error if you try to\n    // manually set it to -1.\n    //\n    // While the type is `number`, it may actually be `null` at runtime.\n    // `null > -1` is true since `null` coerces to `0`, so we default null and\n    // undefined to -1.\n    //\n    // We set attributes instead of properties since setting a property may\n    // throw an out of bounds error in relation to the other property.\n    // Attributes will not throw errors while the state is updating.\n    if ((state.minLength ?? -1) > -1) {\n      inputOrTextArea.setAttribute('minlength', String(state.minLength));\n    } else {\n      inputOrTextArea.removeAttribute('minlength');\n    }\n\n    if ((state.maxLength ?? -1) > -1) {\n      inputOrTextArea.setAttribute('maxlength', String(state.maxLength));\n    } else {\n      inputOrTextArea.removeAttribute('maxlength');\n    }\n\n    return {\n      validity: inputOrTextArea.validity,\n      validationMessage: inputOrTextArea.validationMessage,\n    };\n  }\n\n  protected override equals(\n    {state: prev}: TextFieldState,\n    {state: next}: TextFieldState,\n  ) {\n    // Check shared input and textarea properties\n    const inputOrTextAreaEqual =\n      prev.type === next.type &&\n      prev.value === next.value &&\n      prev.required === next.required &&\n      prev.minLength === next.minLength &&\n      prev.maxLength === next.maxLength;\n\n    if (!isInputState(prev) || !isInputState(next)) {\n      // Both are textareas, all relevant properties are equal.\n      return inputOrTextAreaEqual;\n    }\n\n    // Check additional input-specific properties.\n    return (\n      inputOrTextAreaEqual &&\n      prev.pattern === next.pattern &&\n      prev.min === next.min &&\n      prev.max === next.max &&\n      prev.step === next.step\n    );\n  }\n\n  protected override copy({state}: TextFieldState): TextFieldState {\n    // Don't hold a reference to the rendered control when copying since we\n    // don't use it when checking if the state changed.\n    return {\n      state: isInputState(state)\n        ? this.copyInput(state)\n        : this.copyTextArea(state),\n      renderedControl: null,\n    };\n  }\n\n  private copyInput(state: InputState): InputState {\n    const {type, pattern, min, max, step} = state;\n    return {\n      ...this.copySharedState(state),\n      type,\n      pattern,\n      min,\n      max,\n      step,\n    };\n  }\n\n  private copyTextArea(state: TextAreaState): TextAreaState {\n    return {\n      ...this.copySharedState(state),\n      type: state.type,\n    };\n  }\n\n  private copySharedState({\n    value,\n    required,\n    minLength,\n    maxLength,\n  }: SharedInputAndTextAreaState): SharedInputAndTextAreaState {\n    return {value, required, minLength, maxLength};\n  }\n}\n\nfunction isInputState(state: InputState | TextAreaState): state is InputState {\n  return state.type !== 'textarea';\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {LitElement, PropertyValues, html, nothing} from 'lit';\nimport {property, query, queryAssignedElements, state} from 'lit/decorators.js';\nimport {classMap} from 'lit/directives/class-map.js';\nimport {live} from 'lit/directives/live.js';\nimport {StyleInfo, styleMap} from 'lit/directives/style-map.js';\nimport {StaticValue, html as staticHtml} from 'lit/static-html.js';\n\nimport {Field} from '../../field/internal/field.js';\nimport {ARIAMixinStrict} from '../../internal/aria/aria.js';\nimport {mixinDelegatesAria} from '../../internal/aria/delegate.js';\nimport {stringConverter} from '../../internal/controller/string-converter.js';\nimport {redispatchEvent} from '../../internal/events/redispatch-event.js';\nimport {\n  createValidator,\n  getValidityAnchor,\n  mixinConstraintValidation,\n} from '../../labs/behaviors/constraint-validation.js';\nimport {mixinElementInternals} from '../../labs/behaviors/element-internals.js';\nimport {\n  getFormValue,\n  mixinFormAssociated,\n} from '../../labs/behaviors/form-associated.js';\nimport {\n  mixinOnReportValidity,\n  onReportValidity,\n} from '../../labs/behaviors/on-report-validity.js';\nimport {TextFieldValidator} from '../../labs/behaviors/validators/text-field-validator.js';\nimport {Validator} from '../../labs/behaviors/validators/validator.js';\n\n/**\n * Input types that are compatible with the text field.\n */\nexport type TextFieldType =\n  | 'email'\n  | 'number'\n  | 'password'\n  | 'search'\n  | 'tel'\n  | 'text'\n  | 'url'\n  | 'textarea';\n\n/**\n * Input types that are not fully supported for the text field.\n */\nexport type UnsupportedTextFieldType =\n  | 'color'\n  | 'date'\n  | 'datetime-local'\n  | 'file'\n  | 'month'\n  | 'time'\n  | 'week';\n\n/**\n * Input types that are incompatible with the text field.\n */\nexport type InvalidTextFieldType =\n  | 'button'\n  | 'checkbox'\n  | 'hidden'\n  | 'image'\n  | 'radio'\n  | 'range'\n  | 'reset'\n  | 'submit';\n\n// Separate variable needed for closure.\nconst textFieldBaseClass = mixinDelegatesAria(\n  mixinOnReportValidity(\n    mixinConstraintValidation(\n      mixinFormAssociated(mixinElementInternals(LitElement)),\n    ),\n  ),\n);\n\n/**\n * A text field component.\n *\n * @fires select {Event} The native `select` event on\n * [`<input>`](https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/select_event)\n * --bubbles\n * @fires change {Event} The native `change` event on\n * [`<input>`](https://developer.mozilla.org/en-US/docs/Web/API/HTMLElement/change_event)\n * --bubbles\n * @fires input {InputEvent} The native `input` event on\n * [`<input>`](https://developer.mozilla.org/en-US/docs/Web/API/HTMLElement/input_event)\n * --bubbles --composed\n */\nexport abstract class TextField extends textFieldBaseClass {\n  /** @nocollapse */\n  static override shadowRootOptions: ShadowRootInit = {\n    ...LitElement.shadowRootOptions,\n    delegatesFocus: true,\n  };\n\n  /**\n   * Gets or sets whether or not the text field is in a visually invalid state.\n   *\n   * This error state overrides the error state controlled by\n   * `reportValidity()`.\n   */\n  @property({type: Boolean, reflect: true}) error = false;\n\n  /**\n   * The error message that replaces supporting text when `error` is true. If\n   * `errorText` is an empty string, then the supporting text will continue to\n   * show.\n   *\n   * This error message overrides the error message displayed by\n   * `reportValidity()`.\n   */\n  @property({attribute: 'error-text'}) errorText = '';\n\n  /**\n   * The floating Material label of the textfield component. It informs the user\n   * about what information is requested for a text field. It is aligned with\n   * the input text, is always visible, and it floats when focused or when text\n   * is entered into the textfield. This label also sets accessibilty labels,\n   * but the accessible label is overriden by `aria-label`.\n   *\n   * Learn more about floating labels from the Material Design guidelines:\n   * https://m3.material.io/components/text-fields/guidelines\n   */\n  @property() label = '';\n\n  /**\n   * Disables the asterisk on the floating label, when the text field is\n   * required.\n   */\n  @property({type: Boolean, attribute: 'no-asterisk'}) noAsterisk = false;\n\n  /**\n   * Indicates that the user must specify a value for the input before the\n   * owning form can be submitted and will render an error state when\n   * `reportValidity()` is invoked when value is empty. Additionally the\n   * floating label will render an asterisk `\"*\"` when true.\n   *\n   * https://developer.mozilla.org/en-US/docs/Web/HTML/Attributes/required\n   */\n  @property({type: Boolean, reflect: true}) required = false;\n\n  /**\n   * The current value of the text field. It is always a string.\n   */\n  @property() value = '';\n\n  /**\n   * An optional prefix to display before the input value.\n   */\n  @property({attribute: 'prefix-text'}) prefixText = '';\n\n  /**\n   * An optional suffix to display after the input value.\n   */\n  @property({attribute: 'suffix-text'}) suffixText = '';\n\n  /**\n   * Whether or not the text field has a leading icon. Used for SSR.\n   */\n  @property({type: Boolean, attribute: 'has-leading-icon'})\n  hasLeadingIcon = false;\n\n  /**\n   * Whether or not the text field has a trailing icon. Used for SSR.\n   */\n  @property({type: Boolean, attribute: 'has-trailing-icon'})\n  hasTrailingIcon = false;\n\n  /**\n   * Conveys additional information below the text field, such as how it should\n   * be used.\n   */\n  @property({attribute: 'supporting-text'}) supportingText = '';\n\n  /**\n   * Override the input text CSS `direction`. Useful for RTL languages that use\n   * LTR notation for fractions.\n   */\n  @property({attribute: 'text-direction'}) textDirection = '';\n\n  /**\n   * The number of rows to display for a `type=\"textarea\"` text field.\n   * Defaults to 2.\n   */\n  @property({type: Number}) rows = 2;\n\n  /**\n   * The number of cols to display for a `type=\"textarea\"` text field.\n   * Defaults to 20.\n   */\n  @property({type: Number}) cols = 20;\n\n  // <input> properties\n  @property({reflect: true}) override inputMode = '';\n\n  /**\n   * Defines the greatest value in the range of permitted values.\n   *\n   * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input#max\n   */\n  @property() max = '';\n\n  /**\n   * The maximum number of characters a user can enter into the text field. Set\n   * to -1 for none.\n   *\n   * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input#maxlength\n   */\n  @property({type: Number}) maxLength = -1;\n\n  /**\n   * Defines the most negative value in the range of permitted values.\n   *\n   * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input#min\n   */\n  @property() min = '';\n\n  /**\n   * The minimum number of characters a user can enter into the text field. Set\n   * to -1 for none.\n   *\n   * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input#minlength\n   */\n  @property({type: Number}) minLength = -1;\n\n  /**\n   * When true, hide the spinner for `type=\"number\"` text fields.\n   */\n  @property({type: Boolean, attribute: 'no-spinner'}) noSpinner = false;\n\n  /**\n   * A regular expression that the text field's value must match to pass\n   * constraint validation.\n   *\n   * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input#pattern\n   */\n  @property() pattern = '';\n\n  /**\n   * Defines the text displayed in the textfield when it has no value. Provides\n   * a brief hint to the user as to the expected type of data that should be\n   * entered into the control. Unlike `label`, the placeholder is not visible\n   * and does not float when the textfield has a value.\n   *\n   * https://developer.mozilla.org/en-US/docs/Web/HTML/Attributes/placeholder\n   */\n  @property({reflect: true, converter: stringConverter}) placeholder = '';\n\n  /**\n   * Indicates whether or not a user should be able to edit the text field's\n   * value.\n   *\n   * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input#readonly\n   */\n  @property({type: Boolean, reflect: true}) readOnly = false;\n\n  /**\n   * Indicates that input accepts multiple email addresses.\n   *\n   * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input/email#multiple\n   */\n  @property({type: Boolean, reflect: true}) multiple = false;\n\n  /**\n   * Gets or sets the direction in which selection occurred.\n   */\n  get selectionDirection() {\n    return this.getInputOrTextarea().selectionDirection;\n  }\n  set selectionDirection(value: 'forward' | 'backward' | 'none' | null) {\n    this.getInputOrTextarea().selectionDirection = value;\n  }\n\n  /**\n   * Gets or sets the end position or offset of a text selection.\n   */\n  get selectionEnd() {\n    return this.getInputOrTextarea().selectionEnd;\n  }\n  set selectionEnd(value: number | null) {\n    this.getInputOrTextarea().selectionEnd = value;\n  }\n\n  /**\n   * Gets or sets the starting position or offset of a text selection.\n   */\n  get selectionStart() {\n    return this.getInputOrTextarea().selectionStart;\n  }\n  set selectionStart(value: number | null) {\n    this.getInputOrTextarea().selectionStart = value;\n  }\n\n  /**\n   * Returns or sets the element's step attribute, which works with min and max\n   * to limit the increments at which a numeric or date-time value can be set.\n   *\n   * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input#step\n   */\n  @property() step = '';\n\n  /**\n   * The `<input>` type to use, defaults to \"text\". The type greatly changes how\n   * the text field behaves.\n   *\n   * Text fields support a limited number of `<input>` types:\n   *\n   * - text\n   * - textarea\n   * - email\n   * - number\n   * - password\n   * - search\n   * - tel\n   * - url\n   *\n   * See\n   * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input#input_types\n   * for more details on each input type.\n   */\n  @property({reflect: true})\n  type: TextFieldType | UnsupportedTextFieldType = 'text';\n\n  /**\n   * Describes what, if any, type of autocomplete functionality the input\n   * should provide.\n   *\n   * https://developer.mozilla.org/en-US/docs/Web/HTML/Attributes/autocomplete\n   */\n  @property({reflect: true}) autocomplete = '';\n\n  /**\n   * The text field's value as a number.\n   */\n  get valueAsNumber() {\n    const input = this.getInput();\n    if (!input) {\n      return NaN;\n    }\n\n    return input.valueAsNumber;\n  }\n  set valueAsNumber(value: number) {\n    const input = this.getInput();\n    if (!input) {\n      return;\n    }\n\n    input.valueAsNumber = value;\n    this.value = input.value;\n  }\n\n  /**\n   * The text field's value as a Date.\n   */\n  get valueAsDate() {\n    const input = this.getInput();\n    if (!input) {\n      return null;\n    }\n\n    return input.valueAsDate;\n  }\n  set valueAsDate(value: Date | null) {\n    const input = this.getInput();\n    if (!input) {\n      return;\n    }\n\n    input.valueAsDate = value;\n    this.value = input.value;\n  }\n\n  protected abstract readonly fieldTag: StaticValue;\n\n  /**\n   * Returns true when the text field has been interacted with. Native\n   * validation errors only display in response to user interactions.\n   */\n  @state() private dirty = false;\n  @state() private focused = false;\n  /**\n   * Whether or not a native error has been reported via `reportValidity()`.\n   */\n  @state() private nativeError = false;\n  /**\n   * The validation message displayed from a native error via\n   * `reportValidity()`.\n   */\n  @state() private nativeErrorText = '';\n\n  private get hasError() {\n    return this.error || this.nativeError;\n  }\n\n  @query('.input')\n  private readonly inputOrTextarea!:\n    | HTMLInputElement\n    | HTMLTextAreaElement\n    | null;\n  @query('.field') private readonly field!: Field | null;\n  @queryAssignedElements({slot: 'leading-icon'})\n  private readonly leadingIcons!: Element[];\n  @queryAssignedElements({slot: 'trailing-icon'})\n  private readonly trailingIcons!: Element[];\n\n  /**\n   * Selects all the text in the text field.\n   *\n   * https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/select\n   */\n  select() {\n    this.getInputOrTextarea().select();\n  }\n\n  /**\n   * Replaces a range of text with a new string.\n   *\n   * https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/setRangeText\n   */\n  setRangeText(replacement: string): void;\n  setRangeText(\n    replacement: string,\n    start: number,\n    end: number,\n    selectionMode?: SelectionMode,\n  ): void;\n  setRangeText(...args: unknown[]) {\n    // Calling setRangeText with 1 vs 3-4 arguments has different behavior.\n    // Use spread syntax and type casting to ensure correct usage.\n    this.getInputOrTextarea().setRangeText(\n      ...(args as Parameters<HTMLInputElement['setRangeText']>),\n    );\n    this.value = this.getInputOrTextarea().value;\n  }\n\n  /**\n   * Sets the start and end positions of a selection in the text field.\n   *\n   * https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/setSelectionRange\n   *\n   * @param start The offset into the text field for the start of the selection.\n   * @param end The offset into the text field for the end of the selection.\n   * @param direction The direction in which the selection is performed.\n   */\n  setSelectionRange(\n    start: number | null,\n    end: number | null,\n    direction?: 'forward' | 'backward' | 'none',\n  ) {\n    this.getInputOrTextarea().setSelectionRange(start, end, direction);\n  }\n\n  /**\n   * Shows the browser picker for an input element of type \"date\", \"time\", etc.\n   *\n   * For a full list of supported types, see:\n   * https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/showPicker#browser_compatibility\n   *\n   * https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/showPicker\n   */\n  showPicker() {\n    const input = this.getInput();\n    if (!input) {\n      return;\n    }\n\n    input.showPicker();\n  }\n\n  /**\n   * Decrements the value of a numeric type text field by `step` or `n` `step`\n   * number of times.\n   *\n   * https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/stepDown\n   *\n   * @param stepDecrement The number of steps to decrement, defaults to 1.\n   */\n  stepDown(stepDecrement?: number) {\n    const input = this.getInput();\n    if (!input) {\n      return;\n    }\n\n    input.stepDown(stepDecrement);\n    this.value = input.value;\n  }\n\n  /**\n   * Increments the value of a numeric type text field by `step` or `n` `step`\n   * number of times.\n   *\n   * https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/stepUp\n   *\n   * @param stepIncrement The number of steps to increment, defaults to 1.\n   */\n  stepUp(stepIncrement?: number) {\n    const input = this.getInput();\n    if (!input) {\n      return;\n    }\n\n    input.stepUp(stepIncrement);\n    this.value = input.value;\n  }\n\n  /**\n   * Reset the text field to its default value.\n   */\n  reset() {\n    this.dirty = false;\n    this.value = this.getAttribute('value') ?? '';\n    this.nativeError = false;\n    this.nativeErrorText = '';\n  }\n\n  override attributeChangedCallback(\n    attribute: string,\n    newValue: string | null,\n    oldValue: string | null,\n  ) {\n    if (attribute === 'value' && this.dirty) {\n      // After user input, changing the value attribute no longer updates the\n      // text field's value (until reset). This matches native <input> behavior.\n      return;\n    }\n\n    super.attributeChangedCallback(attribute, newValue, oldValue);\n  }\n\n  protected override render() {\n    const classes = {\n      'disabled': this.disabled,\n      'error': !this.disabled && this.hasError,\n      'textarea': this.type === 'textarea',\n      'no-spinner': this.noSpinner,\n    };\n\n    return html`\n      <span class=\"text-field ${classMap(classes)}\">\n        ${this.renderField()}\n      </span>\n    `;\n  }\n\n  protected override updated(changedProperties: PropertyValues) {\n    // Keep changedProperties arg so that subclasses may call it\n\n    // If a property such as `type` changes and causes the internal <input>\n    // value to change without dispatching an event, re-sync it.\n    const value = this.getInputOrTextarea().value;\n    if (this.value !== value) {\n      // Note this is typically inefficient in updated() since it schedules\n      // another update. However, it is needed for the <input> to fully render\n      // before checking its value.\n      this.value = value;\n    }\n  }\n\n  private renderField() {\n    return staticHtml`<${this.fieldTag}\n      class=\"field\"\n      count=${this.value.length}\n      ?disabled=${this.disabled}\n      ?error=${this.hasError}\n      error-text=${this.getErrorText()}\n      ?focused=${this.focused}\n      ?has-end=${this.hasTrailingIcon}\n      ?has-start=${this.hasLeadingIcon}\n      label=${this.label}\n      ?no-asterisk=${this.noAsterisk}\n      max=${this.maxLength}\n      ?populated=${!!this.value}\n      ?required=${this.required}\n      ?resizable=${this.type === 'textarea'}\n      supporting-text=${this.supportingText}\n    >\n      ${this.renderLeadingIcon()}\n      ${this.renderInputOrTextarea()}\n      ${this.renderTrailingIcon()}\n      <div id=\"description\" slot=\"aria-describedby\"></div>\n      <slot name=\"container\" slot=\"container\"></slot>\n    </${this.fieldTag}>`;\n  }\n\n  private renderLeadingIcon() {\n    return html`\n      <span class=\"icon leading\" slot=\"start\">\n        <slot name=\"leading-icon\" @slotchange=${this.handleIconChange}></slot>\n      </span>\n    `;\n  }\n\n  private renderTrailingIcon() {\n    return html`\n      <span class=\"icon trailing\" slot=\"end\">\n        <slot name=\"trailing-icon\" @slotchange=${this.handleIconChange}></slot>\n      </span>\n    `;\n  }\n\n  private renderInputOrTextarea() {\n    const style: StyleInfo = {'direction': this.textDirection};\n    const ariaLabel =\n      (this as ARIAMixinStrict).ariaLabel || this.label || nothing;\n    // lit-anaylzer `autocomplete` types are too strict\n    // tslint:disable-next-line:no-any\n    const autocomplete = this.autocomplete as any;\n\n    // These properties may be set to null if the attribute is removed, and\n    // `null > -1` is incorrectly `true`.\n    const hasMaxLength = (this.maxLength ?? -1) > -1;\n    const hasMinLength = (this.minLength ?? -1) > -1;\n    if (this.type === 'textarea') {\n      return html`\n        <textarea\n          class=\"input\"\n          style=${styleMap(style)}\n          aria-describedby=\"description\"\n          aria-invalid=${this.hasError}\n          aria-label=${ariaLabel}\n          autocomplete=${autocomplete || nothing}\n          name=${this.name || nothing}\n          ?disabled=${this.disabled}\n          maxlength=${hasMaxLength ? this.maxLength : nothing}\n          minlength=${hasMinLength ? this.minLength : nothing}\n          placeholder=${this.placeholder || nothing}\n          ?readonly=${this.readOnly}\n          ?required=${this.required}\n          rows=${this.rows}\n          cols=${this.cols}\n          .value=${live(this.value)}\n          @change=${this.redispatchEvent}\n          @focus=${this.handleFocusChange}\n          @blur=${this.handleFocusChange}\n          @input=${this.handleInput}\n          @select=${this.redispatchEvent}></textarea>\n      `;\n    }\n\n    const prefix = this.renderPrefix();\n    const suffix = this.renderSuffix();\n\n    // TODO(b/243805848): remove `as unknown as number` and `as any` once lit\n    // analyzer is fixed\n    // tslint:disable-next-line:no-any\n    const inputMode = this.inputMode as any;\n    return html`\n      <div class=\"input-wrapper\">\n        ${prefix}\n        <input\n          class=\"input\"\n          style=${styleMap(style)}\n          aria-describedby=\"description\"\n          aria-invalid=${this.hasError}\n          aria-label=${ariaLabel}\n          autocomplete=${autocomplete || nothing}\n          name=${this.name || nothing}\n          ?disabled=${this.disabled}\n          inputmode=${inputMode || nothing}\n          max=${(this.max || nothing) as unknown as number}\n          maxlength=${hasMaxLength ? this.maxLength : nothing}\n          min=${(this.min || nothing) as unknown as number}\n          minlength=${hasMinLength ? this.minLength : nothing}\n          pattern=${this.pattern || nothing}\n          placeholder=${this.placeholder || nothing}\n          ?readonly=${this.readOnly}\n          ?required=${this.required}\n          ?multiple=${this.multiple}\n          step=${(this.step || nothing) as unknown as number}\n          type=${this.type}\n          .value=${live(this.value)}\n          @change=${this.redispatchEvent}\n          @focus=${this.handleFocusChange}\n          @blur=${this.handleFocusChange}\n          @input=${this.handleInput}\n          @select=${this.redispatchEvent} />\n        ${suffix}\n      </div>\n    `;\n  }\n\n  private renderPrefix() {\n    return this.renderAffix(this.prefixText, /* isSuffix */ false);\n  }\n\n  private renderSuffix() {\n    return this.renderAffix(this.suffixText, /* isSuffix */ true);\n  }\n\n  private renderAffix(text: string, isSuffix: boolean) {\n    if (!text) {\n      return nothing;\n    }\n\n    const classes = {\n      'suffix': isSuffix,\n      'prefix': !isSuffix,\n    };\n\n    return html`<span class=\"${classMap(classes)}\">${text}</span>`;\n  }\n\n  private getErrorText() {\n    return this.error ? this.errorText : this.nativeErrorText;\n  }\n\n  private handleFocusChange() {\n    // When calling focus() or reportValidity() during change, it's possible\n    // for blur to be called after the new focus event. Rather than set\n    // `this.focused` to true/false on focus/blur, we always set it to whether\n    // or not the input itself is focused.\n    this.focused = this.inputOrTextarea?.matches(':focus') ?? false;\n  }\n\n  private handleInput(event: InputEvent) {\n    this.dirty = true;\n    this.value = (event.target as HTMLInputElement).value;\n  }\n\n  private redispatchEvent(event: Event) {\n    redispatchEvent(this, event);\n  }\n\n  private getInputOrTextarea() {\n    if (!this.inputOrTextarea) {\n      // If the input is not yet defined, synchronously render.\n      // e.g.\n      // const textField = document.createElement('md-outlined-text-field');\n      // document.body.appendChild(textField);\n      // textField.focus(); // synchronously render\n      this.connectedCallback();\n      this.scheduleUpdate();\n    }\n\n    if (this.isUpdatePending) {\n      // If there are pending updates, synchronously perform them. This ensures\n      // that constraint validation properties (like `required`) are synced\n      // before interacting with input APIs that depend on them.\n      this.scheduleUpdate();\n    }\n\n    return this.inputOrTextarea!;\n  }\n\n  private getInput() {\n    if (this.type === 'textarea') {\n      return null;\n    }\n\n    return this.getInputOrTextarea() as HTMLInputElement;\n  }\n\n  private handleIconChange() {\n    this.hasLeadingIcon = this.leadingIcons.length > 0;\n    this.hasTrailingIcon = this.trailingIcons.length > 0;\n  }\n\n  // Writable mixin properties for lit-html binding, needed for lit-analyzer\n  declare disabled: boolean;\n  declare name: string;\n\n  override [getFormValue]() {\n    return this.value;\n  }\n\n  override formResetCallback() {\n    this.reset();\n  }\n\n  override formStateRestoreCallback(state: string) {\n    this.value = state;\n  }\n\n  override focus() {\n    // Required for the case that the user slots a focusable element into the\n    // leading icon slot such as an iconbutton due to how delegatesFocus works.\n    this.getInputOrTextarea().focus();\n  }\n\n  override [createValidator](): Validator<unknown> {\n    return new TextFieldValidator(() => ({\n      state: this,\n      renderedControl: this.inputOrTextarea,\n    }));\n  }\n\n  override [getValidityAnchor](): HTMLElement | null {\n    return this.inputOrTextarea;\n  }\n\n  override [onReportValidity](invalidEvent: Event | null) {\n    // Prevent default pop-up behavior.\n    invalidEvent?.preventDefault();\n\n    const prevMessage = this.getErrorText();\n    this.nativeError = !!invalidEvent;\n    this.nativeErrorText = this.validationMessage;\n\n    if (prevMessage === this.getErrorText()) {\n      this.field?.reannounceError();\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOO,IAAM,SAAS;;;;ACetB,IAAM,EAAC,YAAY,UAAS,IAAI;AAIhC,IAAM,0BAA0B;AAEhC,IAAM,OACJ,2BACA,OAAO,UAAU,SACjB,OAAO,UAAU,YAAY,OACzB,OAAO,SAAU,OACjB,CAAC,SAAe;AAsEf,IAAM,qBAAqB,CAAC,SAChC,KAA2B,YAAY;AAkG1C,IAAM,cAAc,CAAA;AAab,IAAM,oBAAoB,CAAC,MAAY,QAAiB,gBAC5D,KAAK,mBAAmB;;;ACxM3B,IAAM,gBAAN,cAA4B,UAAS;EACnC,YAAY,UAAkB;AAC5B,UAAM,QAAQ;AACd,QACE,EACE,SAAS,SAAS,SAAS,YAC3B,SAAS,SAAS,SAAS,aAC3B,SAAS,SAAS,SAAS,oBAE7B;AACA,YAAM,IAAI,MACR,gEAAgE;IAEpE;AACA,QAAI,CAAC,mBAAmB,QAAQ,GAAG;AACjC,YAAM,IAAI,MAAM,sDAAsD;IACxE;EACF;EAEA,OAAO,OAAc;AACnB,WAAO;EACT;EAES,OAAO,MAAqB,CAAC,KAAK,GAA4B;AACrE,QAAI,UAAU,YAAY,UAAU,SAAS;AAC3C,aAAO;IACT;AACA,UAAM,UAAU,KAAK;AACrB,UAAM,OAAO,KAAK;AAElB,QAAI,KAAK,SAAS,SAAS,UAAU;AAEnC,UAAI,UAAW,QAAgB,IAAI,GAAG;AACpC,eAAO;MACT;IACF,WAAW,KAAK,SAAS,SAAS,mBAAmB;AACnD,UAAI,CAAC,CAAC,UAAU,QAAQ,aAAa,IAAI,GAAG;AAC1C,eAAO;MACT;IACF,WAAW,KAAK,SAAS,SAAS,WAAW;AAC3C,UAAI,QAAQ,aAAa,IAAI,MAAM,OAAO,KAAK,GAAG;AAChD,eAAO;MACT;IACF;AAGA,sBAAkB,IAAI;AACtB,WAAO;EACT;;AA2BK,IAAM,OAAO,UAAU,aAAa;;;ACrFpC,IAAM,kBAAkB;EAC7B,cAAc,OAAoB;AAChC,WAAO,SAAS;EAClB;EACA,YAAY,OAAa;AACvB,WAAO,SAAS;EAClB;;;;ACkGI,IAAO,qBAAP,cAAkC,UAAyB;EAI5C,gBAAgB,EAAC,OAAAA,QAAO,gBAAe,GAAiB;AACzE,QAAI,kBAAkB;AACtB,QAAI,aAAaA,MAAK,KAAK,CAAC,iBAAiB;AAE3C,wBAAkB,KAAK,gBAAgB,SAAS,cAAc,OAAO;AAErE,WAAK,eAAe;IACtB,WAAW,CAAC,iBAAiB;AAE3B,wBACE,KAAK,mBAAmB,SAAS,cAAc,UAAU;AAE3D,WAAK,kBAAkB;IACzB;AAGA,UAAM,QAAQ,aAAaA,MAAK,IAC3B,kBACD;AAGJ,QAAI,OAAO;AACT,YAAM,OAAOA,OAAM;IACrB;AAEA,QAAI,gBAAgB,UAAUA,OAAM,OAAO;AAKzC,sBAAgB,QAAQA,OAAM;IAChC;AAEA,oBAAgB,WAAWA,OAAM;AAMjC,QAAI,OAAO;AACT,YAAM,aAAaA;AACnB,UAAI,WAAW,SAAS;AACtB,cAAM,UAAU,WAAW;MAC7B,OAAO;AACL,cAAM,gBAAgB,SAAS;MACjC;AAEA,UAAI,WAAW,KAAK;AAClB,cAAM,MAAM,WAAW;MACzB,OAAO;AACL,cAAM,gBAAgB,KAAK;MAC7B;AAEA,UAAI,WAAW,KAAK;AAClB,cAAM,MAAM,WAAW;MACzB,OAAO;AACL,cAAM,gBAAgB,KAAK;MAC7B;AAEA,UAAI,WAAW,MAAM;AACnB,cAAM,OAAO,WAAW;MAC1B,OAAO;AACL,cAAM,gBAAgB,MAAM;MAC9B;IACF;AAaA,SAAKA,OAAM,aAAa,MAAM,IAAI;AAChC,sBAAgB,aAAa,aAAa,OAAOA,OAAM,SAAS,CAAC;IACnE,OAAO;AACL,sBAAgB,gBAAgB,WAAW;IAC7C;AAEA,SAAKA,OAAM,aAAa,MAAM,IAAI;AAChC,sBAAgB,aAAa,aAAa,OAAOA,OAAM,SAAS,CAAC;IACnE,OAAO;AACL,sBAAgB,gBAAgB,WAAW;IAC7C;AAEA,WAAO;MACL,UAAU,gBAAgB;MAC1B,mBAAmB,gBAAgB;;EAEvC;EAEmB,OACjB,EAAC,OAAO,KAAI,GACZ,EAAC,OAAO,KAAI,GAAiB;AAG7B,UAAM,uBACJ,KAAK,SAAS,KAAK,QACnB,KAAK,UAAU,KAAK,SACpB,KAAK,aAAa,KAAK,YACvB,KAAK,cAAc,KAAK,aACxB,KAAK,cAAc,KAAK;AAE1B,QAAI,CAAC,aAAa,IAAI,KAAK,CAAC,aAAa,IAAI,GAAG;AAE9C,aAAO;IACT;AAGA,WACE,wBACA,KAAK,YAAY,KAAK,WACtB,KAAK,QAAQ,KAAK,OAClB,KAAK,QAAQ,KAAK,OAClB,KAAK,SAAS,KAAK;EAEvB;EAEmB,KAAK,EAAC,OAAAA,OAAK,GAAiB;AAG7C,WAAO;MACL,OAAO,aAAaA,MAAK,IACrB,KAAK,UAAUA,MAAK,IACpB,KAAK,aAAaA,MAAK;MAC3B,iBAAiB;;EAErB;EAEQ,UAAUA,QAAiB;AACjC,UAAM,EAAC,MAAM,SAAS,KAAK,KAAK,KAAI,IAAIA;AACxC,WAAO;MACL,GAAG,KAAK,gBAAgBA,MAAK;MAC7B;MACA;MACA;MACA;MACA;;EAEJ;EAEQ,aAAaA,QAAoB;AACvC,WAAO;MACL,GAAG,KAAK,gBAAgBA,MAAK;MAC7B,MAAMA,OAAM;;EAEhB;EAEQ,gBAAgB,EACtB,OACA,UACA,WACA,UAAS,GACmB;AAC5B,WAAO,EAAC,OAAO,UAAU,WAAW,UAAS;EAC/C;;AAGF,SAAS,aAAaA,QAAiC;AACrD,SAAOA,OAAM,SAAS;AACxB;;;AC5MA,IAAM,qBAAqB,mBACzB,sBACE,0BACE,oBAAoB,sBAAsB,UAAU,CAAC,CAAC,CACvD,CACF;AAgBG,IAAgB,YAAhB,cAAkC,mBAAkB;EAA1D,cAAA;;AAa4C,SAAA,QAAQ;AAUb,SAAA,YAAY;AAYrC,SAAA,QAAQ;AAMiC,SAAA,aAAa;AAUxB,SAAA,WAAW;AAKzC,SAAA,QAAQ;AAKkB,SAAA,aAAa;AAKb,SAAA,aAAa;AAMnD,SAAA,iBAAiB;AAMjB,SAAA,kBAAkB;AAMwB,SAAA,iBAAiB;AAMlB,SAAA,gBAAgB;AAM/B,SAAA,OAAO;AAMP,SAAA,OAAO;AAGG,SAAA,YAAY;AAOpC,SAAA,MAAM;AAQQ,SAAA,YAAY;AAO1B,SAAA,MAAM;AAQQ,SAAA,YAAY;AAKc,SAAA,YAAY;AAQpD,SAAA,UAAU;AAUiC,SAAA,cAAc;AAQ3B,SAAA,WAAW;AAOX,SAAA,WAAW;AAsCzC,SAAA,OAAO;AAsBnB,SAAA,OAAiD;AAQtB,SAAA,eAAe;AAkDzB,SAAA,QAAQ;AACR,SAAA,UAAU;AAIV,SAAA,cAAc;AAKd,SAAA,kBAAkB;EA8ZrC;;;;EAzhBE,IAAI,qBAAkB;AACpB,WAAO,KAAK,mBAAkB,EAAG;EACnC;EACA,IAAI,mBAAmB,OAA6C;AAClE,SAAK,mBAAkB,EAAG,qBAAqB;EACjD;;;;EAKA,IAAI,eAAY;AACd,WAAO,KAAK,mBAAkB,EAAG;EACnC;EACA,IAAI,aAAa,OAAoB;AACnC,SAAK,mBAAkB,EAAG,eAAe;EAC3C;;;;EAKA,IAAI,iBAAc;AAChB,WAAO,KAAK,mBAAkB,EAAG;EACnC;EACA,IAAI,eAAe,OAAoB;AACrC,SAAK,mBAAkB,EAAG,iBAAiB;EAC7C;;;;EA2CA,IAAI,gBAAa;AACf,UAAM,QAAQ,KAAK,SAAQ;AAC3B,QAAI,CAAC,OAAO;AACV,aAAO;IACT;AAEA,WAAO,MAAM;EACf;EACA,IAAI,cAAc,OAAa;AAC7B,UAAM,QAAQ,KAAK,SAAQ;AAC3B,QAAI,CAAC,OAAO;AACV;IACF;AAEA,UAAM,gBAAgB;AACtB,SAAK,QAAQ,MAAM;EACrB;;;;EAKA,IAAI,cAAW;AACb,UAAM,QAAQ,KAAK,SAAQ;AAC3B,QAAI,CAAC,OAAO;AACV,aAAO;IACT;AAEA,WAAO,MAAM;EACf;EACA,IAAI,YAAY,OAAkB;AAChC,UAAM,QAAQ,KAAK,SAAQ;AAC3B,QAAI,CAAC,OAAO;AACV;IACF;AAEA,UAAM,cAAc;AACpB,SAAK,QAAQ,MAAM;EACrB;EAoBA,IAAY,WAAQ;AAClB,WAAO,KAAK,SAAS,KAAK;EAC5B;;;;;;EAkBA,SAAM;AACJ,SAAK,mBAAkB,EAAG,OAAM;EAClC;EAcA,gBAAgB,MAAe;AAG7B,SAAK,mBAAkB,EAAG,aACxB,GAAI,IAAqD;AAE3D,SAAK,QAAQ,KAAK,mBAAkB,EAAG;EACzC;;;;;;;;;;EAWA,kBACE,OACA,KACA,WAA2C;AAE3C,SAAK,mBAAkB,EAAG,kBAAkB,OAAO,KAAK,SAAS;EACnE;;;;;;;;;EAUA,aAAU;AACR,UAAM,QAAQ,KAAK,SAAQ;AAC3B,QAAI,CAAC,OAAO;AACV;IACF;AAEA,UAAM,WAAU;EAClB;;;;;;;;;EAUA,SAAS,eAAsB;AAC7B,UAAM,QAAQ,KAAK,SAAQ;AAC3B,QAAI,CAAC,OAAO;AACV;IACF;AAEA,UAAM,SAAS,aAAa;AAC5B,SAAK,QAAQ,MAAM;EACrB;;;;;;;;;EAUA,OAAO,eAAsB;AAC3B,UAAM,QAAQ,KAAK,SAAQ;AAC3B,QAAI,CAAC,OAAO;AACV;IACF;AAEA,UAAM,OAAO,aAAa;AAC1B,SAAK,QAAQ,MAAM;EACrB;;;;EAKA,QAAK;AACH,SAAK,QAAQ;AACb,SAAK,QAAQ,KAAK,aAAa,OAAO,KAAK;AAC3C,SAAK,cAAc;AACnB,SAAK,kBAAkB;EACzB;EAES,yBACP,WACA,UACA,UAAuB;AAEvB,QAAI,cAAc,WAAW,KAAK,OAAO;AAGvC;IACF;AAEA,UAAM,yBAAyB,WAAW,UAAU,QAAQ;EAC9D;EAEmB,SAAM;AACvB,UAAM,UAAU;MACd,YAAY,KAAK;MACjB,SAAS,CAAC,KAAK,YAAY,KAAK;MAChC,YAAY,KAAK,SAAS;MAC1B,cAAc,KAAK;;AAGrB,WAAO;gCACqB,SAAS,OAAO,CAAC;UACvC,KAAK,YAAW,CAAE;;;EAG1B;EAEmB,QAAQ,mBAAiC;AAK1D,UAAM,QAAQ,KAAK,mBAAkB,EAAG;AACxC,QAAI,KAAK,UAAU,OAAO;AAIxB,WAAK,QAAQ;IACf;EACF;EAEQ,cAAW;AACjB,WAAOC,SAAc,KAAK,QAAQ;;cAExB,KAAK,MAAM,MAAM;kBACb,KAAK,QAAQ;eAChB,KAAK,QAAQ;mBACT,KAAK,aAAY,CAAE;iBACrB,KAAK,OAAO;iBACZ,KAAK,eAAe;mBAClB,KAAK,cAAc;cACxB,KAAK,KAAK;qBACH,KAAK,UAAU;YACxB,KAAK,SAAS;mBACP,CAAC,CAAC,KAAK,KAAK;kBACb,KAAK,QAAQ;mBACZ,KAAK,SAAS,UAAU;wBACnB,KAAK,cAAc;;QAEnC,KAAK,kBAAiB,CAAE;QACxB,KAAK,sBAAqB,CAAE;QAC5B,KAAK,mBAAkB,CAAE;;;QAGzB,KAAK,QAAQ;EACnB;EAEQ,oBAAiB;AACvB,WAAO;;gDAEqC,KAAK,gBAAgB;;;EAGnE;EAEQ,qBAAkB;AACxB,WAAO;;iDAEsC,KAAK,gBAAgB;;;EAGpE;EAEQ,wBAAqB;AAC3B,UAAM,QAAmB,EAAC,aAAa,KAAK,cAAa;AACzD,UAAM,YACH,KAAyB,aAAa,KAAK,SAAS;AAGvD,UAAM,eAAe,KAAK;AAI1B,UAAM,gBAAgB,KAAK,aAAa,MAAM;AAC9C,UAAM,gBAAgB,KAAK,aAAa,MAAM;AAC9C,QAAI,KAAK,SAAS,YAAY;AAC5B,aAAO;;;kBAGK,SAAS,KAAK,CAAC;;yBAER,KAAK,QAAQ;uBACf,SAAS;yBACP,gBAAgB,OAAO;iBAC/B,KAAK,QAAQ,OAAO;sBACf,KAAK,QAAQ;sBACb,eAAe,KAAK,YAAY,OAAO;sBACvC,eAAe,KAAK,YAAY,OAAO;wBACrC,KAAK,eAAe,OAAO;sBAC7B,KAAK,QAAQ;sBACb,KAAK,QAAQ;iBAClB,KAAK,IAAI;iBACT,KAAK,IAAI;mBACP,KAAK,KAAK,KAAK,CAAC;oBACf,KAAK,eAAe;mBACrB,KAAK,iBAAiB;kBACvB,KAAK,iBAAiB;mBACrB,KAAK,WAAW;oBACf,KAAK,eAAe;;IAEpC;AAEA,UAAM,SAAS,KAAK,aAAY;AAChC,UAAM,SAAS,KAAK,aAAY;AAKhC,UAAM,YAAY,KAAK;AACvB,WAAO;;UAED,MAAM;;;kBAGE,SAAS,KAAK,CAAC;;yBAER,KAAK,QAAQ;uBACf,SAAS;yBACP,gBAAgB,OAAO;iBAC/B,KAAK,QAAQ,OAAO;sBACf,KAAK,QAAQ;sBACb,aAAa,OAAO;gBACzB,KAAK,OAAO,OAA6B;sBACpC,eAAe,KAAK,YAAY,OAAO;gBAC5C,KAAK,OAAO,OAA6B;sBACpC,eAAe,KAAK,YAAY,OAAO;oBACzC,KAAK,WAAW,OAAO;wBACnB,KAAK,eAAe,OAAO;sBAC7B,KAAK,QAAQ;sBACb,KAAK,QAAQ;sBACb,KAAK,QAAQ;iBACjB,KAAK,QAAQ,OAA6B;iBAC3C,KAAK,IAAI;mBACP,KAAK,KAAK,KAAK,CAAC;oBACf,KAAK,eAAe;mBACrB,KAAK,iBAAiB;kBACvB,KAAK,iBAAiB;mBACrB,KAAK,WAAW;oBACf,KAAK,eAAe;UAC9B,MAAM;;;EAGd;EAEQ,eAAY;AAClB,WAAO,KAAK;MAAY,KAAK;;MAA2B;IAAK;EAC/D;EAEQ,eAAY;AAClB,WAAO,KAAK;MAAY,KAAK;;MAA2B;IAAI;EAC9D;EAEQ,YAAY,MAAc,UAAiB;AACjD,QAAI,CAAC,MAAM;AACT,aAAO;IACT;AAEA,UAAM,UAAU;MACd,UAAU;MACV,UAAU,CAAC;;AAGb,WAAO,oBAAoB,SAAS,OAAO,CAAC,KAAK,IAAI;EACvD;EAEQ,eAAY;AAClB,WAAO,KAAK,QAAQ,KAAK,YAAY,KAAK;EAC5C;EAEQ,oBAAiB;AAKvB,SAAK,UAAU,KAAK,iBAAiB,QAAQ,QAAQ,KAAK;EAC5D;EAEQ,YAAY,OAAiB;AACnC,SAAK,QAAQ;AACb,SAAK,QAAS,MAAM,OAA4B;EAClD;EAEQ,gBAAgB,OAAY;AAClC,oBAAgB,MAAM,KAAK;EAC7B;EAEQ,qBAAkB;AACxB,QAAI,CAAC,KAAK,iBAAiB;AAMzB,WAAK,kBAAiB;AACtB,WAAK,eAAc;IACrB;AAEA,QAAI,KAAK,iBAAiB;AAIxB,WAAK,eAAc;IACrB;AAEA,WAAO,KAAK;EACd;EAEQ,WAAQ;AACd,QAAI,KAAK,SAAS,YAAY;AAC5B,aAAO;IACT;AAEA,WAAO,KAAK,mBAAkB;EAChC;EAEQ,mBAAgB;AACtB,SAAK,iBAAiB,KAAK,aAAa,SAAS;AACjD,SAAK,kBAAkB,KAAK,cAAc,SAAS;EACrD;EAMS,CAAC,YAAY,IAAC;AACrB,WAAO,KAAK;EACd;EAES,oBAAiB;AACxB,SAAK,MAAK;EACZ;EAES,yBAAyBC,QAAa;AAC7C,SAAK,QAAQA;EACf;EAES,QAAK;AAGZ,SAAK,mBAAkB,EAAG,MAAK;EACjC;EAES,CAAC,eAAe,IAAC;AACxB,WAAO,IAAI,mBAAmB,OAAO;MACnC,OAAO;MACP,iBAAiB,KAAK;MACtB;EACJ;EAES,CAAC,iBAAiB,IAAC;AAC1B,WAAO,KAAK;EACd;EAES,CAAC,gBAAgB,EAAE,cAA0B;AAEpD,kBAAc,eAAc;AAE5B,UAAM,cAAc,KAAK,aAAY;AACrC,SAAK,cAAc,CAAC,CAAC;AACrB,SAAK,kBAAkB,KAAK;AAE5B,QAAI,gBAAgB,KAAK,aAAY,GAAI;AACvC,WAAK,OAAO,gBAAe;IAC7B;EACF;;AAxsBgB,UAAA,oBAAoC;EAClD,GAAG,WAAW;EACd,gBAAgB;;AASwB,WAAA;EAAzC,SAAS,EAAC,MAAM,SAAS,SAAS,KAAI,CAAC;;AAUH,WAAA;EAApC,SAAS,EAAC,WAAW,aAAY,CAAC;;AAYvB,WAAA;EAAX,SAAQ;;AAM4C,WAAA;EAApD,SAAS,EAAC,MAAM,SAAS,WAAW,cAAa,CAAC;;AAUT,WAAA;EAAzC,SAAS,EAAC,MAAM,SAAS,SAAS,KAAI,CAAC;;AAK5B,WAAA;EAAX,SAAQ;;AAK6B,WAAA;EAArC,SAAS,EAAC,WAAW,cAAa,CAAC;;AAKE,WAAA;EAArC,SAAS,EAAC,WAAW,cAAa,CAAC;;AAMpC,WAAA;EADC,SAAS,EAAC,MAAM,SAAS,WAAW,mBAAkB,CAAC;;AAOxD,WAAA;EADC,SAAS,EAAC,MAAM,SAAS,WAAW,oBAAmB,CAAC;;AAOf,WAAA;EAAzC,SAAS,EAAC,WAAW,kBAAiB,CAAC;;AAMC,WAAA;EAAxC,SAAS,EAAC,WAAW,iBAAgB,CAAC;;AAMb,WAAA;EAAzB,SAAS,EAAC,MAAM,OAAM,CAAC;;AAME,WAAA;EAAzB,SAAS,EAAC,MAAM,OAAM,CAAC;;AAGY,WAAA;EAAnC,SAAS,EAAC,SAAS,KAAI,CAAC;;AAOb,WAAA;EAAX,SAAQ;;AAQiB,WAAA;EAAzB,SAAS,EAAC,MAAM,OAAM,CAAC;;AAOZ,WAAA;EAAX,SAAQ;;AAQiB,WAAA;EAAzB,SAAS,EAAC,MAAM,OAAM,CAAC;;AAK4B,WAAA;EAAnD,SAAS,EAAC,MAAM,SAAS,WAAW,aAAY,CAAC;;AAQtC,WAAA;EAAX,SAAQ;;AAU8C,WAAA;EAAtD,SAAS,EAAC,SAAS,MAAM,WAAW,gBAAe,CAAC;;AAQX,WAAA;EAAzC,SAAS,EAAC,MAAM,SAAS,SAAS,KAAI,CAAC;;AAOE,WAAA;EAAzC,SAAS,EAAC,MAAM,SAAS,SAAS,KAAI,CAAC;;AAsC5B,WAAA;EAAX,SAAQ;;AAsBT,WAAA;EADC,SAAS,EAAC,SAAS,KAAI,CAAC;;AASE,WAAA;EAA1B,SAAS,EAAC,SAAS,KAAI,CAAC;;AAkDR,WAAA;EAAhB,MAAK;;AACW,WAAA;EAAhB,MAAK;;AAIW,WAAA;EAAhB,MAAK;;AAKW,WAAA;EAAhB,MAAK;;AAOW,WAAA;EADhB,MAAM,QAAQ;;AAKmB,WAAA;EAAjC,MAAM,QAAQ;;AAEE,WAAA;EADhB,sBAAsB,EAAC,MAAM,eAAc,CAAC;;AAG5B,WAAA;EADhB,sBAAsB,EAAC,MAAM,gBAAe,CAAC;;", "names": ["state", "html", "state"]}