{"version": 3, "sources": ["../../@material/web/select/internal/selectoption/selectOptionController.ts", "../../@material/web/select/internal/selectoption/select-option.ts", "../../@material/web/select/select-option.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {Reactive<PERSON>ontroller, ReactiveControllerHost} from 'lit';\n\nimport {\n  MenuItemController,\n  MenuItemControllerConfig,\n} from '../../../menu/internal/controllers/menuItemController.js';\nimport {SelectOption} from './select-option.js';\n\n/**\n * Creates an event fired by a SelectOption to request selection from md-select.\n * Typically fired after `selected` changes from `false` to `true`.\n */\nexport function createRequestSelectionEvent() {\n  return new Event('request-selection', {\n    bubbles: true,\n    composed: true,\n  });\n}\n\n/**\n * Creates an event fired by a SelectOption to request deselection from\n * md-select. Typically fired after `selected` changes from `true` to `false`.\n */\nexport function createRequestDeselectionEvent() {\n  return new Event('request-deselection', {\n    bubbles: true,\n    composed: true,\n  });\n}\n\n/**\n * The options used to inialize SelectOptionController.\n */\nexport type SelectOptionConfig = MenuItemControllerConfig;\n\n/**\n * A controller that provides most functionality and md-select compatibility for\n * an element that implements the SelectOption interface.\n */\nexport class SelectOptionController implements ReactiveController {\n  private readonly menuItemController: MenuItemController;\n  private internalDisplayText: string | null = null;\n  private lastSelected: boolean;\n  private firstUpdate = true;\n\n  /**\n   * The recommended role of the select option.\n   */\n  get role() {\n    return this.menuItemController.role;\n  }\n\n  /**\n   * The text that is selectable via typeahead. If not set, defaults to the\n   * innerText of the item slotted into the `\"headline\"` slot, and if there are\n   * no slotted elements into headline, then it checks the _default_ slot, and\n   * then the `\"supporting-text\"` slot if nothing is in _default_.\n   */\n  get typeaheadText() {\n    return this.menuItemController.typeaheadText;\n  }\n\n  setTypeaheadText(text: string) {\n    this.menuItemController.setTypeaheadText(text);\n  }\n\n  /**\n   * The text that is displayed in the select field when selected. If not set,\n   * defaults to the textContent of the item slotted into the `\"headline\"` slot,\n   * and if there are no slotted elements into headline, then it checks the\n   * _default_ slot, and then the `\"supporting-text\"` slot if nothing is in\n   * _default_.\n   */\n  get displayText() {\n    if (this.internalDisplayText !== null) {\n      return this.internalDisplayText;\n    }\n\n    return this.menuItemController.typeaheadText;\n  }\n\n  setDisplayText(text: string) {\n    this.internalDisplayText = text;\n  }\n\n  /**\n   * @param host The SelectOption in which to attach this controller to.\n   * @param config The object that configures this controller's behavior.\n   */\n  constructor(\n    private readonly host: ReactiveControllerHost & SelectOption,\n    config: SelectOptionConfig,\n  ) {\n    this.lastSelected = this.host.selected;\n    this.menuItemController = new MenuItemController(host, config);\n    host.addController(this);\n  }\n\n  hostUpdate() {\n    if (this.lastSelected !== this.host.selected) {\n      this.host.ariaSelected = this.host.selected ? 'true' : 'false';\n    }\n  }\n\n  hostUpdated() {\n    // Do not dispatch event on first update / boot-up.\n    if (this.lastSelected !== this.host.selected && !this.firstUpdate) {\n      // This section is really useful for when the user sets selected on the\n      // option programmatically. Most other cases (click and keyboard) are\n      // handled by md-select because it needs to coordinate the\n      // single-selection behavior.\n      if (this.host.selected) {\n        this.host.dispatchEvent(createRequestSelectionEvent());\n      } else {\n        this.host.dispatchEvent(createRequestDeselectionEvent());\n      }\n    }\n\n    this.lastSelected = this.host.selected;\n    this.firstUpdate = false;\n  }\n\n  /**\n   * Bind this click listener to the interactive element. Handles closing the\n   * menu.\n   */\n  onClick = () => {\n    this.menuItemController.onClick();\n  };\n\n  /**\n   * Bind this click listener to the interactive element. Handles closing the\n   * menu.\n   */\n  onKeydown = (e: KeyboardEvent) => {\n    this.menuItemController.onKeydown(e);\n  };\n}\n", "/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport '../../../focus/md-focus-ring.js';\nimport '../../../labs/item/item.js';\nimport '../../../ripple/ripple.js';\n\nimport {html, LitElement, nothing} from 'lit';\nimport {\n  property,\n  query,\n  queryAssignedElements,\n  queryAssignedNodes,\n} from 'lit/decorators.js';\nimport {ClassInfo, classMap} from 'lit/directives/class-map.js';\n\nimport {ARIAMixinStrict} from '../../../internal/aria/aria.js';\nimport {mixinDelegatesAria} from '../../../internal/aria/delegate.js';\nimport {MenuItem} from '../../../menu/internal/controllers/menuItemController.js';\n\nimport {SelectOptionController} from './selectOptionController.js';\n\n/**\n * The interface specific to a Select Option\n */\ninterface SelectOptionSelf {\n  /**\n   * The form value associated with the Select Option. (Note: the visual portion\n   * of the SelectOption is the headline defined in ListItem)\n   */\n  value: string;\n  /**\n   * Whether or not the SelectOption is selected.\n   */\n  selected: boolean;\n  /**\n   * The text to display in the select when selected. Defaults to the\n   * textContent of the Element slotted into the headline.\n   */\n  displayText: string;\n}\n\n/**\n * The interface to implement for a select option. Additionally, the element\n * must have `md-list-item` and `md-menu-item` attributes on the host.\n */\nexport type SelectOption = SelectOptionSelf & MenuItem;\n\n// Separate variable needed for closure.\nconst selectOptionBaseClass = mixinDelegatesAria(LitElement);\n\n/**\n * @fires close-menu {CustomEvent<{initiator: SelectOption, reason: Reason, itemPath: SelectOption[]}>}\n * Closes the encapsulating menu on closable interaction. --bubbles --composed\n * @fires request-selection {Event} Requests the parent md-select to select this\n * element (and deselect others if single-selection) when `selected` changed to\n * `true`. --bubbles --composed\n * @fires request-deselection {Event} Requests the parent md-select to deselect\n * this element when `selected` changed to `false`. --bubbles --composed\n */\nexport class SelectOptionEl\n  extends selectOptionBaseClass\n  implements SelectOption\n{\n  /** @nocollapse */\n  static override shadowRootOptions = {\n    ...LitElement.shadowRootOptions,\n    delegatesFocus: true,\n  };\n\n  /**\n   * Disables the item and makes it non-selectable and non-interactive.\n   */\n  @property({type: Boolean, reflect: true}) disabled = false;\n\n  /**\n   * READONLY: self-identifies as a menu item and sets its identifying attribute\n   */\n  @property({type: Boolean, attribute: 'md-menu-item', reflect: true})\n  isMenuItem = true;\n\n  /**\n   * Sets the item in the selected visual state when a submenu is opened.\n   */\n  @property({type: Boolean}) selected = false;\n  /**\n   * Form value of the option.\n   */\n  @property() value = '';\n\n  @query('.list-item') protected readonly listItemRoot!: HTMLElement | null;\n\n  @queryAssignedElements({slot: 'headline'})\n  protected readonly headlineElements!: HTMLElement[];\n  @queryAssignedElements({slot: 'supporting-text'})\n  protected readonly supportingTextElements!: HTMLElement[];\n  @queryAssignedNodes({slot: ''})\n  protected readonly defaultElements!: Element[];\n\n  type = 'option' as const;\n\n  /**\n   * The text that is selectable via typeahead. If not set, defaults to the\n   * innerText of the item slotted into the `\"headline\"` slot.\n   */\n  get typeaheadText() {\n    return this.selectOptionController.typeaheadText;\n  }\n\n  @property({attribute: 'typeahead-text'})\n  set typeaheadText(text: string) {\n    this.selectOptionController.setTypeaheadText(text);\n  }\n\n  /**\n   * The text that is displayed in the select field when selected. If not set,\n   * defaults to the textContent of the item slotted into the `\"headline\"` slot.\n   */\n  get displayText() {\n    return this.selectOptionController.displayText;\n  }\n\n  @property({attribute: 'display-text'})\n  set displayText(text: string) {\n    this.selectOptionController.setDisplayText(text);\n  }\n\n  private readonly selectOptionController = new SelectOptionController(this, {\n    getHeadlineElements: () => {\n      return this.headlineElements;\n    },\n    getSupportingTextElements: () => {\n      return this.supportingTextElements;\n    },\n    getDefaultElements: () => {\n      return this.defaultElements;\n    },\n    getInteractiveElement: () => this.listItemRoot,\n  });\n\n  protected override render() {\n    return this.renderListItem(html`\n      <md-item>\n        <div slot=\"container\">\n          ${this.renderRipple()} ${this.renderFocusRing()}\n        </div>\n        <slot name=\"start\" slot=\"start\"></slot>\n        <slot name=\"end\" slot=\"end\"></slot>\n        ${this.renderBody()}\n      </md-item>\n    `);\n  }\n\n  /**\n   * Renders the root list item.\n   *\n   * @param content the child content of the list item.\n   */\n  protected renderListItem(content: unknown) {\n    return html`\n      <li\n        id=\"item\"\n        tabindex=${this.disabled ? -1 : 0}\n        role=${this.selectOptionController.role}\n        aria-label=${(this as ARIAMixinStrict).ariaLabel || nothing}\n        aria-selected=${(this as ARIAMixinStrict).ariaSelected || nothing}\n        aria-checked=${(this as ARIAMixinStrict).ariaChecked || nothing}\n        aria-expanded=${(this as ARIAMixinStrict).ariaExpanded || nothing}\n        aria-haspopup=${(this as ARIAMixinStrict).ariaHasPopup || nothing}\n        class=\"list-item ${classMap(this.getRenderClasses())}\"\n        @click=${this.selectOptionController.onClick}\n        @keydown=${this.selectOptionController.onKeydown}\n        >${content}</li\n      >\n    `;\n  }\n\n  /**\n   * Handles rendering of the ripple element.\n   */\n  protected renderRipple() {\n    return html` <md-ripple\n      part=\"ripple\"\n      for=\"item\"\n      ?disabled=${this.disabled}></md-ripple>`;\n  }\n\n  /**\n   * Handles rendering of the focus ring.\n   */\n  protected renderFocusRing() {\n    return html` <md-focus-ring\n      part=\"focus-ring\"\n      for=\"item\"\n      inward></md-focus-ring>`;\n  }\n\n  /**\n   * Classes applied to the list item root.\n   */\n  protected getRenderClasses(): ClassInfo {\n    return {\n      'disabled': this.disabled,\n      'selected': this.selected,\n    };\n  }\n\n  /**\n   * Handles rendering the headline and supporting text.\n   */\n  protected renderBody() {\n    return html`\n      <slot></slot>\n      <slot name=\"overline\" slot=\"overline\"></slot>\n      <slot name=\"headline\" slot=\"headline\"></slot>\n      <slot name=\"supporting-text\" slot=\"supporting-text\"></slot>\n      <slot\n        name=\"trailing-supporting-text\"\n        slot=\"trailing-supporting-text\"></slot>\n    `;\n  }\n\n  override focus() {\n    // TODO(b/300334509): needed for some cases where delegatesFocus doesn't\n    // work programmatically like in FF and select-option\n    this.listItemRoot?.focus();\n  }\n}\n", "/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {styles} from '../menu/internal/menuitem/menu-item-styles.js';\n\nimport {SelectOptionEl} from './internal/selectoption/select-option.js';\n\nexport {type SelectOption} from './internal/selectoption/select-option.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-select-option': MdSelectOption;\n  }\n}\n\n/**\n * @summary\n * Select menus display a list of choices on temporary surfaces and display the\n * currently selected menu item above the menu.\n *\n * @description\n * The select component allows users to choose a value from a fixed list of\n * available options. Composed of an interactive anchor button and a menu, it is\n * analogous to the native HTML `<select>` element. This is the option that\n * can be placed inside of an md-select.\n *\n * This component is a subclass of `md-menu-item` and can accept the same slots,\n * properties, and events as `md-menu-item`.\n *\n * @example\n * ```html\n * <md-outlined-select label=\"fruits\">\n *   <!-- An empty selected option will give select an \"un-filled\" state -->\n *   <md-select-option selected></md-select-option>\n *   <md-select-option value=\"apple\" headline=\"Apple\"></md-select-option>\n *   <md-select-option value=\"banana\" headline=\"Banana\"></md-select-option>\n *   <md-select-option value=\"kiwi\" headline=\"Kiwi\"></md-select-option>\n *   <md-select-option value=\"orange\" headline=\"Orange\"></md-select-option>\n *   <md-select-option value=\"tomato\" headline=\"Tomato\"></md-select-option>\n * </md-outlined-select>\n * ```\n *\n * @final\n * @suppress {visibility}\n */\n@customElement('md-select-option')\nexport class MdSelectOption extends SelectOptionEl {\n  static override styles: CSSResultOrNative[] = [styles];\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAkBM,SAAU,8BAA2B;AACzC,SAAO,IAAI,MAAM,qBAAqB;IACpC,SAAS;IACT,UAAU;GACX;AACH;AAMM,SAAU,gCAA6B;AAC3C,SAAO,IAAI,MAAM,uBAAuB;IACtC,SAAS;IACT,UAAU;GACX;AACH;AAWM,IAAO,yBAAP,MAA6B;;;;EASjC,IAAI,OAAI;AACN,WAAO,KAAK,mBAAmB;EACjC;;;;;;;EAQA,IAAI,gBAAa;AACf,WAAO,KAAK,mBAAmB;EACjC;EAEA,iBAAiB,MAAY;AAC3B,SAAK,mBAAmB,iBAAiB,IAAI;EAC/C;;;;;;;;EASA,IAAI,cAAW;AACb,QAAI,KAAK,wBAAwB,MAAM;AACrC,aAAO,KAAK;IACd;AAEA,WAAO,KAAK,mBAAmB;EACjC;EAEA,eAAe,MAAY;AACzB,SAAK,sBAAsB;EAC7B;;;;;EAMA,YACmB,MACjB,QAA0B;AADT,SAAA,OAAA;AAjDX,SAAA,sBAAqC;AAErC,SAAA,cAAc;AAmFtB,SAAA,UAAU,MAAK;AACb,WAAK,mBAAmB,QAAO;IACjC;AAMA,SAAA,YAAY,CAAC,MAAoB;AAC/B,WAAK,mBAAmB,UAAU,CAAC;IACrC;AA3CE,SAAK,eAAe,KAAK,KAAK;AAC9B,SAAK,qBAAqB,IAAI,mBAAmB,MAAM,MAAM;AAC7D,SAAK,cAAc,IAAI;EACzB;EAEA,aAAU;AACR,QAAI,KAAK,iBAAiB,KAAK,KAAK,UAAU;AAC5C,WAAK,KAAK,eAAe,KAAK,KAAK,WAAW,SAAS;IACzD;EACF;EAEA,cAAW;AAET,QAAI,KAAK,iBAAiB,KAAK,KAAK,YAAY,CAAC,KAAK,aAAa;AAKjE,UAAI,KAAK,KAAK,UAAU;AACtB,aAAK,KAAK,cAAc,4BAA2B,CAAE;MACvD,OAAO;AACL,aAAK,KAAK,cAAc,8BAA6B,CAAE;MACzD;IACF;AAEA,SAAK,eAAe,KAAK,KAAK;AAC9B,SAAK,cAAc;EACrB;;;;AC1EF,IAAM,wBAAwB,mBAAmB,UAAU;AAWrD,IAAO,iBAAP,cACI,sBAAqB;EAD/B,cAAA;;AAa4C,SAAA,WAAW;AAMrD,SAAA,aAAa;AAKc,SAAA,WAAW;AAI1B,SAAA,QAAQ;AAWpB,SAAA,OAAO;AA4BU,SAAA,yBAAyB,IAAI,uBAAuB,MAAM;MACzE,qBAAqB,MAAK;AACxB,eAAO,KAAK;MACd;MACA,2BAA2B,MAAK;AAC9B,eAAO,KAAK;MACd;MACA,oBAAoB,MAAK;AACvB,eAAO,KAAK;MACd;MACA,uBAAuB,MAAM,KAAK;KACnC;EAyFH;;;;;EA1HE,IAAI,gBAAa;AACf,WAAO,KAAK,uBAAuB;EACrC;EAGA,IAAI,cAAc,MAAY;AAC5B,SAAK,uBAAuB,iBAAiB,IAAI;EACnD;;;;;EAMA,IAAI,cAAW;AACb,WAAO,KAAK,uBAAuB;EACrC;EAGA,IAAI,YAAY,MAAY;AAC1B,SAAK,uBAAuB,eAAe,IAAI;EACjD;EAemB,SAAM;AACvB,WAAO,KAAK,eAAe;;;YAGnB,KAAK,aAAY,CAAE,IAAI,KAAK,gBAAe,CAAE;;;;UAI/C,KAAK,WAAU,CAAE;;KAEtB;EACH;;;;;;EAOU,eAAe,SAAgB;AACvC,WAAO;;;mBAGQ,KAAK,WAAW,KAAK,CAAC;eAC1B,KAAK,uBAAuB,IAAI;qBACzB,KAAyB,aAAa,OAAO;wBAC1C,KAAyB,gBAAgB,OAAO;uBACjD,KAAyB,eAAe,OAAO;wBAC9C,KAAyB,gBAAgB,OAAO;wBAChD,KAAyB,gBAAgB,OAAO;2BAC9C,SAAS,KAAK,iBAAgB,CAAE,CAAC;iBAC3C,KAAK,uBAAuB,OAAO;mBACjC,KAAK,uBAAuB,SAAS;WAC7C,OAAO;;;EAGhB;;;;EAKU,eAAY;AACpB,WAAO;;;kBAGO,KAAK,QAAQ;EAC7B;;;;EAKU,kBAAe;AACvB,WAAO;;;;EAIT;;;;EAKU,mBAAgB;AACxB,WAAO;MACL,YAAY,KAAK;MACjB,YAAY,KAAK;;EAErB;;;;EAKU,aAAU;AAClB,WAAO;;;;;;;;;EAST;EAES,QAAK;AAGZ,SAAK,cAAc,MAAK;EAC1B;;AAjKgB,eAAA,oBAAoB;EAClC,GAAG,WAAW;EACd,gBAAgB;;AAMwB,WAAA;EAAzC,SAAS,EAAC,MAAM,SAAS,SAAS,KAAI,CAAC;;AAMxC,WAAA;EADC,SAAS,EAAC,MAAM,SAAS,WAAW,gBAAgB,SAAS,KAAI,CAAC;;AAMxC,WAAA;EAA1B,SAAS,EAAC,MAAM,QAAO,CAAC;;AAIb,WAAA;EAAX,SAAQ;;AAE+B,WAAA;EAAvC,MAAM,YAAY;;AAGA,WAAA;EADlB,sBAAsB,EAAC,MAAM,WAAU,CAAC;;AAGtB,WAAA;EADlB,sBAAsB,EAAC,MAAM,kBAAiB,CAAC;;AAG7B,WAAA;EADlB,mBAAmB,EAAC,MAAM,GAAE,CAAC;;AAc9B,WAAA;EADC,SAAS,EAAC,WAAW,iBAAgB,CAAC;;AAcvC,WAAA;EADC,SAAS,EAAC,WAAW,eAAc,CAAC;;;;ACzEhC,IAAM,iBAAN,MAAMA,wBAAuB,eAAc;;AAChC,eAAA,SAA8B,CAAC,MAAM;AAD1C,iBAAc,WAAA;EAD1B,cAAc,kBAAkB;GACpB,cAAc;", "names": ["MdSelectOption"]}