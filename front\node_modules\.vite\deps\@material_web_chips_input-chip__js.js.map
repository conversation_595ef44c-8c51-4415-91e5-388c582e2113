{"version": 3, "sources": ["../../@material/web/chips/internal/input-chip.ts", "../../@material/web/chips/internal/input-styles.ts", "../../@material/web/chips/input-chip.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {html, nothing} from 'lit';\nimport {property, query} from 'lit/decorators.js';\n\nimport {ARIAMixinStrict} from '../../internal/aria/aria.js';\n\nimport {MultiActionChip} from './multi-action-chip.js';\nimport {renderRemoveButton} from './trailing-icons.js';\n\n/**\n * An input chip component.\n *\n * @fires remove {Event} Dispatched when the remove button is clicked.\n */\nexport class InputChip extends MultiActionChip {\n  @property({type: Boolean}) avatar = false;\n  @property() href = '';\n  @property() target: '_blank' | '_parent' | '_self' | '_top' | '' = '';\n  @property({type: Boolean, attribute: 'remove-only'}) removeOnly = false;\n  @property({type: <PERSON>olean, reflect: true}) selected = false;\n\n  protected get primaryId() {\n    if (this.href) {\n      return 'link';\n    }\n\n    if (this.removeOnly) {\n      return '';\n    }\n\n    return 'button';\n  }\n\n  protected override get rippleDisabled() {\n    // Link chips cannot be disabled\n    return !this.href && (this.disabled || this.softDisabled);\n  }\n\n  protected get primaryAction() {\n    // Don't use @query() since a remove-only input chip still has a span that\n    // has \"primary action\" classes.\n    if (this.removeOnly) {\n      return null;\n    }\n\n    return this.renderRoot.querySelector<HTMLElement>('.primary.action');\n  }\n\n  @query('.trailing.action')\n  protected readonly trailingAction!: HTMLElement | null;\n\n  protected override getContainerClasses() {\n    return {\n      ...super.getContainerClasses(),\n      avatar: this.avatar,\n      // Link chips cannot be disabled\n      disabled: !this.href && (this.disabled || this.softDisabled),\n      link: !!this.href,\n      selected: this.selected,\n      'has-trailing': true,\n    };\n  }\n\n  protected override renderPrimaryAction(content: unknown) {\n    const {ariaLabel} = this as ARIAMixinStrict;\n    if (this.href) {\n      return html`\n        <a\n          class=\"primary action\"\n          id=\"link\"\n          aria-label=${ariaLabel || nothing}\n          href=${this.href}\n          target=${this.target || nothing}\n          >${content}</a\n        >\n      `;\n    }\n\n    if (this.removeOnly) {\n      return html`\n        <span class=\"primary action\" aria-label=${ariaLabel || nothing}>\n          ${content}\n        </span>\n      `;\n    }\n\n    return html`\n      <button\n        class=\"primary action\"\n        id=\"button\"\n        aria-label=${ariaLabel || nothing}\n        aria-disabled=${this.softDisabled || nothing}\n        ?disabled=${this.disabled && !this.alwaysFocusable}\n        type=\"button\"\n        >${content}</button\n      >\n    `;\n  }\n\n  protected override renderTrailingAction(focusListener: EventListener) {\n    return renderRemoveButton({\n      focusListener,\n      ariaLabel: this.ariaLabelRemove,\n      disabled: !this.href && (this.disabled || this.softDisabled),\n      tabbable: this.removeOnly,\n    });\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n// Generated stylesheet for ./chips/internal/input-styles.css.\nimport {css} from 'lit';\nexport const styles = css`:host{--_container-height: var(--md-input-chip-container-height, 32px);--_disabled-label-text-color: var(--md-input-chip-disabled-label-text-color, var(--md-sys-color-on-surface, #1d1b20));--_disabled-label-text-opacity: var(--md-input-chip-disabled-label-text-opacity, 0.38);--_disabled-selected-container-color: var(--md-input-chip-disabled-selected-container-color, var(--md-sys-color-on-surface, #1d1b20));--_disabled-selected-container-opacity: var(--md-input-chip-disabled-selected-container-opacity, 0.12);--_label-text-font: var(--md-input-chip-label-text-font, var(--md-sys-typescale-label-large-font, var(--md-ref-typeface-plain, Roboto)));--_label-text-line-height: var(--md-input-chip-label-text-line-height, var(--md-sys-typescale-label-large-line-height, 1.25rem));--_label-text-size: var(--md-input-chip-label-text-size, var(--md-sys-typescale-label-large-size, 0.875rem));--_label-text-weight: var(--md-input-chip-label-text-weight, var(--md-sys-typescale-label-large-weight, var(--md-ref-typeface-weight-medium, 500)));--_selected-container-color: var(--md-input-chip-selected-container-color, var(--md-sys-color-secondary-container, #e8def8));--_selected-focus-label-text-color: var(--md-input-chip-selected-focus-label-text-color, var(--md-sys-color-on-secondary-container, #1d192b));--_selected-hover-label-text-color: var(--md-input-chip-selected-hover-label-text-color, var(--md-sys-color-on-secondary-container, #1d192b));--_selected-hover-state-layer-color: var(--md-input-chip-selected-hover-state-layer-color, var(--md-sys-color-on-secondary-container, #1d192b));--_selected-hover-state-layer-opacity: var(--md-input-chip-selected-hover-state-layer-opacity, 0.08);--_selected-label-text-color: var(--md-input-chip-selected-label-text-color, var(--md-sys-color-on-secondary-container, #1d192b));--_selected-outline-width: var(--md-input-chip-selected-outline-width, 0px);--_selected-pressed-label-text-color: var(--md-input-chip-selected-pressed-label-text-color, var(--md-sys-color-on-secondary-container, #1d192b));--_selected-pressed-state-layer-color: var(--md-input-chip-selected-pressed-state-layer-color, var(--md-sys-color-on-secondary-container, #1d192b));--_selected-pressed-state-layer-opacity: var(--md-input-chip-selected-pressed-state-layer-opacity, 0.12);--_disabled-outline-color: var(--md-input-chip-disabled-outline-color, var(--md-sys-color-on-surface, #1d1b20));--_disabled-outline-opacity: var(--md-input-chip-disabled-outline-opacity, 0.12);--_focus-label-text-color: var(--md-input-chip-focus-label-text-color, var(--md-sys-color-on-surface-variant, #49454f));--_focus-outline-color: var(--md-input-chip-focus-outline-color, var(--md-sys-color-on-surface-variant, #49454f));--_hover-label-text-color: var(--md-input-chip-hover-label-text-color, var(--md-sys-color-on-surface-variant, #49454f));--_hover-state-layer-color: var(--md-input-chip-hover-state-layer-color, var(--md-sys-color-on-surface-variant, #49454f));--_hover-state-layer-opacity: var(--md-input-chip-hover-state-layer-opacity, 0.08);--_label-text-color: var(--md-input-chip-label-text-color, var(--md-sys-color-on-surface-variant, #49454f));--_outline-color: var(--md-input-chip-outline-color, var(--md-sys-color-outline, #79747e));--_outline-width: var(--md-input-chip-outline-width, 1px);--_pressed-label-text-color: var(--md-input-chip-pressed-label-text-color, var(--md-sys-color-on-surface-variant, #49454f));--_pressed-state-layer-color: var(--md-input-chip-pressed-state-layer-color, var(--md-sys-color-on-surface-variant, #49454f));--_pressed-state-layer-opacity: var(--md-input-chip-pressed-state-layer-opacity, 0.12);--_avatar-shape: var(--md-input-chip-avatar-shape, var(--md-sys-shape-corner-full, 9999px));--_avatar-size: var(--md-input-chip-avatar-size, 24px);--_disabled-avatar-opacity: var(--md-input-chip-disabled-avatar-opacity, 0.38);--_disabled-leading-icon-color: var(--md-input-chip-disabled-leading-icon-color, var(--md-sys-color-on-surface, #1d1b20));--_disabled-leading-icon-opacity: var(--md-input-chip-disabled-leading-icon-opacity, 0.38);--_icon-size: var(--md-input-chip-icon-size, 18px);--_selected-focus-leading-icon-color: var(--md-input-chip-selected-focus-leading-icon-color, var(--md-sys-color-primary, #6750a4));--_selected-hover-leading-icon-color: var(--md-input-chip-selected-hover-leading-icon-color, var(--md-sys-color-primary, #6750a4));--_selected-leading-icon-color: var(--md-input-chip-selected-leading-icon-color, var(--md-sys-color-primary, #6750a4));--_selected-pressed-leading-icon-color: var(--md-input-chip-selected-pressed-leading-icon-color, var(--md-sys-color-primary, #6750a4));--_focus-leading-icon-color: var(--md-input-chip-focus-leading-icon-color, var(--md-sys-color-primary, #6750a4));--_hover-leading-icon-color: var(--md-input-chip-hover-leading-icon-color, var(--md-sys-color-primary, #6750a4));--_leading-icon-color: var(--md-input-chip-leading-icon-color, var(--md-sys-color-primary, #6750a4));--_pressed-leading-icon-color: var(--md-input-chip-pressed-leading-icon-color, var(--md-sys-color-primary, #6750a4));--_disabled-trailing-icon-color: var(--md-input-chip-disabled-trailing-icon-color, var(--md-sys-color-on-surface, #1d1b20));--_disabled-trailing-icon-opacity: var(--md-input-chip-disabled-trailing-icon-opacity, 0.38);--_selected-focus-trailing-icon-color: var(--md-input-chip-selected-focus-trailing-icon-color, var(--md-sys-color-on-secondary-container, #1d192b));--_selected-hover-trailing-icon-color: var(--md-input-chip-selected-hover-trailing-icon-color, var(--md-sys-color-on-secondary-container, #1d192b));--_selected-pressed-trailing-icon-color: var(--md-input-chip-selected-pressed-trailing-icon-color, var(--md-sys-color-on-secondary-container, #1d192b));--_selected-trailing-icon-color: var(--md-input-chip-selected-trailing-icon-color, var(--md-sys-color-on-secondary-container, #1d192b));--_focus-trailing-icon-color: var(--md-input-chip-focus-trailing-icon-color, var(--md-sys-color-on-surface-variant, #49454f));--_hover-trailing-icon-color: var(--md-input-chip-hover-trailing-icon-color, var(--md-sys-color-on-surface-variant, #49454f));--_pressed-trailing-icon-color: var(--md-input-chip-pressed-trailing-icon-color, var(--md-sys-color-on-surface-variant, #49454f));--_trailing-icon-color: var(--md-input-chip-trailing-icon-color, var(--md-sys-color-on-surface-variant, #49454f));--_container-shape-start-start: var(--md-input-chip-container-shape-start-start, var(--md-input-chip-container-shape, var(--md-sys-shape-corner-small, 8px)));--_container-shape-start-end: var(--md-input-chip-container-shape-start-end, var(--md-input-chip-container-shape, var(--md-sys-shape-corner-small, 8px)));--_container-shape-end-end: var(--md-input-chip-container-shape-end-end, var(--md-input-chip-container-shape, var(--md-sys-shape-corner-small, 8px)));--_container-shape-end-start: var(--md-input-chip-container-shape-end-start, var(--md-input-chip-container-shape, var(--md-sys-shape-corner-small, 8px)));--_leading-space: var(--md-input-chip-leading-space, 16px);--_trailing-space: var(--md-input-chip-trailing-space, 16px);--_icon-label-space: var(--md-input-chip-icon-label-space, 8px);--_with-leading-icon-leading-space: var(--md-input-chip-with-leading-icon-leading-space, 8px);--_with-trailing-icon-trailing-space: var(--md-input-chip-with-trailing-icon-trailing-space, 8px)}:host([avatar]){--_container-shape-start-start: var( --md-input-chip-container-shape-start-start, var(--md-input-chip-container-shape, calc(var(--_container-height) / 2)) );--_container-shape-start-end: var( --md-input-chip-container-shape-start-end, var(--md-input-chip-container-shape, calc(var(--_container-height) / 2)) );--_container-shape-end-end: var( --md-input-chip-container-shape-end-end, var(--md-input-chip-container-shape, calc(var(--_container-height) / 2)) );--_container-shape-end-start: var( --md-input-chip-container-shape-end-start, var(--md-input-chip-container-shape, calc(var(--_container-height) / 2)) )}.avatar .primary.action{padding-inline-start:4px}.avatar .leading.icon ::slotted(:first-child){border-radius:var(--_avatar-shape);height:var(--_avatar-size);width:var(--_avatar-size)}.disabled.avatar .leading.icon{opacity:var(--_disabled-avatar-opacity)}@media(forced-colors: active){.link .outline{border-color:ActiveText}.disabled.avatar .leading.icon{opacity:1}}\n`;\n", "/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {InputChip} from './internal/input-chip.js';\nimport {styles} from './internal/input-styles.js';\nimport {styles as selectableStyles} from './internal/selectable-styles.js';\nimport {styles as sharedStyles} from './internal/shared-styles.js';\nimport {styles as trailingIconStyles} from './internal/trailing-icon-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-input-chip': MdInputChip;\n  }\n}\n\n/**\n * TODO(b/243982145): add docs\n *\n * @final\n * @suppress {visibility}\n */\n@customElement('md-input-chip')\nexport class MdInputChip extends InputChip {\n  static override styles: CSSResultOrNative[] = [\n    sharedStyles,\n    trailingIconStyles,\n    selectableStyles,\n    styles,\n  ];\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmBM,IAAO,YAAP,cAAyB,gBAAe;EAA9C,cAAA;;AAC6B,SAAA,SAAS;AACxB,SAAA,OAAO;AACP,SAAA,SAAuD;AACd,SAAA,aAAa;AACxB,SAAA,WAAW;EAwFvD;EAtFE,IAAc,YAAS;AACrB,QAAI,KAAK,MAAM;AACb,aAAO;IACT;AAEA,QAAI,KAAK,YAAY;AACnB,aAAO;IACT;AAEA,WAAO;EACT;EAEA,IAAuB,iBAAc;AAEnC,WAAO,CAAC,KAAK,SAAS,KAAK,YAAY,KAAK;EAC9C;EAEA,IAAc,gBAAa;AAGzB,QAAI,KAAK,YAAY;AACnB,aAAO;IACT;AAEA,WAAO,KAAK,WAAW,cAA2B,iBAAiB;EACrE;EAKmB,sBAAmB;AACpC,WAAO;MACL,GAAG,MAAM,oBAAmB;MAC5B,QAAQ,KAAK;;MAEb,UAAU,CAAC,KAAK,SAAS,KAAK,YAAY,KAAK;MAC/C,MAAM,CAAC,CAAC,KAAK;MACb,UAAU,KAAK;MACf,gBAAgB;;EAEpB;EAEmB,oBAAoB,SAAgB;AACrD,UAAM,EAAC,UAAS,IAAI;AACpB,QAAI,KAAK,MAAM;AACb,aAAO;;;;uBAIU,aAAa,OAAO;iBAC1B,KAAK,IAAI;mBACP,KAAK,UAAU,OAAO;aAC5B,OAAO;;;IAGhB;AAEA,QAAI,KAAK,YAAY;AACnB,aAAO;kDACqC,aAAa,OAAO;YAC1D,OAAO;;;IAGf;AAEA,WAAO;;;;qBAIU,aAAa,OAAO;wBACjB,KAAK,gBAAgB,OAAO;oBAChC,KAAK,YAAY,CAAC,KAAK,eAAe;;WAE/C,OAAO;;;EAGhB;EAEmB,qBAAqB,eAA4B;AAClE,WAAO,mBAAmB;MACxB;MACA,WAAW,KAAK;MAChB,UAAU,CAAC,KAAK,SAAS,KAAK,YAAY,KAAK;MAC/C,UAAU,KAAK;KAChB;EACH;;AA3F2B,WAAA;EAA1B,SAAS,EAAC,MAAM,QAAO,CAAC;;AACb,WAAA;EAAX,SAAQ;;AACG,WAAA;EAAX,SAAQ;;AAC4C,WAAA;EAApD,SAAS,EAAC,MAAM,SAAS,WAAW,cAAa,CAAC;;AACT,WAAA;EAAzC,SAAS,EAAC,MAAM,SAAS,SAAS,KAAI,CAAC;;AA8BrB,WAAA;EADlB,MAAM,kBAAkB;;;;AC9CpB,IAAMA,UAAS;;;;ACqBf,IAAM,cAAN,MAAMC,qBAAoB,UAAS;;AACxB,YAAA,SAA8B;EAC5C;EACAC;EACAA;EACAA;;AALS,cAAW,WAAA;EADvB,cAAc,eAAe;GACjB,WAAW;", "names": ["styles", "MdInputChip", "styles"]}