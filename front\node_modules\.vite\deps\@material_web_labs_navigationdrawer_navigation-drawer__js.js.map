{"version": 3, "sources": ["../../@material/web/labs/navigationdrawer/internal/navigation-drawer.ts", "../../@material/web/labs/navigationdrawer/internal/navigation-drawer-styles.ts", "../../@material/web/labs/navigationdrawer/navigation-drawer.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport '../../../elevation/elevation.js';\n\nimport {html, LitElement, nothing, PropertyValues} from 'lit';\nimport {property} from 'lit/decorators.js';\nimport {classMap} from 'lit/directives/class-map.js';\n\nimport {ARIAMixinStrict} from '../../../internal/aria/aria.js';\nimport {mixinDelegatesAria} from '../../../internal/aria/delegate.js';\n\n// Separate variable needed for closure.\nconst navigationDrawerBaseClass = mixinDelegatesAria(LitElement);\n\n/**\n * b/265346501 - add docs\n *\n * @fires navigation-drawer-changed {CustomEvent<{opened: boolean}>}\n * Dispatched whenever the drawer opens or closes --bubbles --composed\n */\nexport class NavigationDrawer extends navigationDrawerBaseClass {\n  @property({type: Boolean}) opened = false;\n  @property() pivot: 'start' | 'end' = 'end';\n\n  protected override render() {\n    const ariaExpanded = this.opened ? 'true' : 'false';\n    const ariaHidden = !this.opened ? 'true' : 'false';\n    // Needed for closure conformance\n    const {ariaLabel, ariaModal} = this as ARIAMixinStrict;\n    return html`\n      <div\n        aria-expanded=\"${ariaExpanded}\"\n        aria-hidden=\"${ariaHidden}\"\n        aria-label=${ariaLabel || nothing}\n        aria-modal=\"${ariaModal || nothing}\"\n        class=\"md3-navigation-drawer ${this.getRenderClasses()}\"\n        role=\"dialog\">\n        <md-elevation part=\"elevation\"></md-elevation>\n        <div class=\"md3-navigation-drawer__slot-content\">\n          <slot></slot>\n        </div>\n      </div>\n    `;\n  }\n\n  private getRenderClasses() {\n    return classMap({\n      'md3-navigation-drawer--opened': this.opened,\n      'md3-navigation-drawer--pivot-at-start': this.pivot === 'start',\n    });\n  }\n\n  protected override updated(\n    changedProperties: PropertyValues<NavigationDrawer>,\n  ) {\n    if (changedProperties.has('opened')) {\n      setTimeout(() => {\n        this.dispatchEvent(\n          new CustomEvent('navigation-drawer-changed', {\n            detail: {opened: this.opened},\n            bubbles: true,\n            composed: true,\n          }),\n        );\n      }, 250);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n// Generated stylesheet for ./labs/navigationdrawer/internal/navigation-drawer-styles.css.\nimport {css} from 'lit';\nexport const styles = css`:host{--_container-color: var(--md-navigation-drawer-container-color, #fff);--_container-height: var(--md-navigation-drawer-container-height, 100%);--_container-shape: var(--md-navigation-drawer-container-shape, 0 16px 16px 0);--_container-width: var(--md-navigation-drawer-container-width, 360px);--_divider-color: var(--md-navigation-drawer-divider-color, #000);--_modal-container-elevation: var(--md-navigation-drawer-modal-container-elevation, 1);--_standard-container-elevation: var(--md-navigation-drawer-standard-container-elevation, 0);--md-elevation-level: var(--_standard-container-elevation);--md-elevation-shadow-color: var(--_divider-color)}:host{display:flex}.md3-navigation-drawer{inline-size:0;box-sizing:border-box;display:flex;justify-content:flex-end;overflow:hidden;overflow-y:auto;visibility:hidden;transition:inline-size .25s cubic-bezier(0.4, 0, 0.2, 1) 0s,visibility 0s cubic-bezier(0.4, 0, 0.2, 1) .25s}md-elevation{z-index:0}.md3-navigation-drawer--opened{visibility:visible;transition:inline-size .25s cubic-bezier(0.4, 0, 0.2, 1) 0s,visibility 0s cubic-bezier(0.4, 0, 0.2, 1) 0s}.md3-navigation-drawer--pivot-at-start{justify-content:flex-start}.md3-navigation-drawer__slot-content{display:flex;flex-direction:column;position:relative}\n`;\n", "/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {customElement} from 'lit/decorators.js';\n\nimport {NavigationDrawer} from './internal/navigation-drawer.js';\nimport {styles} from './internal/navigation-drawer-styles.js';\nimport {styles as sharedStyles} from './internal/shared-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-navigation-drawer': MdNavigationDrawer;\n  }\n}\n\n/**\n * @final\n * @suppress {visibility}\n */\n@customElement('md-navigation-drawer')\nexport class MdNavigationDrawer extends NavigationDrawer {\n  static override readonly styles = [sharedStyles, styles];\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAgBA,IAAM,4BAA4B,mBAAmB,UAAU;AAQzD,IAAO,mBAAP,cAAgC,0BAAyB;EAA/D,cAAA;;AAC6B,SAAA,SAAS;AACxB,SAAA,QAAyB;EA6CvC;EA3CqB,SAAM;AACvB,UAAM,eAAe,KAAK,SAAS,SAAS;AAC5C,UAAM,aAAa,CAAC,KAAK,SAAS,SAAS;AAE3C,UAAM,EAAC,WAAW,UAAS,IAAI;AAC/B,WAAO;;yBAEc,YAAY;uBACd,UAAU;qBACZ,aAAa,OAAO;sBACnB,aAAa,OAAO;uCACH,KAAK,iBAAgB,CAAE;;;;;;;;EAQ5D;EAEQ,mBAAgB;AACtB,WAAO,SAAS;MACd,iCAAiC,KAAK;MACtC,yCAAyC,KAAK,UAAU;KACzD;EACH;EAEmB,QACjB,mBAAmD;AAEnD,QAAI,kBAAkB,IAAI,QAAQ,GAAG;AACnC,iBAAW,MAAK;AACd,aAAK,cACH,IAAI,YAAY,6BAA6B;UAC3C,QAAQ,EAAC,QAAQ,KAAK,OAAM;UAC5B,SAAS;UACT,UAAU;SACX,CAAC;MAEN,GAAG,GAAG;IACR;EACF;;AA7C2B,WAAA;EAA1B,SAAS,EAAC,MAAM,QAAO,CAAC;;AACb,WAAA;EAAX,SAAQ;;;;ACnBJ,IAAMA,UAAS;;;;ACgBf,IAAM,qBAAN,MAAMC,4BAA2B,iBAAgB;;AAC7B,mBAAA,SAAS,CAAC,QAAcC,OAAM;AAD5C,qBAAkB,WAAA;EAD9B,cAAc,sBAAsB;GACxB,kBAAkB;", "names": ["styles", "MdNavigationDrawer", "styles"]}