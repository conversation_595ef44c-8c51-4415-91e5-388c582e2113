{"version": 3, "sources": ["../../@material/web/elevation/internal/elevation.ts", "../../@material/web/elevation/internal/elevation-styles.ts", "../../@material/web/elevation/elevation.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2022 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {html, LitElement} from 'lit';\n\n/**\n * A component for elevation.\n */\nexport class Elevation extends LitElement {\n  override connectedCallback() {\n    super.connectedCallback();\n    // Needed for VoiceOver, which will create a \"group\" if the element is a\n    // sibling to other content.\n    this.setAttribute('aria-hidden', 'true');\n  }\n\n  protected override render() {\n    return html`<span class=\"shadow\"></span>`;\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n// Generated stylesheet for ./elevation/internal/elevation-styles.css.\nimport {css} from 'lit';\nexport const styles = css`:host,.shadow,.shadow::before,.shadow::after{border-radius:inherit;inset:0;position:absolute;transition-duration:inherit;transition-property:inherit;transition-timing-function:inherit}:host{display:flex;pointer-events:none;transition-property:box-shadow,opacity}.shadow::before,.shadow::after{content:\"\";transition-property:box-shadow,opacity;--_level: var(--md-elevation-level, 0);--_shadow-color: var(--md-elevation-shadow-color, var(--md-sys-color-shadow, #000))}.shadow::before{box-shadow:0px calc(1px*(clamp(0,var(--_level),1) + clamp(0,var(--_level) - 3,1) + 2*clamp(0,var(--_level) - 4,1))) calc(1px*(2*clamp(0,var(--_level),1) + clamp(0,var(--_level) - 2,1) + clamp(0,var(--_level) - 4,1))) 0px var(--_shadow-color);opacity:.3}.shadow::after{box-shadow:0px calc(1px*(clamp(0,var(--_level),1) + clamp(0,var(--_level) - 1,1) + 2*clamp(0,var(--_level) - 2,3))) calc(1px*(3*clamp(0,var(--_level),2) + 2*clamp(0,var(--_level) - 2,3))) calc(1px*(clamp(0,var(--_level),4) + 2*clamp(0,var(--_level) - 4,1))) var(--_shadow-color);opacity:.15}\n`;\n", "/**\n * @license\n * Copyright 2022 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {Elevation} from './internal/elevation.js';\nimport {styles} from './internal/elevation-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-elevation': MdElevation;\n  }\n}\n\n/**\n * The `<md-elevation>` custom element with default styles.\n *\n * Elevation is the relative distance between two surfaces along the z-axis.\n *\n * @final\n * @suppress {visibility}\n */\n@customElement('md-elevation')\nexport class MdElevation extends Elevation {\n  static override styles: CSSResultOrNative[] = [styles];\n}\n"], "mappings": ";;;;;;;;;;;AAWM,IAAO,YAAP,cAAyB,WAAU;EAC9B,oBAAiB;AACxB,UAAM,kBAAiB;AAGvB,SAAK,aAAa,eAAe,MAAM;EACzC;EAEmB,SAAM;AACvB,WAAO;EACT;;;;ACdK,IAAM,SAAS;;;;ACoBf,IAAM,cAAN,MAAMA,qBAAoB,UAAS;;AACxB,YAAA,SAA8B,CAAC,MAAM;AAD1C,cAAW,WAAA;EADvB,cAAc,cAAc;GAChB,WAAW;", "names": ["MdElevation"]}