{"version": 3, "sources": ["../../@material/web/internal/events/dispatch-hooks.ts", "../../@material/web/switch/internal/switch.ts", "../../@material/web/switch/internal/switch-styles.ts", "../../@material/web/switch/switch.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\n/**\n * A symbol used to access dispatch hooks on an event.\n */\nconst dispatchHooks = Symbol('dispatchHooks');\n\n/**\n * An `Event` with additional symbols for dispatch hooks.\n */\ninterface EventWithDispatchHooks extends Event {\n  [dispatchHooks]: EventTarget;\n}\n\n/**\n * Add a hook for an event that is called after the event is dispatched and\n * propagates to other event listeners.\n *\n * This is useful for behaviors that need to check if an event is canceled.\n *\n * The callback is invoked synchronously, which allows for better integration\n * with synchronous platform APIs (like `<form>` or `<label>` clicking).\n *\n * Note: `setupDispatchHooks()` must be called on the element before adding any\n * other event listeners. Call it in the constructor of an element or\n * controller.\n *\n * @example\n * ```ts\n * class MyControl extends LitElement {\n *   constructor() {\n *     super();\n *     setupDispatchHooks(this, 'click');\n *     this.addEventListener('click', event => {\n *       afterDispatch(event, () => {\n *         if (event.defaultPrevented) {\n *           return\n *         }\n *\n *         // ... perform logic\n *       });\n *     });\n *   }\n * }\n * ```\n *\n * @example\n * ```ts\n * class MyController implements ReactiveController {\n *   constructor(host: ReactiveElement) {\n *     // setupDispatchHooks() may be called multiple times for the same\n *     // element and events, making it safe for multiple controllers to use it.\n *     setupDispatchHooks(host, 'click');\n *     host.addEventListener('click', event => {\n *       afterDispatch(event, () => {\n *         if (event.defaultPrevented) {\n *           return;\n *         }\n *\n *         // ... perform logic\n *       });\n *     });\n *   }\n * }\n * ```\n *\n * @param event The event to add a hook to.\n * @param callback A hook that is called after the event finishes dispatching.\n */\nexport function afterDispatch(event: Event, callback: () => void) {\n  const hooks = (event as EventWithDispatchHooks)[dispatchHooks];\n  if (!hooks) {\n    throw new Error(`'${event.type}' event needs setupDispatchHooks().`);\n  }\n\n  hooks.addEventListener('after', callback);\n}\n\n/**\n * A lookup map of elements and event types that have a dispatch hook listener\n * set up. Used to ensure we don't set up multiple hook listeners on the same\n * element for the same event.\n */\nconst ELEMENT_DISPATCH_HOOK_TYPES = new WeakMap<Element, Set<string>>();\n\n/**\n * Sets up an element to add dispatch hooks to given event types. This must be\n * called before adding any event listeners that need to use dispatch hooks\n * like `afterDispatch()`.\n *\n * This function is safe to call multiple times with the same element or event\n * types. Call it in the constructor of elements, mixins, and controllers to\n * ensure it is set up before external listeners.\n *\n * @example\n * ```ts\n * class MyControl extends LitElement {\n *   constructor() {\n *     super();\n *     setupDispatchHooks(this, 'click');\n *     this.addEventListener('click', this.listenerUsingAfterDispatch);\n *   }\n * }\n * ```\n *\n * @param element The element to set up event dispatch hooks for.\n * @param eventTypes The event types to add dispatch hooks to.\n */\nexport function setupDispatchHooks(\n  element: Element,\n  ...eventTypes: [string, ...string[]]\n) {\n  let typesAlreadySetUp = ELEMENT_DISPATCH_HOOK_TYPES.get(element);\n  if (!typesAlreadySetUp) {\n    typesAlreadySetUp = new Set();\n    ELEMENT_DISPATCH_HOOK_TYPES.set(element, typesAlreadySetUp);\n  }\n\n  for (const eventType of eventTypes) {\n    // Don't register multiple dispatch hook listeners. A second registration\n    // would lead to the second listener re-dispatching a re-dispatched event,\n    // which can cause an infinite loop inside the other one.\n    if (typesAlreadySetUp.has(eventType)) {\n      continue;\n    }\n\n    // When we re-dispatch the event, it's going to immediately trigger this\n    // listener again. Use a flag to ignore it.\n    let isRedispatching = false;\n    element.addEventListener(\n      eventType,\n      (event: Event) => {\n        if (isRedispatching) {\n          return;\n        }\n\n        // Do not let the event propagate to any other listener (not just\n        // bubbling listeners with `stopPropagation()`).\n        event.stopImmediatePropagation();\n        // Make a copy.\n        const eventCopy = Reflect.construct(event.constructor, [\n          event.type,\n          event,\n        ]);\n\n        // Add hooks onto the event.\n        const hooks = new EventTarget();\n        (eventCopy as EventWithDispatchHooks)[dispatchHooks] = hooks;\n\n        // Re-dispatch the event. We can't reuse `redispatchEvent()` since we\n        // need to add the hooks to the copy before it's dispatched.\n        isRedispatching = true;\n        const dispatched = element.dispatchEvent(eventCopy);\n        isRedispatching = false;\n        if (!dispatched) {\n          event.preventDefault();\n        }\n\n        // Synchronously call afterDispatch() hooks.\n        hooks.dispatchEvent(new Event('after'));\n      },\n      {\n        // Ensure this listener runs before other listeners.\n        // `setupDispatchHooks()` should be called in constructors to also\n        // ensure they run before any other externally-added capture listeners.\n        capture: true,\n      },\n    );\n\n    typesAlreadySetUp.add(eventType);\n  }\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport '../../focus/md-focus-ring.js';\nimport '../../ripple/ripple.js';\n\nimport {html, isServer, LitElement, nothing, TemplateResult} from 'lit';\nimport {property, query} from 'lit/decorators.js';\nimport {ClassInfo, classMap} from 'lit/directives/class-map.js';\n\nimport {mixinDelegatesAria} from '../../internal/aria/delegate.js';\nimport {\n  afterDispatch,\n  setupDispatchHooks,\n} from '../../internal/events/dispatch-hooks.js';\nimport {\n  dispatchActivationClick,\n  isActivationClick,\n} from '../../internal/events/form-label-activation.js';\nimport {redispatchEvent} from '../../internal/events/redispatch-event.js';\nimport {\n  createValidator,\n  getValidityAnchor,\n  mixinConstraintValidation,\n} from '../../labs/behaviors/constraint-validation.js';\nimport {mixinElementInternals} from '../../labs/behaviors/element-internals.js';\nimport {\n  getFormState,\n  getFormValue,\n  mixinFormAssociated,\n} from '../../labs/behaviors/form-associated.js';\nimport {CheckboxValidator} from '../../labs/behaviors/validators/checkbox-validator.js';\n\n// Separate variable needed for closure.\nconst switchBaseClass = mixinDelegatesAria(\n  mixinConstraintValidation(\n    mixinFormAssociated(mixinElementInternals(LitElement)),\n  ),\n);\n\n/**\n * @fires input {InputEvent} Fired whenever `selected` changes due to user\n * interaction (bubbles and composed).\n * @fires change {Event} Fired whenever `selected` changes due to user\n * interaction (bubbles).\n */\nexport class Switch extends switchBaseClass {\n  /** @nocollapse */\n  static override shadowRootOptions: ShadowRootInit = {\n    mode: 'open',\n    delegatesFocus: true,\n  };\n\n  /**\n   * Puts the switch in the selected state and sets the form submission value to\n   * the `value` property.\n   */\n  @property({type: Boolean}) selected = false;\n\n  /**\n   * Shows both the selected and deselected icons.\n   */\n  @property({type: Boolean}) icons = false;\n\n  /**\n   * Shows only the selected icon, and not the deselected icon. If `true`,\n   * overrides the behavior of the `icons` property.\n   */\n  @property({type: Boolean, attribute: 'show-only-selected-icon'})\n  showOnlySelectedIcon = false;\n\n  /**\n   * When true, require the switch to be selected when participating in\n   * form submission.\n   *\n   * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input/checkbox#validation\n   */\n  @property({type: Boolean}) required = false;\n\n  /**\n   * The value associated with this switch on form submission. `null` is\n   * submitted when `selected` is `false`.\n   */\n  @property() value = 'on';\n\n  @query('input') private readonly input!: HTMLInputElement | null;\n\n  constructor() {\n    super();\n    if (isServer) {\n      return;\n    }\n\n    // This click listener does not currently need dispatch hooks since it does\n    // not check `event.defaultPrevented`.\n    this.addEventListener('click', (event: MouseEvent) => {\n      if (!isActivationClick(event) || !this.input) {\n        return;\n      }\n      this.focus();\n      dispatchActivationClick(this.input);\n    });\n\n    // Add the aria keyboard interaction pattern for switch and the Enter key.\n    // See https://www.w3.org/WAI/ARIA/apg/patterns/switch/.\n    setupDispatchHooks(this, 'keydown');\n    this.addEventListener('keydown', (event: KeyboardEvent) => {\n      afterDispatch(event, () => {\n        const ignoreEvent = event.defaultPrevented || event.key !== 'Enter';\n        if (ignoreEvent || this.disabled || !this.input) {\n          return;\n        }\n\n        this.input.click();\n      });\n    });\n  }\n\n  protected override render(): TemplateResult {\n    return html`\n      <div class=\"switch ${classMap(this.getRenderClasses())}\">\n        <input\n          id=\"switch\"\n          class=\"touch\"\n          type=\"checkbox\"\n          role=\"switch\"\n          aria-label=${(this as ARIAMixin).ariaLabel || nothing}\n          ?checked=${this.selected}\n          ?disabled=${this.disabled}\n          ?required=${this.required}\n          @input=${this.handleInput}\n          @change=${this.handleChange} />\n\n        <md-focus-ring part=\"focus-ring\" for=\"switch\"></md-focus-ring>\n        <span class=\"track\"> ${this.renderHandle()} </span>\n      </div>\n    `;\n  }\n\n  private getRenderClasses(): ClassInfo {\n    return {\n      'selected': this.selected,\n      'unselected': !this.selected,\n      'disabled': this.disabled,\n    };\n  }\n\n  private renderHandle() {\n    const classes = {\n      'with-icon': this.showOnlySelectedIcon ? this.selected : this.icons,\n    };\n    return html`\n      ${this.renderTouchTarget()}\n      <span class=\"handle-container\">\n        <md-ripple for=\"switch\" ?disabled=\"${this.disabled}\"></md-ripple>\n        <span class=\"handle ${classMap(classes)}\">\n          ${this.shouldShowIcons() ? this.renderIcons() : html``}\n        </span>\n      </span>\n    `;\n  }\n\n  private renderIcons() {\n    return html`\n      <div class=\"icons\">\n        ${this.renderOnIcon()}\n        ${this.showOnlySelectedIcon ? html`` : this.renderOffIcon()}\n      </div>\n    `;\n  }\n\n  /**\n   * https://fonts.google.com/icons?selected=Material%20Symbols%20Outlined%3Acheck%3AFILL%400%3Bwght%40500%3BGRAD%400%3Bopsz%4024\n   */\n  private renderOnIcon() {\n    return html`\n      <slot class=\"icon icon--on\" name=\"on-icon\">\n        <svg viewBox=\"0 0 24 24\">\n          <path\n            d=\"M9.55 18.2 3.65 12.3 5.275 10.675 9.55 14.95 18.725 5.775 20.35 7.4Z\" />\n        </svg>\n      </slot>\n    `;\n  }\n\n  /**\n   * https://fonts.google.com/icons?selected=Material%20Symbols%20Outlined%3Aclose%3AFILL%400%3Bwght%40500%3BGRAD%400%3Bopsz%4024\n   */\n  private renderOffIcon() {\n    return html`\n      <slot class=\"icon icon--off\" name=\"off-icon\">\n        <svg viewBox=\"0 0 24 24\">\n          <path\n            d=\"M6.4 19.2 4.8 17.6 10.4 12 4.8 6.4 6.4 4.8 12 10.4 17.6 4.8 19.2 6.4 13.6 12 19.2 17.6 17.6 19.2 12 13.6Z\" />\n        </svg>\n      </slot>\n    `;\n  }\n\n  private renderTouchTarget() {\n    return html`<span class=\"touch\"></span>`;\n  }\n\n  private shouldShowIcons(): boolean {\n    return this.icons || this.showOnlySelectedIcon;\n  }\n\n  private handleInput(event: Event) {\n    const target = event.target as HTMLInputElement;\n    this.selected = target.checked;\n    // <input> 'input' event bubbles and is composed, don't re-dispatch it.\n  }\n\n  private handleChange(event: Event) {\n    // <input> 'change' event is not composed, re-dispatch it.\n    redispatchEvent(this, event);\n  }\n\n  // Writable mixin properties for lit-html binding, needed for lit-analyzer\n  declare disabled: boolean;\n  declare name: string;\n\n  override [getFormValue]() {\n    return this.selected ? this.value : null;\n  }\n\n  override [getFormState]() {\n    return String(this.selected);\n  }\n\n  override formResetCallback() {\n    // The selected property does not reflect, so the original attribute set by\n    // the user is used to determine the default value.\n    this.selected = this.hasAttribute('selected');\n  }\n\n  override formStateRestoreCallback(state: string) {\n    this.selected = state === 'true';\n  }\n\n  override [createValidator]() {\n    return new CheckboxValidator(() => ({\n      checked: this.selected,\n      required: this.required,\n    }));\n  }\n\n  override [getValidityAnchor]() {\n    return this.input;\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n// Generated stylesheet for ./switch/internal/switch-styles.css.\nimport {css} from 'lit';\nexport const styles = css`@layer styles, hcm;@layer styles{:host{display:inline-flex;outline:none;vertical-align:top;-webkit-tap-highlight-color:rgba(0,0,0,0);cursor:pointer}:host([disabled]){cursor:default}:host([touch-target=wrapper]){margin:max(0px,(48px - var(--md-switch-track-height, 32px))/2) 0px}md-focus-ring{--md-focus-ring-shape-start-start: var(--md-switch-track-shape-start-start, var(--md-switch-track-shape, var(--md-sys-shape-corner-full, 9999px)));--md-focus-ring-shape-start-end: var(--md-switch-track-shape-start-end, var(--md-switch-track-shape, var(--md-sys-shape-corner-full, 9999px)));--md-focus-ring-shape-end-end: var(--md-switch-track-shape-end-end, var(--md-switch-track-shape, var(--md-sys-shape-corner-full, 9999px)));--md-focus-ring-shape-end-start: var(--md-switch-track-shape-end-start, var(--md-switch-track-shape, var(--md-sys-shape-corner-full, 9999px)))}.switch{align-items:center;display:inline-flex;flex-shrink:0;position:relative;width:var(--md-switch-track-width, 52px);height:var(--md-switch-track-height, 32px);border-start-start-radius:var(--md-switch-track-shape-start-start, var(--md-switch-track-shape, var(--md-sys-shape-corner-full, 9999px)));border-start-end-radius:var(--md-switch-track-shape-start-end, var(--md-switch-track-shape, var(--md-sys-shape-corner-full, 9999px)));border-end-end-radius:var(--md-switch-track-shape-end-end, var(--md-switch-track-shape, var(--md-sys-shape-corner-full, 9999px)));border-end-start-radius:var(--md-switch-track-shape-end-start, var(--md-switch-track-shape, var(--md-sys-shape-corner-full, 9999px)))}input{appearance:none;height:max(100%,var(--md-switch-touch-target-size, 48px));outline:none;margin:0;position:absolute;width:max(100%,var(--md-switch-touch-target-size, 48px));z-index:1;cursor:inherit;top:50%;left:50%;transform:translate(-50%, -50%)}:host([touch-target=none]) input{display:none}}@layer styles{.track{position:absolute;width:100%;height:100%;box-sizing:border-box;border-radius:inherit;display:flex;justify-content:center;align-items:center}.track::before{content:\"\";display:flex;position:absolute;height:100%;width:100%;border-radius:inherit;box-sizing:border-box;transition-property:opacity,background-color;transition-timing-function:linear;transition-duration:67ms}.disabled .track{background-color:rgba(0,0,0,0);border-color:rgba(0,0,0,0)}.disabled .track::before,.disabled .track::after{transition:none;opacity:var(--md-switch-disabled-track-opacity, 0.12)}.disabled .track::before{background-clip:content-box}.selected .track::before{background-color:var(--md-switch-selected-track-color, var(--md-sys-color-primary, #6750a4))}.selected:hover .track::before{background-color:var(--md-switch-selected-hover-track-color, var(--md-sys-color-primary, #6750a4))}.selected:focus-within .track::before{background-color:var(--md-switch-selected-focus-track-color, var(--md-sys-color-primary, #6750a4))}.selected:active .track::before{background-color:var(--md-switch-selected-pressed-track-color, var(--md-sys-color-primary, #6750a4))}.selected.disabled .track{background-clip:border-box}.selected.disabled .track::before{background-color:var(--md-switch-disabled-selected-track-color, var(--md-sys-color-on-surface, #1d1b20))}.unselected .track::before{background-color:var(--md-switch-track-color, var(--md-sys-color-surface-container-highest, #e6e0e9));border-color:var(--md-switch-track-outline-color, var(--md-sys-color-outline, #79747e));border-style:solid;border-width:var(--md-switch-track-outline-width, 2px)}.unselected:hover .track::before{background-color:var(--md-switch-hover-track-color, var(--md-sys-color-surface-container-highest, #e6e0e9));border-color:var(--md-switch-hover-track-outline-color, var(--md-sys-color-outline, #79747e))}.unselected:focus-visible .track::before{background-color:var(--md-switch-focus-track-color, var(--md-sys-color-surface-container-highest, #e6e0e9));border-color:var(--md-switch-focus-track-outline-color, var(--md-sys-color-outline, #79747e))}.unselected:active .track::before{background-color:var(--md-switch-pressed-track-color, var(--md-sys-color-surface-container-highest, #e6e0e9));border-color:var(--md-switch-pressed-track-outline-color, var(--md-sys-color-outline, #79747e))}.unselected.disabled .track::before{background-color:var(--md-switch-disabled-track-color, var(--md-sys-color-surface-container-highest, #e6e0e9));border-color:var(--md-switch-disabled-track-outline-color, var(--md-sys-color-on-surface, #1d1b20))}}@layer hcm{@media(forced-colors: active){.selected .track::before{background:ButtonText;border-color:ButtonText}.disabled .track::before{border-color:GrayText;opacity:1}.disabled.selected .track::before{background:GrayText}}}@layer styles{.handle-container{display:flex;place-content:center;place-items:center;position:relative;transition:margin 300ms cubic-bezier(0.175, 0.885, 0.32, 1.275)}.selected .handle-container{margin-inline-start:calc(var(--md-switch-track-width, 52px) - var(--md-switch-track-height, 32px))}.unselected .handle-container{margin-inline-end:calc(var(--md-switch-track-width, 52px) - var(--md-switch-track-height, 32px))}.disabled .handle-container{transition:none}.handle{border-start-start-radius:var(--md-switch-handle-shape-start-start, var(--md-switch-handle-shape, var(--md-sys-shape-corner-full, 9999px)));border-start-end-radius:var(--md-switch-handle-shape-start-end, var(--md-switch-handle-shape, var(--md-sys-shape-corner-full, 9999px)));border-end-end-radius:var(--md-switch-handle-shape-end-end, var(--md-switch-handle-shape, var(--md-sys-shape-corner-full, 9999px)));border-end-start-radius:var(--md-switch-handle-shape-end-start, var(--md-switch-handle-shape, var(--md-sys-shape-corner-full, 9999px)));height:var(--md-switch-handle-height, 16px);width:var(--md-switch-handle-width, 16px);transform-origin:center;transition-property:height,width;transition-duration:250ms,250ms;transition-timing-function:cubic-bezier(0.2, 0, 0, 1),cubic-bezier(0.2, 0, 0, 1);z-index:0}.handle::before{content:\"\";display:flex;inset:0;position:absolute;border-radius:inherit;box-sizing:border-box;transition:background-color 67ms linear}.disabled .handle,.disabled .handle::before{transition:none}.selected .handle{height:var(--md-switch-selected-handle-height, 24px);width:var(--md-switch-selected-handle-width, 24px)}.handle.with-icon{height:var(--md-switch-with-icon-handle-height, 24px);width:var(--md-switch-with-icon-handle-width, 24px)}.selected:not(.disabled):active .handle,.unselected:not(.disabled):active .handle{height:var(--md-switch-pressed-handle-height, 28px);width:var(--md-switch-pressed-handle-width, 28px);transition-timing-function:linear;transition-duration:100ms}.selected .handle::before{background-color:var(--md-switch-selected-handle-color, var(--md-sys-color-on-primary, #fff))}.selected:hover .handle::before{background-color:var(--md-switch-selected-hover-handle-color, var(--md-sys-color-primary-container, #eaddff))}.selected:focus-within .handle::before{background-color:var(--md-switch-selected-focus-handle-color, var(--md-sys-color-primary-container, #eaddff))}.selected:active .handle::before{background-color:var(--md-switch-selected-pressed-handle-color, var(--md-sys-color-primary-container, #eaddff))}.selected.disabled .handle::before{background-color:var(--md-switch-disabled-selected-handle-color, var(--md-sys-color-surface, #fef7ff));opacity:var(--md-switch-disabled-selected-handle-opacity, 1)}.unselected .handle::before{background-color:var(--md-switch-handle-color, var(--md-sys-color-outline, #79747e))}.unselected:hover .handle::before{background-color:var(--md-switch-hover-handle-color, var(--md-sys-color-on-surface-variant, #49454f))}.unselected:focus-within .handle::before{background-color:var(--md-switch-focus-handle-color, var(--md-sys-color-on-surface-variant, #49454f))}.unselected:active .handle::before{background-color:var(--md-switch-pressed-handle-color, var(--md-sys-color-on-surface-variant, #49454f))}.unselected.disabled .handle::before{background-color:var(--md-switch-disabled-handle-color, var(--md-sys-color-on-surface, #1d1b20));opacity:var(--md-switch-disabled-handle-opacity, 0.38)}md-ripple{border-radius:var(--md-switch-state-layer-shape, var(--md-sys-shape-corner-full, 9999px));height:var(--md-switch-state-layer-size, 40px);inset:unset;width:var(--md-switch-state-layer-size, 40px)}.selected md-ripple{--md-ripple-hover-color: var(--md-switch-selected-hover-state-layer-color, var(--md-sys-color-primary, #6750a4));--md-ripple-pressed-color: var(--md-switch-selected-pressed-state-layer-color, var(--md-sys-color-primary, #6750a4));--md-ripple-hover-opacity: var(--md-switch-selected-hover-state-layer-opacity, 0.08);--md-ripple-pressed-opacity: var(--md-switch-selected-pressed-state-layer-opacity, 0.12)}.unselected md-ripple{--md-ripple-hover-color: var(--md-switch-hover-state-layer-color, var(--md-sys-color-on-surface, #1d1b20));--md-ripple-pressed-color: var(--md-switch-pressed-state-layer-color, var(--md-sys-color-on-surface, #1d1b20));--md-ripple-hover-opacity: var(--md-switch-hover-state-layer-opacity, 0.08);--md-ripple-pressed-opacity: var(--md-switch-pressed-state-layer-opacity, 0.12)}}@layer hcm{@media(forced-colors: active){.unselected .handle::before{background:ButtonText}.disabled .handle::before{opacity:1}.disabled.unselected .handle::before{background:GrayText}}}@layer styles{.icons{position:relative;height:100%;width:100%}.icon{position:absolute;inset:0;margin:auto;display:flex;align-items:center;justify-content:center;fill:currentColor;transition:fill 67ms linear,opacity 33ms linear,transform 167ms cubic-bezier(0.2, 0, 0, 1);opacity:0}.disabled .icon{transition:none}.selected .icon--on,.unselected .icon--off{opacity:1}.unselected .handle:not(.with-icon) .icon--on{transform:rotate(-45deg)}.icon--off{width:var(--md-switch-icon-size, 16px);height:var(--md-switch-icon-size, 16px);color:var(--md-switch-icon-color, var(--md-sys-color-surface-container-highest, #e6e0e9))}.unselected:hover .icon--off{color:var(--md-switch-hover-icon-color, var(--md-sys-color-surface-container-highest, #e6e0e9))}.unselected:focus-within .icon--off{color:var(--md-switch-focus-icon-color, var(--md-sys-color-surface-container-highest, #e6e0e9))}.unselected:active .icon--off{color:var(--md-switch-pressed-icon-color, var(--md-sys-color-surface-container-highest, #e6e0e9))}.unselected.disabled .icon--off{color:var(--md-switch-disabled-icon-color, var(--md-sys-color-surface-container-highest, #e6e0e9));opacity:var(--md-switch-disabled-icon-opacity, 0.38)}.icon--on{width:var(--md-switch-selected-icon-size, 16px);height:var(--md-switch-selected-icon-size, 16px);color:var(--md-switch-selected-icon-color, var(--md-sys-color-on-primary-container, #21005d))}.selected:hover .icon--on{color:var(--md-switch-selected-hover-icon-color, var(--md-sys-color-on-primary-container, #21005d))}.selected:focus-within .icon--on{color:var(--md-switch-selected-focus-icon-color, var(--md-sys-color-on-primary-container, #21005d))}.selected:active .icon--on{color:var(--md-switch-selected-pressed-icon-color, var(--md-sys-color-on-primary-container, #21005d))}.selected.disabled .icon--on{color:var(--md-switch-disabled-selected-icon-color, var(--md-sys-color-on-surface, #1d1b20));opacity:var(--md-switch-disabled-selected-icon-opacity, 0.38)}}@layer hcm{@media(forced-colors: active){.icon--off{fill:Canvas}.icon--on{fill:ButtonText}.disabled.unselected .icon--off,.disabled.selected .icon--on{opacity:1}.disabled .icon--on{fill:GrayText}}}\n`;\n", "/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {Switch} from './internal/switch.js';\nimport {styles} from './internal/switch-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-switch': MdSwitch;\n  }\n}\n\n/**\n * @summary Switches toggle the state of a single item on or off.\n *\n * @description\n * There's one type of switch in Material. Use this selection control when the\n * user needs to toggle a single item on or off.\n *\n * @final\n * @suppress {visibility}\n */\n@customElement('md-switch')\nexport class MdSwitch extends Switch {\n  static override styles: CSSResultOrNative[] = [styles];\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,IAAM,gBAAgB,OAAO,eAAe;AAgEtC,SAAU,cAAc,OAAc,UAAoB;AAC9D,QAAM,QAAS,MAAiC,aAAa;AAC7D,MAAI,CAAC,OAAO;AACV,UAAM,IAAI,MAAM,IAAI,MAAM,IAAI,qCAAqC;EACrE;AAEA,QAAM,iBAAiB,SAAS,QAAQ;AAC1C;AAOA,IAAM,8BAA8B,oBAAI,QAAO;AAyBzC,SAAU,mBACd,YACG,YAAiC;AAEpC,MAAI,oBAAoB,4BAA4B,IAAI,OAAO;AAC/D,MAAI,CAAC,mBAAmB;AACtB,wBAAoB,oBAAI,IAAG;AAC3B,gCAA4B,IAAI,SAAS,iBAAiB;EAC5D;AAEA,aAAW,aAAa,YAAY;AAIlC,QAAI,kBAAkB,IAAI,SAAS,GAAG;AACpC;IACF;AAIA,QAAI,kBAAkB;AACtB,YAAQ,iBACN,WACA,CAAC,UAAgB;AACf,UAAI,iBAAiB;AACnB;MACF;AAIA,YAAM,yBAAwB;AAE9B,YAAM,YAAY,QAAQ,UAAU,MAAM,aAAa;QACrD,MAAM;QACN;OACD;AAGD,YAAM,QAAQ,IAAI,YAAW;AAC5B,gBAAqC,aAAa,IAAI;AAIvD,wBAAkB;AAClB,YAAM,aAAa,QAAQ,cAAc,SAAS;AAClD,wBAAkB;AAClB,UAAI,CAAC,YAAY;AACf,cAAM,eAAc;MACtB;AAGA,YAAM,cAAc,IAAI,MAAM,OAAO,CAAC;IACxC,GACA;;;;MAIE,SAAS;KACV;AAGH,sBAAkB,IAAI,SAAS;EACjC;AACF;;;AC1IA,IAAM,kBAAkB,mBACtB,0BACE,oBAAoB,sBAAsB,UAAU,CAAC,CAAC,CACvD;AASG,IAAO,SAAP,cAAsB,gBAAe;EAyCzC,cAAA;AACE,UAAK;AA/BoB,SAAA,WAAW;AAKX,SAAA,QAAQ;AAOnC,SAAA,uBAAuB;AAQI,SAAA,WAAW;AAM1B,SAAA,QAAQ;AAMlB,QAAI,UAAU;AACZ;IACF;AAIA,SAAK,iBAAiB,SAAS,CAAC,UAAqB;AACnD,UAAI,CAAC,kBAAkB,KAAK,KAAK,CAAC,KAAK,OAAO;AAC5C;MACF;AACA,WAAK,MAAK;AACV,8BAAwB,KAAK,KAAK;IACpC,CAAC;AAID,uBAAmB,MAAM,SAAS;AAClC,SAAK,iBAAiB,WAAW,CAAC,UAAwB;AACxD,oBAAc,OAAO,MAAK;AACxB,cAAM,cAAc,MAAM,oBAAoB,MAAM,QAAQ;AAC5D,YAAI,eAAe,KAAK,YAAY,CAAC,KAAK,OAAO;AAC/C;QACF;AAEA,aAAK,MAAM,MAAK;MAClB,CAAC;IACH,CAAC;EACH;EAEmB,SAAM;AACvB,WAAO;2BACgB,SAAS,KAAK,iBAAgB,CAAE,CAAC;;;;;;uBAMpC,KAAmB,aAAa,OAAO;qBAC1C,KAAK,QAAQ;sBACZ,KAAK,QAAQ;sBACb,KAAK,QAAQ;mBAChB,KAAK,WAAW;oBACf,KAAK,YAAY;;;+BAGN,KAAK,aAAY,CAAE;;;EAGhD;EAEQ,mBAAgB;AACtB,WAAO;MACL,YAAY,KAAK;MACjB,cAAc,CAAC,KAAK;MACpB,YAAY,KAAK;;EAErB;EAEQ,eAAY;AAClB,UAAM,UAAU;MACd,aAAa,KAAK,uBAAuB,KAAK,WAAW,KAAK;;AAEhE,WAAO;QACH,KAAK,kBAAiB,CAAE;;6CAEa,KAAK,QAAQ;8BAC5B,SAAS,OAAO,CAAC;YACnC,KAAK,gBAAe,IAAK,KAAK,YAAW,IAAK,MAAM;;;;EAI9D;EAEQ,cAAW;AACjB,WAAO;;UAED,KAAK,aAAY,CAAE;UACnB,KAAK,uBAAuB,SAAS,KAAK,cAAa,CAAE;;;EAGjE;;;;EAKQ,eAAY;AAClB,WAAO;;;;;;;;EAQT;;;;EAKQ,gBAAa;AACnB,WAAO;;;;;;;;EAQT;EAEQ,oBAAiB;AACvB,WAAO;EACT;EAEQ,kBAAe;AACrB,WAAO,KAAK,SAAS,KAAK;EAC5B;EAEQ,YAAY,OAAY;AAC9B,UAAM,SAAS,MAAM;AACrB,SAAK,WAAW,OAAO;EAEzB;EAEQ,aAAa,OAAY;AAE/B,oBAAgB,MAAM,KAAK;EAC7B;EAMS,CAAC,YAAY,IAAC;AACrB,WAAO,KAAK,WAAW,KAAK,QAAQ;EACtC;EAES,CAAC,YAAY,IAAC;AACrB,WAAO,OAAO,KAAK,QAAQ;EAC7B;EAES,oBAAiB;AAGxB,SAAK,WAAW,KAAK,aAAa,UAAU;EAC9C;EAES,yBAAyB,OAAa;AAC7C,SAAK,WAAW,UAAU;EAC5B;EAES,CAAC,eAAe,IAAC;AACxB,WAAO,IAAI,kBAAkB,OAAO;MAClC,SAAS,KAAK;MACd,UAAU,KAAK;MACf;EACJ;EAES,CAAC,iBAAiB,IAAC;AAC1B,WAAO,KAAK;EACd;;AAzMgB,OAAA,oBAAoC;EAClD,MAAM;EACN,gBAAgB;;AAOS,WAAA;EAA1B,SAAS,EAAC,MAAM,QAAO,CAAC;;AAKE,WAAA;EAA1B,SAAS,EAAC,MAAM,QAAO,CAAC;;AAOzB,WAAA;EADC,SAAS,EAAC,MAAM,SAAS,WAAW,0BAAyB,CAAC;;AASpC,WAAA;EAA1B,SAAS,EAAC,MAAM,QAAO,CAAC;;AAMb,WAAA;EAAX,SAAQ;;AAEwB,WAAA;EAAhC,MAAM,OAAO;;;;ACjFT,IAAM,SAAS;;;;ACsBf,IAAM,WAAN,MAAMA,kBAAiB,OAAM;;AAClB,SAAA,SAA8B,CAAC,MAAM;AAD1C,WAAQ,WAAA;EADpB,cAAc,WAAW;GACb,QAAQ;", "names": ["MdSwitch"]}