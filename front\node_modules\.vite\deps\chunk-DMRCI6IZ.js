// node_modules/@material/web/menu/internal/controllers/shared.js
function createCloseMenuEvent(initiator, reason) {
  return new CustomEvent("close-menu", {
    bubbles: true,
    composed: true,
    detail: { initiator, reason, itemPath: [initiator] }
  });
}
var createDefaultCloseMenuEvent = createCloseMenuEvent;
function createDeactivateTypeaheadEvent() {
  return new Event("deactivate-typeahead", { bubbles: true, composed: true });
}
function createActivateTypeaheadEvent() {
  return new Event("activate-typeahead", { bubbles: true, composed: true });
}
var NavigableKey = {
  UP: "ArrowUp",
  DOWN: "ArrowDown",
  RIGHT: "ArrowRight",
  LEFT: "ArrowLeft"
};
var SelectionKey = {
  SPACE: "Space",
  ENTER: "Enter"
};
var CloseReason = {
  CLICK_SELECTION: "click-selection",
  KEYDOWN: "keydown"
};
var KeydownCloseKey = {
  ESCAPE: "Escape",
  SPACE: SelectionKey.SPACE,
  ENTER: SelectionKey.ENTER
};
function isClosableKey(code) {
  return Object.values(KeydownCloseKey).some((value) => value === code);
}
function isSelectableKey(code) {
  return Object.values(SelectionKey).some((value) => value === code);
}
function isElementInSubtree(target, container) {
  const focusEv = new Event("md-contains", { bubbles: true, composed: true });
  let composedPath = [];
  const listener = (ev) => {
    composedPath = ev.composedPath();
  };
  container.addEventListener("md-contains", listener);
  target.dispatchEvent(focusEv);
  container.removeEventListener("md-contains", listener);
  const isContained = composedPath.length > 0;
  return isContained;
}
var FocusState = {
  NONE: "none",
  LIST_ROOT: "list-root",
  FIRST_ITEM: "first-item",
  LAST_ITEM: "last-item"
};

export {
  createDefaultCloseMenuEvent,
  createDeactivateTypeaheadEvent,
  createActivateTypeaheadEvent,
  NavigableKey,
  SelectionKey,
  CloseReason,
  KeydownCloseKey,
  isClosableKey,
  isSelectableKey,
  isElementInSubtree,
  FocusState
};
/*! Bundled license information:

@material/web/menu/internal/controllers/shared.js:
  (**
   * @license
   * Copyright 2023 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   *)
*/
//# sourceMappingURL=chunk-DMRCI6IZ.js.map
