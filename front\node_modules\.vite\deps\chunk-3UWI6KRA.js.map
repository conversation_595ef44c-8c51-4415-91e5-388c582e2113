{"version": 3, "sources": ["../../@material/web/button/internal/shared-styles.ts", "../../@material/web/button/internal/button.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2024 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n// Generated stylesheet for ./button/internal/shared-styles.css.\nimport {css} from 'lit';\nexport const styles = css`:host{border-start-start-radius:var(--_container-shape-start-start);border-start-end-radius:var(--_container-shape-start-end);border-end-start-radius:var(--_container-shape-end-start);border-end-end-radius:var(--_container-shape-end-end);box-sizing:border-box;cursor:pointer;display:inline-flex;gap:8px;min-height:var(--_container-height);outline:none;padding-block:calc((var(--_container-height) - max(var(--_label-text-line-height),var(--_icon-size)))/2);padding-inline-start:var(--_leading-space);padding-inline-end:var(--_trailing-space);place-content:center;place-items:center;position:relative;font-family:var(--_label-text-font);font-size:var(--_label-text-size);line-height:var(--_label-text-line-height);font-weight:var(--_label-text-weight);text-overflow:ellipsis;text-wrap:nowrap;user-select:none;-webkit-tap-highlight-color:rgba(0,0,0,0);vertical-align:top;--md-ripple-hover-color: var(--_hover-state-layer-color);--md-ripple-pressed-color: var(--_pressed-state-layer-color);--md-ripple-hover-opacity: var(--_hover-state-layer-opacity);--md-ripple-pressed-opacity: var(--_pressed-state-layer-opacity)}md-focus-ring{--md-focus-ring-shape-start-start: var(--_container-shape-start-start);--md-focus-ring-shape-start-end: var(--_container-shape-start-end);--md-focus-ring-shape-end-end: var(--_container-shape-end-end);--md-focus-ring-shape-end-start: var(--_container-shape-end-start)}:host(:is([disabled],[soft-disabled])){cursor:default;pointer-events:none}.button{border-radius:inherit;cursor:inherit;display:inline-flex;align-items:center;justify-content:center;border:none;outline:none;-webkit-appearance:none;vertical-align:middle;background:rgba(0,0,0,0);text-decoration:none;min-width:calc(64px - var(--_leading-space) - var(--_trailing-space));width:100%;z-index:0;height:100%;font:inherit;color:var(--_label-text-color);padding:0;gap:inherit;text-transform:inherit}.button::-moz-focus-inner{padding:0;border:0}:host(:hover) .button{color:var(--_hover-label-text-color)}:host(:focus-within) .button{color:var(--_focus-label-text-color)}:host(:active) .button{color:var(--_pressed-label-text-color)}.background{background-color:var(--_container-color);border-radius:inherit;inset:0;position:absolute}.label{overflow:hidden}:is(.button,.label,.label slot),.label ::slotted(*){text-overflow:inherit}:host(:is([disabled],[soft-disabled])) .label{color:var(--_disabled-label-text-color);opacity:var(--_disabled-label-text-opacity)}:host(:is([disabled],[soft-disabled])) .background{background-color:var(--_disabled-container-color);opacity:var(--_disabled-container-opacity)}@media(forced-colors: active){.background{border:1px solid CanvasText}:host(:is([disabled],[soft-disabled])){--_disabled-icon-color: GrayText;--_disabled-icon-opacity: 1;--_disabled-container-opacity: 1;--_disabled-label-text-color: GrayText;--_disabled-label-text-opacity: 1}}:host([has-icon]:not([trailing-icon])){padding-inline-start:var(--_with-leading-icon-leading-space);padding-inline-end:var(--_with-leading-icon-trailing-space)}:host([has-icon][trailing-icon]){padding-inline-start:var(--_with-trailing-icon-leading-space);padding-inline-end:var(--_with-trailing-icon-trailing-space)}::slotted([slot=icon]){display:inline-flex;position:relative;writing-mode:horizontal-tb;fill:currentColor;flex-shrink:0;color:var(--_icon-color);font-size:var(--_icon-size);inline-size:var(--_icon-size);block-size:var(--_icon-size)}:host(:hover) ::slotted([slot=icon]){color:var(--_hover-icon-color)}:host(:focus-within) ::slotted([slot=icon]){color:var(--_focus-icon-color)}:host(:active) ::slotted([slot=icon]){color:var(--_pressed-icon-color)}:host(:is([disabled],[soft-disabled])) ::slotted([slot=icon]){color:var(--_disabled-icon-color);opacity:var(--_disabled-icon-opacity)}.touch{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%)}:host([touch-target=wrapper]){margin:max(0px,(48px - var(--_container-height))/2) 0}:host([touch-target=none]) .touch{display:none}\n`;\n", "/**\n * @license\n * Copyright 2019 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport '../../focus/md-focus-ring.js';\nimport '../../ripple/ripple.js';\n\nimport {html, isServer, LitElement, nothing} from 'lit';\nimport {property, query, queryAssignedElements} from 'lit/decorators.js';\n\nimport {ARIAMixinStrict} from '../../internal/aria/aria.js';\nimport {mixinDelegatesAria} from '../../internal/aria/delegate.js';\nimport {\n  FormSubmitter,\n  setupFormSubmitter,\n  type FormSubmitterType,\n} from '../../internal/controller/form-submitter.js';\nimport {\n  dispatchActivationClick,\n  isActivationClick,\n} from '../../internal/events/form-label-activation.js';\nimport {\n  internals,\n  mixinElementInternals,\n} from '../../labs/behaviors/element-internals.js';\n\n// Separate variable needed for closure.\nconst buttonBaseClass = mixinDelegatesAria(mixinElementInternals(LitElement));\n\n/**\n * A button component.\n */\nexport abstract class But<PERSON> extends buttonBaseClass implements FormSubmitter {\n  static {\n    setupFormSubmitter(Button);\n  }\n\n  /** @nocollapse */\n  static readonly formAssociated = true;\n\n  /** @nocollapse */\n  static override shadowRootOptions: ShadowRootInit = {\n    mode: 'open',\n    delegatesFocus: true,\n  };\n\n  /**\n   * Whether or not the button is disabled.\n   */\n  @property({type: Boolean, reflect: true}) disabled = false;\n\n  /**\n   * Whether or not the button is \"soft-disabled\" (disabled but still\n   * focusable).\n   *\n   * Use this when a button needs increased visibility when disabled. See\n   * https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/#kbd_disabled_controls\n   * for more guidance on when this is needed.\n   */\n  @property({type: Boolean, attribute: 'soft-disabled', reflect: true})\n  softDisabled = false;\n\n  /**\n   * The URL that the link button points to.\n   */\n  @property() href = '';\n\n  /**\n   * The filename to use when downloading the linked resource.\n   * If not specified, the browser will determine a filename.\n   * This is only applicable when the button is used as a link (`href` is set).\n   */\n  @property() download = '';\n\n  /**\n   * Where to display the linked `href` URL for a link button. Common options\n   * include `_blank` to open in a new tab.\n   */\n  @property() target: '_blank' | '_parent' | '_self' | '_top' | '' = '';\n\n  /**\n   * Whether to render the icon at the inline end of the label rather than the\n   * inline start.\n   *\n   * _Note:_ Link buttons cannot have trailing icons.\n   */\n  @property({type: Boolean, attribute: 'trailing-icon', reflect: true})\n  trailingIcon = false;\n\n  /**\n   * Whether to display the icon or not.\n   */\n  @property({type: Boolean, attribute: 'has-icon', reflect: true}) hasIcon =\n    false;\n\n  /**\n   * The default behavior of the button. May be \"button\", \"reset\", or \"submit\"\n   * (default).\n   */\n  @property() type: FormSubmitterType = 'submit';\n\n  /**\n   * The value added to a form with the button's name when the button submits a\n   * form.\n   */\n  @property({reflect: true}) value = '';\n\n  get name() {\n    return this.getAttribute('name') ?? '';\n  }\n  set name(name: string) {\n    this.setAttribute('name', name);\n  }\n\n  /**\n   * The associated form element with which this element's value will submit.\n   */\n  get form() {\n    return this[internals].form;\n  }\n\n  @query('.button') private readonly buttonElement!: HTMLElement | null;\n\n  @queryAssignedElements({slot: 'icon', flatten: true})\n  private readonly assignedIcons!: HTMLElement[];\n\n  constructor() {\n    super();\n    if (!isServer) {\n      this.addEventListener('click', this.handleClick.bind(this));\n    }\n  }\n\n  override focus() {\n    this.buttonElement?.focus();\n  }\n\n  override blur() {\n    this.buttonElement?.blur();\n  }\n\n  protected override render() {\n    // Link buttons may not be disabled\n    const isRippleDisabled = !this.href && (this.disabled || this.softDisabled);\n    const buttonOrLink = this.href ? this.renderLink() : this.renderButton();\n    // TODO(b/310046938): due to a limitation in focus ring/ripple, we can't use\n    // the same ID for different elements, so we change the ID instead.\n    const buttonId = this.href ? 'link' : 'button';\n    return html`\n      ${this.renderElevationOrOutline?.()}\n      <div class=\"background\"></div>\n      <md-focus-ring part=\"focus-ring\" for=${buttonId}></md-focus-ring>\n      <md-ripple\n        part=\"ripple\"\n        for=${buttonId}\n        ?disabled=\"${isRippleDisabled}\"></md-ripple>\n      ${buttonOrLink}\n    `;\n  }\n\n  // Buttons can override this to add elevation or an outline. Use this and\n  // return `<md-elevation>` (for elevated, filled, and tonal buttons)\n  // or `<div class=\"outline\">` (for outlined buttons).\n  // Text buttons that have neither do not need to implement this.\n  protected renderElevationOrOutline?(): unknown;\n\n  private renderButton() {\n    // Needed for closure conformance\n    const {ariaLabel, ariaHasPopup, ariaExpanded} = this as ARIAMixinStrict;\n    return html`<button\n      id=\"button\"\n      class=\"button\"\n      ?disabled=${this.disabled}\n      aria-disabled=${this.softDisabled || nothing}\n      aria-label=\"${ariaLabel || nothing}\"\n      aria-haspopup=\"${ariaHasPopup || nothing}\"\n      aria-expanded=\"${ariaExpanded || nothing}\">\n      ${this.renderContent()}\n    </button>`;\n  }\n\n  private renderLink() {\n    // Needed for closure conformance\n    const {ariaLabel, ariaHasPopup, ariaExpanded} = this as ARIAMixinStrict;\n    return html`<a\n      id=\"link\"\n      class=\"button\"\n      aria-label=\"${ariaLabel || nothing}\"\n      aria-haspopup=\"${ariaHasPopup || nothing}\"\n      aria-expanded=\"${ariaExpanded || nothing}\"\n      href=${this.href}\n      download=${this.download || nothing}\n      target=${this.target || nothing}\n      >${this.renderContent()}\n    </a>`;\n  }\n\n  private renderContent() {\n    const icon = html`<slot\n      name=\"icon\"\n      @slotchange=\"${this.handleSlotChange}\"></slot>`;\n\n    return html`\n      <span class=\"touch\"></span>\n      ${this.trailingIcon ? nothing : icon}\n      <span class=\"label\"><slot></slot></span>\n      ${this.trailingIcon ? icon : nothing}\n    `;\n  }\n\n  private handleClick(event: MouseEvent) {\n    // If the button is soft-disabled, we need to explicitly prevent the click\n    // from propagating to other event listeners as well as prevent the default\n    // action.\n    if (!this.href && this.softDisabled) {\n      event.stopImmediatePropagation();\n      event.preventDefault();\n      return;\n    }\n\n    if (!isActivationClick(event) || !this.buttonElement) {\n      return;\n    }\n    this.focus();\n    dispatchActivationClick(this.buttonElement);\n  }\n\n  private handleSlotChange() {\n    this.hasIcon = this.assignedIcons.length > 0;\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOO,IAAM,SAAS;;;;ACsBtB,IAAM,kBAAkB,mBAAmB,sBAAsB,UAAU,CAAC;AAKtE,IAAgB,SAAhB,cAA+B,gBAAe;EA2ElD,IAAI,OAAI;AACN,WAAO,KAAK,aAAa,MAAM,KAAK;EACtC;EACA,IAAI,KAAK,MAAY;AACnB,SAAK,aAAa,QAAQ,IAAI;EAChC;;;;EAKA,IAAI,OAAI;AACN,WAAO,KAAK,SAAS,EAAE;EACzB;EAOA,cAAA;AACE,UAAK;AA9EmC,SAAA,WAAW;AAWrD,SAAA,eAAe;AAKH,SAAA,OAAO;AAOP,SAAA,WAAW;AAMX,SAAA,SAAuD;AASnE,SAAA,eAAe;AAKkD,SAAA,UAC/D;AAMU,SAAA,OAA0B;AAMX,SAAA,QAAQ;AAuBjC,QAAI,CAAC,UAAU;AACb,WAAK,iBAAiB,SAAS,KAAK,YAAY,KAAK,IAAI,CAAC;IAC5D;EACF;EAES,QAAK;AACZ,SAAK,eAAe,MAAK;EAC3B;EAES,OAAI;AACX,SAAK,eAAe,KAAI;EAC1B;EAEmB,SAAM;AAEvB,UAAM,mBAAmB,CAAC,KAAK,SAAS,KAAK,YAAY,KAAK;AAC9D,UAAM,eAAe,KAAK,OAAO,KAAK,WAAU,IAAK,KAAK,aAAY;AAGtE,UAAM,WAAW,KAAK,OAAO,SAAS;AACtC,WAAO;QACH,KAAK,2BAA0B,CAAE;;6CAEI,QAAQ;;;cAGvC,QAAQ;qBACD,gBAAgB;QAC7B,YAAY;;EAElB;EAQQ,eAAY;AAElB,UAAM,EAAC,WAAW,cAAc,aAAY,IAAI;AAChD,WAAO;;;kBAGO,KAAK,QAAQ;sBACT,KAAK,gBAAgB,OAAO;oBAC9B,aAAa,OAAO;uBACjB,gBAAgB,OAAO;uBACvB,gBAAgB,OAAO;QACtC,KAAK,cAAa,CAAE;;EAE1B;EAEQ,aAAU;AAEhB,UAAM,EAAC,WAAW,cAAc,aAAY,IAAI;AAChD,WAAO;;;oBAGS,aAAa,OAAO;uBACjB,gBAAgB,OAAO;uBACvB,gBAAgB,OAAO;aACjC,KAAK,IAAI;iBACL,KAAK,YAAY,OAAO;eAC1B,KAAK,UAAU,OAAO;SAC5B,KAAK,cAAa,CAAE;;EAE3B;EAEQ,gBAAa;AACnB,UAAM,OAAO;;qBAEI,KAAK,gBAAgB;AAEtC,WAAO;;QAEH,KAAK,eAAe,UAAU,IAAI;;QAElC,KAAK,eAAe,OAAO,OAAO;;EAExC;EAEQ,YAAY,OAAiB;AAInC,QAAI,CAAC,KAAK,QAAQ,KAAK,cAAc;AACnC,YAAM,yBAAwB;AAC9B,YAAM,eAAc;AACpB;IACF;AAEA,QAAI,CAAC,kBAAkB,KAAK,KAAK,CAAC,KAAK,eAAe;AACpD;IACF;AACA,SAAK,MAAK;AACV,4BAAwB,KAAK,aAAa;EAC5C;EAEQ,mBAAgB;AACtB,SAAK,UAAU,KAAK,cAAc,SAAS;EAC7C;;CApMA,MAAA;AACE,qBAAmB,MAAM;AAC3B,GAAC;AAGe,OAAA,iBAAiB;AAGjB,OAAA,oBAAoC;EAClD,MAAM;EACN,gBAAgB;;AAMwB,WAAA;EAAzC,SAAS,EAAC,MAAM,SAAS,SAAS,KAAI,CAAC;;AAWxC,WAAA;EADC,SAAS,EAAC,MAAM,SAAS,WAAW,iBAAiB,SAAS,KAAI,CAAC;;AAMxD,WAAA;EAAX,SAAQ;;AAOG,WAAA;EAAX,SAAQ;;AAMG,WAAA;EAAX,SAAQ;;AAST,WAAA;EADC,SAAS,EAAC,MAAM,SAAS,WAAW,iBAAiB,SAAS,KAAI,CAAC;;AAMH,WAAA;EAAhE,SAAS,EAAC,MAAM,SAAS,WAAW,YAAY,SAAS,KAAI,CAAC;;AAOnD,WAAA;EAAX,SAAQ;;AAMkB,WAAA;EAA1B,SAAS,EAAC,SAAS,KAAI,CAAC;;AAgBU,WAAA;EAAlC,MAAM,SAAS;;AAGC,WAAA;EADhB,sBAAsB,EAAC,MAAM,QAAQ,SAAS,KAAI,CAAC;;", "names": []}