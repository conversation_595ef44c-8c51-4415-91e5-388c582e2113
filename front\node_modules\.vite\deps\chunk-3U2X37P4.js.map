{"version": 3, "sources": ["../../@material/web/internal/events/form-label-activation.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\n/**\n * Dispatches a click event to the given element that triggers a native action,\n * but is not composed and therefore is not seen outside the element.\n *\n * This is useful for responding to an external click event on the host element\n * that should trigger an internal action like a button click.\n *\n * Note, a helper is provided because setting this up correctly is a bit tricky.\n * In particular, calling `click` on an element creates a composed event, which\n * is not desirable, and a manually dispatched event must specifically be a\n * `MouseEvent` to trigger a native action.\n *\n * @example\n * hostClickListener = (event: MouseEvent) {\n *   if (isActivationClick(event)) {\n *     this.dispatchActivationClick(this.buttonElement);\n *   }\n * }\n *\n */\nexport function dispatchActivationClick(element: HTMLElement) {\n  const event = new MouseEvent('click', {bubbles: true});\n  element.dispatchEvent(event);\n  return event;\n}\n\n/**\n * Returns true if the click event should trigger an activation behavior. The\n * behavior is defined by the element and is whatever it should do when\n * clicked.\n *\n * Typically when an element needs to handle a click, the click is generated\n * from within the element and an event listener within the element implements\n * the needed behavior; however, it's possible to fire a click directly\n * at the element that the element should handle. This method helps\n * distinguish these \"external\" clicks.\n *\n * An \"external\" click can be triggered in a number of ways: via a click\n * on an associated label for a form  associated element, calling\n * `element.click()`, or calling\n * `element.dispatchEvent(new MouseEvent('click', ...))`.\n *\n * Also works around Firefox issue\n * https://bugzilla.mozilla.org/show_bug.cgi?id=1804576 by squelching\n * events for a microtask after called.\n *\n * @example\n * hostClickListener = (event: MouseEvent) {\n *   if (isActivationClick(event)) {\n *     this.dispatchActivationClick(this.buttonElement);\n *   }\n * }\n *\n */\nexport function isActivationClick(event: Event) {\n  // Event must start at the event target.\n  if (event.currentTarget !== event.target) {\n    return false;\n  }\n  // Event must not be retargeted from shadowRoot.\n  if (event.composedPath()[0] !== event.target) {\n    return false;\n  }\n  // Target must not be disabled; this should only occur for a synthetically\n  // dispatched click.\n  if ((event.target as EventTarget & {disabled: boolean}).disabled) {\n    return false;\n  }\n  // This is an activation if the event should not be squelched.\n  return !squelchEvent(event);\n}\n\n// TODO(https://bugzilla.mozilla.org/show_bug.cgi?id=1804576)\n//  Remove when Firefox bug is addressed.\nfunction squelchEvent(event: Event) {\n  const squelched = isSquelchingEvents;\n  if (squelched) {\n    event.preventDefault();\n    event.stopImmediatePropagation();\n  }\n  squelchEventsForMicrotask();\n  return squelched;\n}\n\n// Ignore events for one microtask only.\nlet isSquelchingEvents = false;\nasync function squelchEventsForMicrotask() {\n  isSquelchingEvents = true;\n  // Need to pause for just one microtask.\n  // tslint:disable-next-line\n  await null;\n  isSquelchingEvents = false;\n}\n"], "mappings": ";AA0BM,SAAU,wBAAwB,SAAoB;AAC1D,QAAM,QAAQ,IAAI,WAAW,SAAS,EAAC,SAAS,KAAI,CAAC;AACrD,UAAQ,cAAc,KAAK;AAC3B,SAAO;AACT;AA8BM,SAAU,kBAAkB,OAAY;AAE5C,MAAI,MAAM,kBAAkB,MAAM,QAAQ;AACxC,WAAO;EACT;AAEA,MAAI,MAAM,aAAY,EAAG,CAAC,MAAM,MAAM,QAAQ;AAC5C,WAAO;EACT;AAGA,MAAK,MAAM,OAA6C,UAAU;AAChE,WAAO;EACT;AAEA,SAAO,CAAC,aAAa,KAAK;AAC5B;AAIA,SAAS,aAAa,OAAY;AAChC,QAAM,YAAY;AAClB,MAAI,WAAW;AACb,UAAM,eAAc;AACpB,UAAM,yBAAwB;EAChC;AACA,4BAAyB;AACzB,SAAO;AACT;AAGA,IAAI,qBAAqB;AACzB,eAAe,4BAAyB;AACtC,uBAAqB;AAGrB,QAAM;AACN,uBAAqB;AACvB;", "names": []}