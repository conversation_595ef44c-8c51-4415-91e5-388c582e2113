{"version": 3, "sources": ["../../@material/web/menu/internal/submenu/sub-menu.ts", "../../@material/web/menu/internal/submenu/sub-menu-styles.ts", "../../@material/web/menu/sub-menu.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {html, isServer, LitElement} from 'lit';\nimport {property, queryAssignedElements} from 'lit/decorators.js';\n\nimport {\n  createDeactivateItemsEvent,\n  createRequestActivationEvent,\n  deactivateActiveItem,\n  getFirstActivatableItem,\n} from '../../../list/internal/list-navigation-helpers.js';\nimport {MenuItem} from '../controllers/menuItemController.js';\nimport {\n  CloseMenuEvent,\n  CloseReason,\n  createActivateTypeaheadEvent,\n  createDeactivateTypeaheadEvent,\n  KeydownCloseKey,\n  Menu,\n  NavigableKey,\n  SelectionKey,\n} from '../controllers/shared.js';\nimport {Corner} from '../menu.js';\n\n/**\n * @fires deactivate-items {Event} Requests the parent menu to deselect other\n * items when a submenu opens. --bubbles --composed\n * @fires request-activation {Event} Requests the parent to make the slotted item\n * focusable and focus the item. --bubbles --composed\n * @fires deactivate-typeahead {Event} Requests the parent menu to deactivate\n * the typeahead functionality when a submenu opens. --bubbles --composed\n * @fires activate-typeahead {Event} Requests the parent menu to activate the\n * typeahead functionality when a submenu closes. --bubbles --composed\n */\nexport class SubMenu extends LitElement {\n  /**\n   * The anchorCorner to set on the submenu.\n   */\n  @property({attribute: 'anchor-corner'})\n  anchorCorner: Corner = Corner.START_END;\n  /**\n   * The menuCorner to set on the submenu.\n   */\n  @property({attribute: 'menu-corner'}) menuCorner: Corner = Corner.START_START;\n  /**\n   * The delay between mouseenter and submenu opening.\n   */\n  @property({type: Number, attribute: 'hover-open-delay'}) hoverOpenDelay = 400;\n  /**\n   * The delay between ponterleave and the submenu closing.\n   */\n  @property({type: Number, attribute: 'hover-close-delay'})\n  hoverCloseDelay = 400;\n\n  /**\n   * READONLY: self-identifies as a menu item and sets its identifying attribute\n   */\n  @property({type: Boolean, reflect: true, attribute: 'md-sub-menu'})\n  isSubMenu = true;\n\n  get item() {\n    return this.items[0] ?? null;\n  }\n\n  get menu() {\n    return this.menus[0] ?? null;\n  }\n\n  @queryAssignedElements({slot: 'item', flatten: true})\n  private readonly items!: MenuItem[];\n\n  @queryAssignedElements({slot: 'menu', flatten: true})\n  private readonly menus!: Menu[];\n\n  private previousOpenTimeout = 0;\n  private previousCloseTimeout = 0;\n\n  constructor() {\n    super();\n\n    if (!isServer) {\n      this.addEventListener('mouseenter', this.onMouseenter);\n      this.addEventListener('mouseleave', this.onMouseleave);\n    }\n  }\n\n  override render() {\n    return html`\n      <slot\n        name=\"item\"\n        @click=${this.onClick}\n        @keydown=${this.onKeydown}\n        @slotchange=${this.onSlotchange}>\n      </slot>\n      <slot\n        name=\"menu\"\n        @keydown=${this.onSubMenuKeydown}\n        @close-menu=${this.onCloseSubmenu}\n        @slotchange=${this.onSlotchange}>\n      </slot>\n    `;\n  }\n\n  protected override firstUpdated() {\n    // slotchange is not fired if the contents have been SSRd\n    this.onSlotchange();\n  }\n\n  /**\n   * Shows the submenu.\n   */\n  async show() {\n    const menu = this.menu;\n    if (!menu || menu.open) return;\n\n    // Ensures that we deselect items when the menu closes and reactivate\n    // typeahead when the menu closes, so that we do not have dirty state of\n    // `sub-menu > menu-item[selected]` when we reopen.\n    //\n    // This cannot happen in `close()` because the menu may close via other\n    // means Additionally, this cannot happen in onCloseSubmenu because\n    // `close-menu` may not be called via focusout of outside click and not\n    // triggered by an item\n    menu.addEventListener(\n      'closed',\n      () => {\n        this.item.ariaExpanded = 'false';\n        this.dispatchEvent(createActivateTypeaheadEvent());\n        this.dispatchEvent(createDeactivateItemsEvent());\n        // aria-hidden required so ChromeVox doesn't announce the closed menu\n        menu.ariaHidden = 'true';\n      },\n      {once: true},\n    );\n\n    // Parent menu is `position: absolute` – this creates a new CSS relative\n    // positioning context (similar to doing `position: relative`), so the\n    // submenu's `<md-menu slot=\"submenu\" positioning=\"document\">` would be\n    // wrong even if we change `md-sub-menu` from `position: relative` to\n    // `position: static` because the submenu it would still be positioning\n    // itself relative to the parent menu.\n    if (menu.positioning === 'document') {\n      menu.positioning = 'absolute';\n    }\n    menu.quick = true;\n    // Submenus are in overflow when not fixed. Can remove once we have native\n    // popup support\n    menu.hasOverflow = true;\n    menu.anchorCorner = this.anchorCorner;\n    menu.menuCorner = this.menuCorner;\n    menu.anchorElement = this.item;\n    menu.defaultFocus = 'first-item';\n    // aria-hidden management required so ChromeVox doesn't announce the closed\n    // menu. Remove it here since we are about to show and focus it.\n    menu.removeAttribute('aria-hidden');\n    // This is required in the case where we have a leaf menu open and and the\n    // user hovers a parent menu's item which is not an md-sub-menu item.\n    // If this were set to true, then the menu would close and focus would be\n    // lost. That means the focusout event would have a `relatedTarget` of\n    // `null` since nothing in the menu would be focused anymore due to the\n    // leaf menu closing. restoring focus ensures that we keep focus in the\n    // submenu tree.\n    menu.skipRestoreFocus = false;\n\n    // Menu could already be opened because of mouse interaction\n    const menuAlreadyOpen = menu.open;\n    menu.show();\n    this.item.ariaExpanded = 'true';\n    this.item.ariaHasPopup = 'menu';\n    if (menu.id) {\n      this.item.setAttribute('aria-controls', menu.id);\n    }\n\n    // Deactivate other items. This can be the case if the user has tabbed\n    // around the menu and then mouses over an md-sub-menu.\n    this.dispatchEvent(createDeactivateItemsEvent());\n    this.dispatchEvent(createDeactivateTypeaheadEvent());\n    this.item.selected = true;\n\n    // This is the case of mouse hovering when already opened via keyboard or\n    // vice versa\n    if (!menuAlreadyOpen) {\n      let open = (value: unknown) => {};\n      const opened = new Promise((resolve) => {\n        open = resolve;\n      });\n      menu.addEventListener('opened', open, {once: true});\n      await opened;\n    }\n  }\n\n  /**\n   * Closes the submenu.\n   */\n  async close() {\n    const menu = this.menu;\n    if (!menu || !menu.open) return;\n\n    this.dispatchEvent(createActivateTypeaheadEvent());\n    menu.quick = true;\n    menu.close();\n    this.dispatchEvent(createDeactivateItemsEvent());\n    let close = (value: unknown) => {};\n    const closed = new Promise((resolve) => {\n      close = resolve;\n    });\n    menu.addEventListener('closed', close, {once: true});\n    await closed;\n  }\n\n  protected onSlotchange() {\n    if (!this.item) {\n      return;\n    }\n\n    // TODO(b/301296618): clean up old aria values on change\n    this.item.ariaExpanded = 'false';\n    this.item.ariaHasPopup = 'menu';\n    if (this.menu?.id) {\n      this.item.setAttribute('aria-controls', this.menu.id);\n    }\n    this.item.keepOpen = true;\n\n    const menu = this.menu;\n    if (!menu) return;\n\n    menu.isSubmenu = true;\n    // Required for ChromeVox to not linearly navigate to the menu while closed\n    menu.ariaHidden = 'true';\n  }\n\n  /**\n   * Starts the default 400ms countdown to open the submenu.\n   *\n   * NOTE: We explicitly use mouse events and not pointer events because\n   * pointer events apply to touch events. And if a user were to tap a\n   * sub-menu, it would fire the \"pointerenter\", \"pointerleave\", \"click\" events\n   * which would open the menu on click, and then set the timeout to close the\n   * menu due to pointerleave.\n   */\n  protected onMouseenter = () => {\n    clearTimeout(this.previousOpenTimeout);\n    clearTimeout(this.previousCloseTimeout);\n    if (this.menu?.open) return;\n\n    // Open synchronously if delay is 0. (screenshot tests infra\n    // would never resolve otherwise)\n    if (!this.hoverOpenDelay) {\n      this.show();\n    } else {\n      this.previousOpenTimeout = setTimeout(() => {\n        this.show();\n      }, this.hoverOpenDelay);\n    }\n  };\n\n  /**\n   * Starts the default 400ms countdown to close the submenu.\n   *\n   * NOTE: We explicitly use mouse events and not pointer events because\n   * pointer events apply to touch events. And if a user were to tap a\n   * sub-menu, it would fire the \"pointerenter\", \"pointerleave\", \"click\" events\n   * which would open the menu on click, and then set the timeout to close the\n   * menu due to pointerleave.\n   */\n  protected onMouseleave = () => {\n    clearTimeout(this.previousCloseTimeout);\n    clearTimeout(this.previousOpenTimeout);\n\n    // Close synchronously if delay is 0. (screenshot tests infra\n    // would never resolve otherwise)\n    if (!this.hoverCloseDelay) {\n      this.close();\n    } else {\n      this.previousCloseTimeout = setTimeout(() => {\n        this.close();\n      }, this.hoverCloseDelay);\n    }\n  };\n\n  protected onClick() {\n    this.show();\n  }\n\n  /**\n   * On item keydown handles opening the submenu.\n   */\n  protected async onKeydown(event: KeyboardEvent) {\n    const shouldOpenSubmenu = this.isSubmenuOpenKey(event.code);\n\n    if (event.defaultPrevented) return;\n\n    const openedWithLR =\n      shouldOpenSubmenu &&\n      (NavigableKey.LEFT === event.code || NavigableKey.RIGHT === event.code);\n\n    if (event.code === SelectionKey.SPACE || openedWithLR) {\n      // prevent space from scrolling and Left + Right from selecting previous /\n      // next items or opening / closing parent menus. Only open the submenu.\n      event.preventDefault();\n\n      if (openedWithLR) {\n        event.stopPropagation();\n      }\n    }\n\n    if (!shouldOpenSubmenu) {\n      return;\n    }\n\n    const submenu = this.menu;\n    if (!submenu) return;\n\n    const submenuItems = submenu.items;\n    const firstActivatableItem = getFirstActivatableItem(submenuItems);\n\n    if (firstActivatableItem) {\n      await this.show();\n\n      firstActivatableItem.tabIndex = 0;\n      firstActivatableItem.focus();\n\n      return;\n    }\n  }\n\n  private onCloseSubmenu(event: CloseMenuEvent) {\n    const {itemPath, reason} = event.detail;\n    itemPath.push(this.item);\n\n    this.dispatchEvent(createActivateTypeaheadEvent());\n    // Escape should only close one menu not all of the menus unlike space or\n    // click selection which should close all menus.\n    if (\n      reason.kind === CloseReason.KEYDOWN &&\n      reason.key === KeydownCloseKey.ESCAPE\n    ) {\n      event.stopPropagation();\n      this.item.dispatchEvent(createRequestActivationEvent());\n      return;\n    }\n\n    this.dispatchEvent(createDeactivateItemsEvent());\n  }\n\n  private async onSubMenuKeydown(event: KeyboardEvent) {\n    if (event.defaultPrevented) return;\n    const {close: shouldClose, keyCode} = this.isSubmenuCloseKey(event.code);\n    if (!shouldClose) return;\n\n    // Communicate that it's handled so that we don't accidentally close every\n    // parent menu. Additionally, we want to isolate things like the typeahead\n    // keydowns from bubbling up to the parent menu and confounding things.\n    event.preventDefault();\n\n    if (keyCode === NavigableKey.LEFT || keyCode === NavigableKey.RIGHT) {\n      // Prevent this from bubbling to parents\n      event.stopPropagation();\n    }\n\n    await this.close();\n\n    deactivateActiveItem(this.menu.items);\n    this.item?.focus();\n    this.item.tabIndex = 0;\n    this.item.focus();\n  }\n\n  /**\n   * Determines whether the given KeyboardEvent code is one that should open\n   * the submenu. This is RTL-aware. By default, left, right, space, or enter.\n   *\n   * @param code The native KeyboardEvent code.\n   * @return Whether or not the key code should open the submenu.\n   */\n  private isSubmenuOpenKey(code: string) {\n    const isRtl = getComputedStyle(this).direction === 'rtl';\n    const arrowEnterKey = isRtl ? NavigableKey.LEFT : NavigableKey.RIGHT;\n    switch (code) {\n      case arrowEnterKey:\n      case SelectionKey.SPACE:\n      case SelectionKey.ENTER:\n        return true;\n      default:\n        return false;\n    }\n  }\n\n  /**\n   * Determines whether the given KeyboardEvent code is one that should close\n   * the submenu. This is RTL-aware. By default right, left, or escape.\n   *\n   * @param code The native KeyboardEvent code.\n   * @return Whether or not the key code should close the submenu.\n   */\n  private isSubmenuCloseKey(code: string) {\n    const isRtl = getComputedStyle(this).direction === 'rtl';\n    const arrowEnterKey = isRtl ? NavigableKey.RIGHT : NavigableKey.LEFT;\n    switch (code) {\n      case arrowEnterKey:\n      case KeydownCloseKey.ESCAPE:\n        return {close: true, keyCode: code} as const;\n      default:\n        return {close: false} as const;\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n// Generated stylesheet for ./menu/internal/submenu/sub-menu-styles.css.\nimport {css} from 'lit';\nexport const styles = css`:host{position:relative;display:flex;flex-direction:column}\n`;\n", "/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {SubMenu} from './internal/submenu/sub-menu.js';\nimport {styles} from './internal/submenu/sub-menu-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-sub-menu': MdSubMenu;\n  }\n}\n\n/**\n * @summary Menus display a list of choices on a temporary surface.\n *\n * @description\n * Menu items are the selectable choices within the menu. Menu items must\n * implement the `Menu` interface and also have the `md-menu`\n * attribute. Additionally menu items are list items so they must also have the\n * `md-list-item` attribute.\n *\n * Menu items can control a menu by selectively firing the `close-menu` and\n * `deselect-items` events.\n *\n * This menu item will open a sub-menu that is slotted in the `submenu` slot.\n * Additionally, the containing menu must either have `has-overflow` or\n * `positioning=fixed` set to `true` in order to display the containing menu\n * properly.\n *\n * @example\n * ```html\n * <div style=\"position:relative;\">\n *   <button\n *       id=\"anchor\"\n *       @click=${() => this.menuRef.value.show()}>\n *     Click to open menu\n *   </button>\n *   <!--\n *     `has-overflow` is required when using a submenu which overflows the\n *     menu's contents\n *   -->\n *   <md-menu anchor=\"anchor\" has-overflow ${ref(menuRef)}>\n *     <md-menu-item headline=\"This is a headline\"></md-menu-item>\n *     <md-sub-menu>\n *       <md-menu-item\n *           slot=\"item\"\n *           headline=\"this is a submenu item\">\n *       </md-menu-item>\n *       <md-menu slot=\"menu\">\n *         <md-menu-item headline=\"This is an item inside a submenu\">\n *         </md-menu-item>\n *       </md-menu>\n *     </md-sub-menu>\n *   </md-menu>\n * </div>\n * ```\n *\n * @final\n * @suppress {visibility}\n */\n@customElement('md-sub-menu')\nexport class MdSubMenu extends SubMenu {\n  static override styles: CSSResultOrNative[] = [styles];\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsCM,IAAO,UAAP,cAAuB,WAAU;EA0BrC,IAAI,OAAI;AACN,WAAO,KAAK,MAAM,CAAC,KAAK;EAC1B;EAEA,IAAI,OAAI;AACN,WAAO,KAAK,MAAM,CAAC,KAAK;EAC1B;EAWA,cAAA;AACE,UAAK;AAvCP,SAAA,eAAuB,OAAO;AAIQ,SAAA,aAAqB,OAAO;AAIT,SAAA,iBAAiB;AAK1E,SAAA,kBAAkB;AAMlB,SAAA,YAAY;AAgBJ,SAAA,sBAAsB;AACtB,SAAA,uBAAuB;AAqKrB,SAAA,eAAe,MAAK;AAC5B,mBAAa,KAAK,mBAAmB;AACrC,mBAAa,KAAK,oBAAoB;AACtC,UAAI,KAAK,MAAM;AAAM;AAIrB,UAAI,CAAC,KAAK,gBAAgB;AACxB,aAAK,KAAI;MACX,OAAO;AACL,aAAK,sBAAsB,WAAW,MAAK;AACzC,eAAK,KAAI;QACX,GAAG,KAAK,cAAc;MACxB;IACF;AAWU,SAAA,eAAe,MAAK;AAC5B,mBAAa,KAAK,oBAAoB;AACtC,mBAAa,KAAK,mBAAmB;AAIrC,UAAI,CAAC,KAAK,iBAAiB;AACzB,aAAK,MAAK;MACZ,OAAO;AACL,aAAK,uBAAuB,WAAW,MAAK;AAC1C,eAAK,MAAK;QACZ,GAAG,KAAK,eAAe;MACzB;IACF;AAtME,QAAI,CAAC,UAAU;AACb,WAAK,iBAAiB,cAAc,KAAK,YAAY;AACrD,WAAK,iBAAiB,cAAc,KAAK,YAAY;IACvD;EACF;EAES,SAAM;AACb,WAAO;;;iBAGM,KAAK,OAAO;mBACV,KAAK,SAAS;sBACX,KAAK,YAAY;;;;mBAIpB,KAAK,gBAAgB;sBAClB,KAAK,cAAc;sBACnB,KAAK,YAAY;;;EAGrC;EAEmB,eAAY;AAE7B,SAAK,aAAY;EACnB;;;;EAKA,MAAM,OAAI;AACR,UAAM,OAAO,KAAK;AAClB,QAAI,CAAC,QAAQ,KAAK;AAAM;AAUxB,SAAK,iBACH,UACA,MAAK;AACH,WAAK,KAAK,eAAe;AACzB,WAAK,cAAc,6BAA4B,CAAE;AACjD,WAAK,cAAc,2BAA0B,CAAE;AAE/C,WAAK,aAAa;IACpB,GACA,EAAC,MAAM,KAAI,CAAC;AASd,QAAI,KAAK,gBAAgB,YAAY;AACnC,WAAK,cAAc;IACrB;AACA,SAAK,QAAQ;AAGb,SAAK,cAAc;AACnB,SAAK,eAAe,KAAK;AACzB,SAAK,aAAa,KAAK;AACvB,SAAK,gBAAgB,KAAK;AAC1B,SAAK,eAAe;AAGpB,SAAK,gBAAgB,aAAa;AAQlC,SAAK,mBAAmB;AAGxB,UAAM,kBAAkB,KAAK;AAC7B,SAAK,KAAI;AACT,SAAK,KAAK,eAAe;AACzB,SAAK,KAAK,eAAe;AACzB,QAAI,KAAK,IAAI;AACX,WAAK,KAAK,aAAa,iBAAiB,KAAK,EAAE;IACjD;AAIA,SAAK,cAAc,2BAA0B,CAAE;AAC/C,SAAK,cAAc,+BAA8B,CAAE;AACnD,SAAK,KAAK,WAAW;AAIrB,QAAI,CAAC,iBAAiB;AACpB,UAAI,OAAO,CAAC,UAAkB;MAAE;AAChC,YAAM,SAAS,IAAI,QAAQ,CAAC,YAAW;AACrC,eAAO;MACT,CAAC;AACD,WAAK,iBAAiB,UAAU,MAAM,EAAC,MAAM,KAAI,CAAC;AAClD,YAAM;IACR;EACF;;;;EAKA,MAAM,QAAK;AACT,UAAM,OAAO,KAAK;AAClB,QAAI,CAAC,QAAQ,CAAC,KAAK;AAAM;AAEzB,SAAK,cAAc,6BAA4B,CAAE;AACjD,SAAK,QAAQ;AACb,SAAK,MAAK;AACV,SAAK,cAAc,2BAA0B,CAAE;AAC/C,QAAI,QAAQ,CAAC,UAAkB;IAAE;AACjC,UAAM,SAAS,IAAI,QAAQ,CAAC,YAAW;AACrC,cAAQ;IACV,CAAC;AACD,SAAK,iBAAiB,UAAU,OAAO,EAAC,MAAM,KAAI,CAAC;AACnD,UAAM;EACR;EAEU,eAAY;AACpB,QAAI,CAAC,KAAK,MAAM;AACd;IACF;AAGA,SAAK,KAAK,eAAe;AACzB,SAAK,KAAK,eAAe;AACzB,QAAI,KAAK,MAAM,IAAI;AACjB,WAAK,KAAK,aAAa,iBAAiB,KAAK,KAAK,EAAE;IACtD;AACA,SAAK,KAAK,WAAW;AAErB,UAAM,OAAO,KAAK;AAClB,QAAI,CAAC;AAAM;AAEX,SAAK,YAAY;AAEjB,SAAK,aAAa;EACpB;EAmDU,UAAO;AACf,SAAK,KAAI;EACX;;;;EAKU,MAAM,UAAU,OAAoB;AAC5C,UAAM,oBAAoB,KAAK,iBAAiB,MAAM,IAAI;AAE1D,QAAI,MAAM;AAAkB;AAE5B,UAAM,eACJ,sBACC,aAAa,SAAS,MAAM,QAAQ,aAAa,UAAU,MAAM;AAEpE,QAAI,MAAM,SAAS,aAAa,SAAS,cAAc;AAGrD,YAAM,eAAc;AAEpB,UAAI,cAAc;AAChB,cAAM,gBAAe;MACvB;IACF;AAEA,QAAI,CAAC,mBAAmB;AACtB;IACF;AAEA,UAAM,UAAU,KAAK;AACrB,QAAI,CAAC;AAAS;AAEd,UAAM,eAAe,QAAQ;AAC7B,UAAM,uBAAuB,wBAAwB,YAAY;AAEjE,QAAI,sBAAsB;AACxB,YAAM,KAAK,KAAI;AAEf,2BAAqB,WAAW;AAChC,2BAAqB,MAAK;AAE1B;IACF;EACF;EAEQ,eAAe,OAAqB;AAC1C,UAAM,EAAC,UAAU,OAAM,IAAI,MAAM;AACjC,aAAS,KAAK,KAAK,IAAI;AAEvB,SAAK,cAAc,6BAA4B,CAAE;AAGjD,QACE,OAAO,SAAS,YAAY,WAC5B,OAAO,QAAQ,gBAAgB,QAC/B;AACA,YAAM,gBAAe;AACrB,WAAK,KAAK,cAAc,6BAA4B,CAAE;AACtD;IACF;AAEA,SAAK,cAAc,2BAA0B,CAAE;EACjD;EAEQ,MAAM,iBAAiB,OAAoB;AACjD,QAAI,MAAM;AAAkB;AAC5B,UAAM,EAAC,OAAO,aAAa,QAAO,IAAI,KAAK,kBAAkB,MAAM,IAAI;AACvE,QAAI,CAAC;AAAa;AAKlB,UAAM,eAAc;AAEpB,QAAI,YAAY,aAAa,QAAQ,YAAY,aAAa,OAAO;AAEnE,YAAM,gBAAe;IACvB;AAEA,UAAM,KAAK,MAAK;AAEhB,yBAAqB,KAAK,KAAK,KAAK;AACpC,SAAK,MAAM,MAAK;AAChB,SAAK,KAAK,WAAW;AACrB,SAAK,KAAK,MAAK;EACjB;;;;;;;;EASQ,iBAAiB,MAAY;AACnC,UAAM,QAAQ,iBAAiB,IAAI,EAAE,cAAc;AACnD,UAAM,gBAAgB,QAAQ,aAAa,OAAO,aAAa;AAC/D,YAAQ,MAAM;MACZ,KAAK;MACL,KAAK,aAAa;MAClB,KAAK,aAAa;AAChB,eAAO;MACT;AACE,eAAO;IACX;EACF;;;;;;;;EASQ,kBAAkB,MAAY;AACpC,UAAM,QAAQ,iBAAiB,IAAI,EAAE,cAAc;AACnD,UAAM,gBAAgB,QAAQ,aAAa,QAAQ,aAAa;AAChE,YAAQ,MAAM;MACZ,KAAK;MACL,KAAK,gBAAgB;AACnB,eAAO,EAAC,OAAO,MAAM,SAAS,KAAI;MACpC;AACE,eAAO,EAAC,OAAO,MAAK;IACxB;EACF;;AA9WA,WAAA;EADC,SAAS,EAAC,WAAW,gBAAe,CAAC;;AAKA,WAAA;EAArC,SAAS,EAAC,WAAW,cAAa,CAAC;;AAIqB,WAAA;EAAxD,SAAS,EAAC,MAAM,QAAQ,WAAW,mBAAkB,CAAC;;AAKvD,WAAA;EADC,SAAS,EAAC,MAAM,QAAQ,WAAW,oBAAmB,CAAC;;AAOxD,WAAA;EADC,SAAS,EAAC,MAAM,SAAS,SAAS,MAAM,WAAW,cAAa,CAAC;;AAYjD,WAAA;EADhB,sBAAsB,EAAC,MAAM,QAAQ,SAAS,KAAI,CAAC;;AAInC,WAAA;EADhB,sBAAsB,EAAC,MAAM,QAAQ,SAAS,KAAI,CAAC;;;;ACpE/C,IAAM,SAAS;;;;AC4Df,IAAM,YAAN,MAAMA,mBAAkB,QAAO;;AACpB,UAAA,SAA8B,CAAC,MAAM;AAD1C,YAAS,WAAA;EADrB,cAAc,aAAa;GACf,SAAS;", "names": ["MdSubMenu"]}