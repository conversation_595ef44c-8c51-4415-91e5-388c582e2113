{"version": 3, "sources": ["../../@material/web/iconbutton/internal/icon-button.ts", "../../@material/web/iconbutton/internal/shared-styles.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport '../../focus/md-focus-ring.js';\nimport '../../ripple/ripple.js';\n\nimport {html, isServer, LitElement, nothing} from 'lit';\nimport {property, state} from 'lit/decorators.js';\nimport {classMap} from 'lit/directives/class-map.js';\nimport {literal, html as staticHtml} from 'lit/static-html.js';\n\nimport {ARIAMixinStrict} from '../../internal/aria/aria.js';\nimport {mixinDelegatesAria} from '../../internal/aria/delegate.js';\nimport {\n  FormSubmitter,\n  setupFormSubmitter,\n  type FormSubmitterType,\n} from '../../internal/controller/form-submitter.js';\nimport {isRtl} from '../../internal/controller/is-rtl.js';\nimport {\n  internals,\n  mixinElementInternals,\n} from '../../labs/behaviors/element-internals.js';\n\ntype LinkTarget = '_blank' | '_parent' | '_self' | '_top';\n\n// Separate variable needed for closure.\nconst iconButtonBaseClass = mixinDelegatesAria(\n  mixinElementInternals(LitElement),\n);\n\n/**\n * A button for rendering icons.\n *\n * @fires input {InputEvent} Dispatched when a toggle button toggles --bubbles\n * --composed\n * @fires change {Event} Dispatched when a toggle button toggles --bubbles\n */\nexport class IconButton extends iconButtonBaseClass implements FormSubmitter {\n  static {\n    setupFormSubmitter(IconButton);\n  }\n\n  /** @nocollapse */\n  static readonly formAssociated = true;\n\n  /** @nocollapse */\n  static override shadowRootOptions: ShadowRootInit = {\n    mode: 'open',\n    delegatesFocus: true,\n  };\n\n  /**\n   * Disables the icon button and makes it non-interactive.\n   */\n  @property({type: Boolean, reflect: true}) disabled = false;\n\n  /**\n   * \"Soft-disables\" the icon button (disabled but still focusable).\n   *\n   * Use this when an icon button needs increased visibility when disabled. See\n   * https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/#kbd_disabled_controls\n   * for more guidance on when this is needed.\n   */\n  @property({type: Boolean, attribute: 'soft-disabled', reflect: true})\n  softDisabled = false;\n\n  /**\n   * Flips the icon if it is in an RTL context at startup.\n   */\n  @property({type: Boolean, attribute: 'flip-icon-in-rtl'})\n  flipIconInRtl = false;\n\n  /**\n   * Sets the underlying `HTMLAnchorElement`'s `href` resource attribute.\n   */\n  @property() href = '';\n\n  /**\n   * The filename to use when downloading the linked resource.\n   * If not specified, the browser will determine a filename.\n   * This is only applicable when the icon button is used as a link (`href` is set).\n   */\n  @property() download = '';\n\n  /**\n   * Sets the underlying `HTMLAnchorElement`'s `target` attribute.\n   */\n  @property() target: LinkTarget | '' = '';\n\n  /**\n   * The `aria-label` of the button when the button is toggleable and selected.\n   */\n  @property({attribute: 'aria-label-selected'}) ariaLabelSelected = '';\n\n  /**\n   * When true, the button will toggle between selected and unselected\n   * states\n   */\n  @property({type: Boolean}) toggle = false;\n\n  /**\n   * Sets the selected state. When false, displays the default icon. When true,\n   * displays the selected icon, or the default icon If no `slot=\"selected\"`\n   * icon is provided.\n   */\n  @property({type: Boolean, reflect: true}) selected = false;\n\n  /**\n   * The default behavior of the button. May be \"button\", \"reset\", or \"submit\"\n   * (default).\n   */\n  @property() type: FormSubmitterType = 'submit';\n\n  /**\n   * The value added to a form with the button's name when the button submits a\n   * form.\n   */\n  @property({reflect: true}) value = '';\n\n  get name() {\n    return this.getAttribute('name') ?? '';\n  }\n  set name(name: string) {\n    this.setAttribute('name', name);\n  }\n\n  /**\n   * The associated form element with which this element's value will submit.\n   */\n  get form() {\n    return this[internals].form;\n  }\n\n  /**\n   * The labels this element is associated with.\n   */\n  get labels() {\n    return this[internals].labels;\n  }\n\n  @state() private flipIcon = isRtl(this, this.flipIconInRtl);\n\n  constructor() {\n    super();\n    if (!isServer) {\n      this.addEventListener('click', this.handleClick.bind(this));\n    }\n  }\n\n  protected override willUpdate() {\n    // Link buttons cannot be disabled or soft-disabled.\n    if (this.href) {\n      this.disabled = false;\n      this.softDisabled = false;\n    }\n  }\n\n  protected override render() {\n    const tag = this.href ? literal`div` : literal`button`;\n    // Needed for closure conformance\n    const {ariaLabel, ariaHasPopup, ariaExpanded} = this as ARIAMixinStrict;\n    const hasToggledAriaLabel = ariaLabel && this.ariaLabelSelected;\n    const ariaPressedValue = !this.toggle ? nothing : this.selected;\n    let ariaLabelValue: string | null | typeof nothing = nothing;\n    if (!this.href) {\n      ariaLabelValue =\n        hasToggledAriaLabel && this.selected\n          ? this.ariaLabelSelected\n          : ariaLabel;\n    }\n    return staticHtml`<${tag}\n        class=\"icon-button ${classMap(this.getRenderClasses())}\"\n        id=\"button\"\n        aria-label=\"${ariaLabelValue || nothing}\"\n        aria-haspopup=\"${(!this.href && ariaHasPopup) || nothing}\"\n        aria-expanded=\"${(!this.href && ariaExpanded) || nothing}\"\n        aria-pressed=\"${ariaPressedValue}\"\n        aria-disabled=${(!this.href && this.softDisabled) || nothing}\n        ?disabled=\"${!this.href && this.disabled}\"\n        @click=\"${this.handleClickOnChild}\">\n        ${this.renderFocusRing()}\n        ${this.renderRipple()}\n        ${!this.selected ? this.renderIcon() : nothing}\n        ${this.selected ? this.renderSelectedIcon() : nothing}\n        ${this.href ? this.renderLink() : this.renderTouchTarget()}\n  </${tag}>`;\n  }\n\n  private renderLink() {\n    // Needed for closure conformance\n    const {ariaLabel} = this as ARIAMixinStrict;\n    return html`\n      <a\n        class=\"link\"\n        id=\"link\"\n        href=\"${this.href}\"\n        download=\"${this.download || nothing}\"\n        target=\"${this.target || nothing}\"\n        aria-label=\"${ariaLabel || nothing}\">\n        ${this.renderTouchTarget()}\n      </a>\n    `;\n  }\n\n  protected getRenderClasses() {\n    return {\n      'flip-icon': this.flipIcon,\n      'selected': this.toggle && this.selected,\n    };\n  }\n\n  private renderIcon() {\n    return html`<span class=\"icon\"><slot></slot></span>`;\n  }\n\n  private renderSelectedIcon() {\n    // Use default slot as fallback to not require specifying multiple icons\n    return html`<span class=\"icon icon--selected\"\n      ><slot name=\"selected\"><slot></slot></slot\n    ></span>`;\n  }\n\n  private renderTouchTarget() {\n    return html`<span class=\"touch\"></span>`;\n  }\n\n  private renderFocusRing() {\n    // TODO(b/310046938): use the same id for both elements\n    return html`<md-focus-ring\n      part=\"focus-ring\"\n      for=${this.href ? 'link' : 'button'}></md-focus-ring>`;\n  }\n\n  private renderRipple() {\n    const isRippleDisabled = !this.href && (this.disabled || this.softDisabled);\n    // TODO(b/310046938): use the same id for both elements\n    return html`<md-ripple\n      for=${this.href ? 'link' : nothing}\n      ?disabled=\"${isRippleDisabled}\"></md-ripple>`;\n  }\n\n  override connectedCallback() {\n    this.flipIcon = isRtl(this, this.flipIconInRtl);\n    super.connectedCallback();\n  }\n\n  /** Handles a click on this element. */\n  private handleClick(event: MouseEvent) {\n    // If the icon button is soft-disabled, we need to explicitly prevent the\n    // click from propagating to other event listeners as well as prevent the\n    // default action.\n    if (!this.href && this.softDisabled) {\n      event.stopImmediatePropagation();\n      event.preventDefault();\n      return;\n    }\n  }\n\n  /**\n   * Handles a click on the child <div> or <button> element within this\n   * element's shadow DOM.\n   */\n  private async handleClickOnChild(event: Event) {\n    // Allow the event to propagate\n    await 0;\n    if (\n      !this.toggle ||\n      this.disabled ||\n      this.softDisabled ||\n      event.defaultPrevented\n    ) {\n      return;\n    }\n\n    this.selected = !this.selected;\n    this.dispatchEvent(\n      new InputEvent('input', {bubbles: true, composed: true}),\n    );\n    // Bubbles but does not compose to mimic native browser <input> & <select>\n    // Additionally, native change event is not an InputEvent.\n    this.dispatchEvent(new Event('change', {bubbles: true}));\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n// Generated stylesheet for ./iconbutton/internal/shared-styles.css.\nimport {css} from 'lit';\nexport const styles = css`:host{display:inline-flex;outline:none;-webkit-tap-highlight-color:rgba(0,0,0,0);height:var(--_container-height);width:var(--_container-width);justify-content:center}:host([touch-target=wrapper]){margin:max(0px,(48px - var(--_container-height))/2) max(0px,(48px - var(--_container-width))/2)}md-focus-ring{--md-focus-ring-shape-start-start: var(--_container-shape-start-start);--md-focus-ring-shape-start-end: var(--_container-shape-start-end);--md-focus-ring-shape-end-end: var(--_container-shape-end-end);--md-focus-ring-shape-end-start: var(--_container-shape-end-start)}:host(:is([disabled],[soft-disabled])){pointer-events:none}.icon-button{place-items:center;background:none;border:none;box-sizing:border-box;cursor:pointer;display:flex;place-content:center;outline:none;padding:0;position:relative;text-decoration:none;user-select:none;z-index:0;flex:1;border-start-start-radius:var(--_container-shape-start-start);border-start-end-radius:var(--_container-shape-start-end);border-end-start-radius:var(--_container-shape-end-start);border-end-end-radius:var(--_container-shape-end-end)}.icon ::slotted(*){font-size:var(--_icon-size);height:var(--_icon-size);width:var(--_icon-size);font-weight:inherit}md-ripple{z-index:-1;border-start-start-radius:var(--_container-shape-start-start);border-start-end-radius:var(--_container-shape-start-end);border-end-start-radius:var(--_container-shape-end-start);border-end-end-radius:var(--_container-shape-end-end)}.flip-icon .icon{transform:scaleX(-1)}.icon{display:inline-flex}.link{display:grid;height:100%;outline:none;place-items:center;position:absolute;width:100%}.touch{position:absolute;height:max(48px,100%);width:max(48px,100%)}:host([touch-target=none]) .touch{display:none}@media(forced-colors: active){:host(:is([disabled],[soft-disabled])){--_disabled-icon-color: GrayText;--_disabled-icon-opacity: 1}}\n`;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BA,IAAM,sBAAsB,mBAC1B,sBAAsB,UAAU,CAAC;AAU7B,IAAO,aAAP,cAA0B,oBAAmB;EAkFjD,IAAI,OAAI;AACN,WAAO,KAAK,aAAa,MAAM,KAAK;EACtC;EACA,IAAI,KAAK,MAAY;AACnB,SAAK,aAAa,QAAQ,IAAI;EAChC;;;;EAKA,IAAI,OAAI;AACN,WAAO,KAAK,SAAS,EAAE;EACzB;;;;EAKA,IAAI,SAAM;AACR,WAAO,KAAK,SAAS,EAAE;EACzB;EAIA,cAAA;AACE,UAAK;AAzFmC,SAAA,WAAW;AAUrD,SAAA,eAAe;AAMf,SAAA,gBAAgB;AAKJ,SAAA,OAAO;AAOP,SAAA,WAAW;AAKX,SAAA,SAA0B;AAKQ,SAAA,oBAAoB;AAMvC,SAAA,SAAS;AAOM,SAAA,WAAW;AAMzC,SAAA,OAA0B;AAMX,SAAA,QAAQ;AAuBlB,SAAA,WAAW,MAAM,MAAM,KAAK,aAAa;AAIxD,QAAI,CAAC,UAAU;AACb,WAAK,iBAAiB,SAAS,KAAK,YAAY,KAAK,IAAI,CAAC;IAC5D;EACF;EAEmB,aAAU;AAE3B,QAAI,KAAK,MAAM;AACb,WAAK,WAAW;AAChB,WAAK,eAAe;IACtB;EACF;EAEmB,SAAM;AACvB,UAAM,MAAM,KAAK,OAAO,eAAe;AAEvC,UAAM,EAAC,WAAW,cAAc,aAAY,IAAI;AAChD,UAAM,sBAAsB,aAAa,KAAK;AAC9C,UAAM,mBAAmB,CAAC,KAAK,SAAS,UAAU,KAAK;AACvD,QAAI,iBAAiD;AACrD,QAAI,CAAC,KAAK,MAAM;AACd,uBACE,uBAAuB,KAAK,WACxB,KAAK,oBACL;IACR;AACA,WAAOA,SAAc,GAAG;6BACC,SAAS,KAAK,iBAAgB,CAAE,CAAC;;sBAExC,kBAAkB,OAAO;yBACrB,CAAC,KAAK,QAAQ,gBAAiB,OAAO;yBACtC,CAAC,KAAK,QAAQ,gBAAiB,OAAO;wBACxC,gBAAgB;wBACf,CAAC,KAAK,QAAQ,KAAK,gBAAiB,OAAO;qBAC/C,CAAC,KAAK,QAAQ,KAAK,QAAQ;kBAC9B,KAAK,kBAAkB;UAC/B,KAAK,gBAAe,CAAE;UACtB,KAAK,aAAY,CAAE;UACnB,CAAC,KAAK,WAAW,KAAK,WAAU,IAAK,OAAO;UAC5C,KAAK,WAAW,KAAK,mBAAkB,IAAK,OAAO;UACnD,KAAK,OAAO,KAAK,WAAU,IAAK,KAAK,kBAAiB,CAAE;MAC5D,GAAG;EACP;EAEQ,aAAU;AAEhB,UAAM,EAAC,UAAS,IAAI;AACpB,WAAO;;;;gBAIK,KAAK,IAAI;oBACL,KAAK,YAAY,OAAO;kBAC1B,KAAK,UAAU,OAAO;sBAClB,aAAa,OAAO;UAChC,KAAK,kBAAiB,CAAE;;;EAGhC;EAEU,mBAAgB;AACxB,WAAO;MACL,aAAa,KAAK;MAClB,YAAY,KAAK,UAAU,KAAK;;EAEpC;EAEQ,aAAU;AAChB,WAAO;EACT;EAEQ,qBAAkB;AAExB,WAAO;;;EAGT;EAEQ,oBAAiB;AACvB,WAAO;EACT;EAEQ,kBAAe;AAErB,WAAO;;YAEC,KAAK,OAAO,SAAS,QAAQ;EACvC;EAEQ,eAAY;AAClB,UAAM,mBAAmB,CAAC,KAAK,SAAS,KAAK,YAAY,KAAK;AAE9D,WAAO;YACC,KAAK,OAAO,SAAS,OAAO;mBACrB,gBAAgB;EACjC;EAES,oBAAiB;AACxB,SAAK,WAAW,MAAM,MAAM,KAAK,aAAa;AAC9C,UAAM,kBAAiB;EACzB;;EAGQ,YAAY,OAAiB;AAInC,QAAI,CAAC,KAAK,QAAQ,KAAK,cAAc;AACnC,YAAM,yBAAwB;AAC9B,YAAM,eAAc;AACpB;IACF;EACF;;;;;EAMQ,MAAM,mBAAmB,OAAY;AAE3C,UAAM;AACN,QACE,CAAC,KAAK,UACN,KAAK,YACL,KAAK,gBACL,MAAM,kBACN;AACA;IACF;AAEA,SAAK,WAAW,CAAC,KAAK;AACtB,SAAK,cACH,IAAI,WAAW,SAAS,EAAC,SAAS,MAAM,UAAU,KAAI,CAAC,CAAC;AAI1D,SAAK,cAAc,IAAI,MAAM,UAAU,EAAC,SAAS,KAAI,CAAC,CAAC;EACzD;;CAnPA,MAAA;AACE,qBAAmB,UAAU;AAC/B,GAAC;AAGe,WAAA,iBAAiB;AAGjB,WAAA,oBAAoC;EAClD,MAAM;EACN,gBAAgB;;AAMwB,WAAA;EAAzC,SAAS,EAAC,MAAM,SAAS,SAAS,KAAI,CAAC;;AAUxC,WAAA;EADC,SAAS,EAAC,MAAM,SAAS,WAAW,iBAAiB,SAAS,KAAI,CAAC;;AAOpE,WAAA;EADC,SAAS,EAAC,MAAM,SAAS,WAAW,mBAAkB,CAAC;;AAM5C,WAAA;EAAX,SAAQ;;AAOG,WAAA;EAAX,SAAQ;;AAKG,WAAA;EAAX,SAAQ;;AAKqC,WAAA;EAA7C,SAAS,EAAC,WAAW,sBAAqB,CAAC;;AAMjB,WAAA;EAA1B,SAAS,EAAC,MAAM,QAAO,CAAC;;AAOiB,WAAA;EAAzC,SAAS,EAAC,MAAM,SAAS,SAAS,KAAI,CAAC;;AAM5B,WAAA;EAAX,SAAQ;;AAMkB,WAAA;EAA1B,SAAS,EAAC,SAAS,KAAI,CAAC;;AAuBR,WAAA;EAAhB,MAAK;;;;ACzID,IAAM,SAAS;;", "names": ["html"]}