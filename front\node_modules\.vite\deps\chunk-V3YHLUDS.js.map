{"version": 3, "sources": ["../../@material/web/labs/behaviors/form-associated.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {LitElement, PropertyDeclaration} from 'lit';\nimport {property} from 'lit/decorators.js';\n\nimport {internals, WithElementInternals} from './element-internals.js';\nimport {MixinBase, MixinReturn} from './mixin.js';\n\n/**\n * A form-associated element.\n *\n * IMPORTANT: Requires declares for lit-analyzer\n * @example\n * ```ts\n * const base = mixinFormAssociated(mixinElementInternals(LitElement));\n * class MyControl extends base {\n *   // Writable mixin properties for lit-html binding, needed for lit-analyzer\n *   declare disabled: boolean;\n *   declare name: string;\n * }\n * ```\n */\nexport interface FormAssociated {\n  /**\n   * The associated form element with which this element's value will submit.\n   */\n  readonly form: HTMLFormElement | null;\n\n  /**\n   * The labels this element is associated with.\n   */\n  readonly labels: NodeList;\n\n  /**\n   * The HTML name to use in form submission.\n   */\n  name: string;\n\n  /**\n   * Whether or not the element is disabled.\n   */\n  disabled: boolean;\n\n  /**\n   * Gets the current form value of a component.\n   *\n   * @return The current form value.\n   */\n  [getFormValue](): FormValue | null;\n\n  /**\n   * Gets the current form state of a component. Defaults to the component's\n   * `[formValue]`.\n   *\n   * Use this when the state of an element is different from its value, such as\n   * checkboxes (internal boolean state and a user string value).\n   *\n   * @return The current form state, defaults to the form value.\n   */\n  [getFormState](): FormValue | null;\n\n  /**\n   * A callback for when a form component should be disabled or enabled. This\n   * can be called in a variety of situations, such as disabled `<fieldset>`s.\n   *\n   * @param disabled Whether or not the form control should be disabled.\n   */\n  formDisabledCallback(disabled: boolean): void;\n\n  /**\n   * A callback for when the form requests to reset its value. Typically, the\n   * default value that is reset is represented in the attribute of an element.\n   *\n   * This means the attribute used for the value should not update as the value\n   * changes. For example, a checkbox should not change its default `checked`\n   * attribute when selected. Ensure form values do not reflect.\n   */\n  formResetCallback(): void;\n\n  /**\n   * A callback for when the form restores the state of a component. For\n   * example, when a page is reloaded or forms are autofilled.\n   *\n   * @param state The state to restore, or null to reset the form control's\n   *     value.\n   * @param reason The reason state was restored, either `'restore'` or\n   *   `'autocomplete'`.\n   */\n  formStateRestoreCallback(\n    state: FormRestoreState | null,\n    reason: FormRestoreReason,\n  ): void;\n\n  /**\n   * An optional callback for when the associated form changes.\n   *\n   * @param form The new associated form, or `null` if there is none.\n   */\n  formAssociatedCallback?(form: HTMLFormElement | null): void;\n}\n\n/**\n * The constructor of a `FormAssociated` element.\n */\nexport interface FormAssociatedConstructor {\n  /**\n   * Indicates that an element is participating in form association.\n   */\n  readonly formAssociated: true;\n}\n\n/**\n * A symbol property to retrieve the form value for an element.\n */\nexport const getFormValue = Symbol('getFormValue');\n\n/**\n * A symbol property to retrieve the form state for an element.\n */\nexport const getFormState = Symbol('getFormState');\n\n/**\n * Mixes in form-associated behavior for a class. This allows an element to add\n * values to `<form>` elements.\n *\n * Implementing classes should provide a `[formValue]` to return the current\n * value of the element, as well as reset and restore callbacks.\n *\n * @example\n * ```ts\n * const base = mixinFormAssociated(mixinElementInternals(LitElement));\n *\n * class MyControl extends base {\n *   \\@property()\n *   value = '';\n *\n *   override [getFormValue]() {\n *     return this.value;\n *   }\n *\n *   override formResetCallback() {\n *     const defaultValue = this.getAttribute('value');\n *     this.value = defaultValue;\n *   }\n *\n *   override formStateRestoreCallback(state: string) {\n *     this.value = state;\n *   }\n * }\n * ```\n *\n * Elements may optionally provide a `[formState]` if their values do not\n * represent the state of the component.\n *\n * @example\n * ```ts\n * const base = mixinFormAssociated(mixinElementInternals(LitElement));\n *\n * class MyCheckbox extends base {\n *   \\@property()\n *   value = 'on';\n *\n *   \\@property({type: Boolean})\n *   checked = false;\n *\n *   override [getFormValue]() {\n *     return this.checked ? this.value : null;\n *   }\n *\n *   override [getFormState]() {\n *     return String(this.checked);\n *   }\n *\n *   override formResetCallback() {\n *     const defaultValue = this.hasAttribute('checked');\n *     this.checked = defaultValue;\n *   }\n *\n *   override formStateRestoreCallback(state: string) {\n *     this.checked = Boolean(state);\n *   }\n * }\n * ```\n *\n * IMPORTANT: Requires declares for lit-analyzer\n * @example\n * ```ts\n * const base = mixinFormAssociated(mixinElementInternals(LitElement));\n * class MyControl extends base {\n *   // Writable mixin properties for lit-html binding, needed for lit-analyzer\n *   declare disabled: boolean;\n *   declare name: string;\n * }\n * ```\n *\n * @param base The class to mix functionality into. The base class must use\n *     `mixinElementInternals()`.\n * @return The provided class with `FormAssociated` mixed in.\n */\nexport function mixinFormAssociated<\n  T extends MixinBase<LitElement & WithElementInternals>,\n>(base: T): MixinReturn<T & FormAssociatedConstructor, FormAssociated> {\n  abstract class FormAssociatedElement extends base implements FormAssociated {\n    /** @nocollapse */\n    static readonly formAssociated = true;\n\n    get form() {\n      return this[internals].form;\n    }\n\n    get labels() {\n      return this[internals].labels;\n    }\n\n    // Use @property for the `name` and `disabled` properties to add them to the\n    // `observedAttributes` array and trigger `attributeChangedCallback()`.\n    //\n    // We don't use Lit's default getter/setter (`noAccessor: true`) because\n    // the attributes need to be updated synchronously to work with synchronous\n    // form APIs, and Lit updates attributes async by default.\n    @property({noAccessor: true})\n    get name() {\n      return this.getAttribute('name') ?? '';\n    }\n    set name(name: string) {\n      // Note: setting name to null or empty does not remove the attribute.\n      this.setAttribute('name', name);\n      // We don't need to call `requestUpdate()` since it's called synchronously\n      // in `attributeChangedCallback()`.\n    }\n\n    @property({type: Boolean, noAccessor: true})\n    get disabled() {\n      return this.hasAttribute('disabled');\n    }\n    set disabled(disabled: boolean) {\n      this.toggleAttribute('disabled', disabled);\n      // We don't need to call `requestUpdate()` since it's called synchronously\n      // in `attributeChangedCallback()`.\n    }\n\n    override attributeChangedCallback(\n      name: string,\n      old: string | null,\n      value: string | null,\n    ) {\n      // Manually `requestUpdate()` for `name` and `disabled` when their\n      // attribute or property changes.\n      // The properties update their attributes, so this callback is invoked\n      // immediately when the properties are set. We call `requestUpdate()` here\n      // instead of letting Lit set the properties from the attribute change.\n      // That would cause the properties to re-set the attribute and invoke this\n      // callback again in a loop. This leads to stale state when Lit tries to\n      // determine if a property changed or not.\n      if (name === 'name' || name === 'disabled') {\n        // Disabled's value is only false if the attribute is missing and null.\n        const oldValue = name === 'disabled' ? old !== null : old;\n        // Trigger a lit update when the attribute changes.\n        this.requestUpdate(name, oldValue);\n        return;\n      }\n\n      super.attributeChangedCallback(name, old, value);\n    }\n\n    override requestUpdate(\n      name?: PropertyKey,\n      oldValue?: unknown,\n      options?: PropertyDeclaration,\n    ) {\n      super.requestUpdate(name, oldValue, options);\n      // If any properties change, update the form value, which may have changed\n      // as well.\n      // Update the form value synchronously in `requestUpdate()` rather than\n      // `update()` or `updated()`, which are async. This is necessary to ensure\n      // that form data is updated in time for synchronous event listeners.\n      this[internals].setFormValue(this[getFormValue](), this[getFormState]());\n    }\n\n    [getFormValue](): FormValue | null {\n      // Closure does not allow abstract symbol members, so a default\n      // implementation is needed.\n      throw new Error('Implement [getFormValue]');\n    }\n\n    [getFormState](): FormValue | null {\n      return this[getFormValue]();\n    }\n\n    formDisabledCallback(disabled: boolean) {\n      this.disabled = disabled;\n    }\n\n    abstract formResetCallback(): void;\n\n    abstract formStateRestoreCallback(\n      state: FormRestoreState | null,\n      reason: FormRestoreReason,\n    ): void;\n  }\n\n  return FormAssociatedElement;\n}\n\n/**\n * A value that can be provided for form submission and state.\n */\nexport type FormValue = File | string | FormData;\n\n/**\n * A value to be restored for a component's form value. If a component's form\n * state is a `FormData` object, its entry list of name and values will be\n * provided.\n */\nexport type FormRestoreState =\n  | File\n  | string\n  | Array<[string, FormDataEntryValue]>;\n\n/**\n * The reason a form component is being restored for, either `'restore'` for\n * browser restoration or `'autocomplete'` for restoring user values.\n */\nexport type FormRestoreReason = 'restore' | 'autocomplete';\n"], "mappings": ";;;;;;;;;;;AAsHO,IAAM,eAAe,OAAO,cAAc;AAK1C,IAAM,eAAe,OAAO,cAAc;AAgF3C,SAAU,oBAEd,MAAO;EACP,MAAe,8BAA8B,KAAI;IAI/C,IAAI,OAAI;AACN,aAAO,KAAK,SAAS,EAAE;IACzB;IAEA,IAAI,SAAM;AACR,aAAO,KAAK,SAAS,EAAE;IACzB;;;;;;;IASA,IAAI,OAAI;AACN,aAAO,KAAK,aAAa,MAAM,KAAK;IACtC;IACA,IAAI,KAAK,MAAY;AAEnB,WAAK,aAAa,QAAQ,IAAI;IAGhC;IAGA,IAAI,WAAQ;AACV,aAAO,KAAK,aAAa,UAAU;IACrC;IACA,IAAI,SAAS,UAAiB;AAC5B,WAAK,gBAAgB,YAAY,QAAQ;IAG3C;IAES,yBACP,MACA,KACA,OAAoB;AAUpB,UAAI,SAAS,UAAU,SAAS,YAAY;AAE1C,cAAM,WAAW,SAAS,aAAa,QAAQ,OAAO;AAEtD,aAAK,cAAc,MAAM,QAAQ;AACjC;MACF;AAEA,YAAM,yBAAyB,MAAM,KAAK,KAAK;IACjD;IAES,cACP,MACA,UACA,SAA6B;AAE7B,YAAM,cAAc,MAAM,UAAU,OAAO;AAM3C,WAAK,SAAS,EAAE,aAAa,KAAK,YAAY,EAAC,GAAI,KAAK,YAAY,EAAC,CAAE;IACzE;IAEA,CAAC,YAAY,IAAC;AAGZ,YAAM,IAAI,MAAM,0BAA0B;IAC5C;IAEA,CAAC,YAAY,IAAC;AACZ,aAAO,KAAK,YAAY,EAAC;IAC3B;IAEA,qBAAqB,UAAiB;AACpC,WAAK,WAAW;IAClB;;AAvFgB,wBAAA,iBAAiB;AAiBjC,aAAA;IADC,SAAS,EAAC,YAAY,KAAI,CAAC;;AAY5B,aAAA;IADC,SAAS,EAAC,MAAM,SAAS,YAAY,KAAI,CAAC;;AAsE7C,SAAO;AACT;", "names": []}