{"version": 3, "sources": ["../../@material/web/labs/badge/internal/badge.ts", "../../@material/web/labs/badge/internal/badge-styles.ts", "../../@material/web/labs/badge/badge.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2022 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {html, LitElement} from 'lit';\nimport {property} from 'lit/decorators.js';\nimport {classMap} from 'lit/directives/class-map.js';\n\n/**\n * b/265340196 - add docs\n */\nexport class Badge extends LitElement {\n  @property() value = '';\n\n  protected override render() {\n    const classes = {'md3-badge--large': this.value};\n\n    return html`<div class=\"md3-badge ${classMap(classes)}\">\n      <p class=\"md3-badge__value\">${this.value}</p>\n    </div>`;\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n// Generated stylesheet for ./labs/badge/internal/badge-styles.css.\nimport {css} from 'lit';\nexport const styles = css`:host{--_color: var(--md-badge-color, var(--md-sys-color-error, #b3261e));--_large-color: var(--md-badge-large-color, var(--md-sys-color-error, #b3261e));--_large-label-text-color: var(--md-badge-large-label-text-color, var(--md-sys-color-on-error, #fff));--_large-label-text-font: var(--md-badge-large-label-text-font, var(--md-sys-typescale-label-small-font, var(--md-ref-typeface-plain, Roboto)));--_large-label-text-line-height: var(--md-badge-large-label-text-line-height, var(--md-sys-typescale-label-small-line-height, 1rem));--_large-label-text-size: var(--md-badge-large-label-text-size, var(--md-sys-typescale-label-small-size, 0.6875rem));--_large-label-text-weight: var(--md-badge-large-label-text-weight, var(--md-sys-typescale-label-small-weight, var(--md-ref-typeface-weight-medium, 500)));--_large-shape: var(--md-badge-large-shape, var(--md-sys-shape-corner-full, 9999px));--_large-size: var(--md-badge-large-size, 16px);--_shape: var(--md-badge-shape, var(--md-sys-shape-corner-full, 9999px));--_size: var(--md-badge-size, 6px)}.md3-badge{inset-inline-start:50%;margin-inline-start:6px;margin-block-start:4px;position:absolute;inset-block-start:0px;background-color:var(--_color);border-radius:var(--_shape);height:var(--_size)}.md3-badge:not(.md3-badge--large){width:var(--_size)}.md3-badge.md3-badge--large{display:flex;flex-direction:column;justify-content:center;margin-inline-start:2px;margin-block-start:1px;background-color:var(--_large-color);border-radius:var(--_large-shape);height:var(--_large-size);min-width:var(--_large-size);color:var(--_large-label-text-color)}.md3-badge.md3-badge--large .md3-badge__value{padding:0px 4px;text-align:center}.md3-badge__value{font-family:var(--_large-label-text-font);font-size:var(--_large-label-text-size);line-height:var(--_large-label-text-line-height);font-weight:var(--_large-label-text-weight)}\n`;\n", "/**\n * @license\n * Copyright 2022 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {Badge} from './internal/badge.js';\nimport {styles} from './internal/badge-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-badge': MdBadge;\n  }\n}\n\n/**\n * @final\n * @suppress {visibility}\n */\n@customElement('md-badge')\nexport class MdBadge extends Badge {\n  static override styles: CSSResultOrNative[] = [styles];\n}\n"], "mappings": ";;;;;;;;;;;;;;;AAaM,IAAO,QAAP,cAAqB,WAAU;EAArC,cAAA;;AACc,SAAA,QAAQ;EAStB;EAPqB,SAAM;AACvB,UAAM,UAAU,EAAC,oBAAoB,KAAK,MAAK;AAE/C,WAAO,6BAA6B,SAAS,OAAO,CAAC;oCACrB,KAAK,KAAK;;EAE5C;;AARY,WAAA;EAAX,SAAQ;;;;ACPJ,IAAM,SAAS;;;;ACgBf,IAAM,UAAN,MAAMA,iBAAgB,MAAK;;AAChB,QAAA,SAA8B,CAAC,MAAM;AAD1C,UAAO,WAAA;EADnB,cAAc,UAAU;GACZ,OAAO;", "names": ["MdBadge"]}