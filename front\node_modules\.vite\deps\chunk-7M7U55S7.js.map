{"version": 3, "sources": ["../../@material/web/list/internal/list-controller.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {\n  activateFirstItem,\n  activateLastItem,\n  activateNextItem,\n  activatePreviousItem,\n  getActiveItem,\n  getFirstActivatableItem,\n  ListItem,\n} from './list-navigation-helpers.js';\n\n// TODO: move this file to List and make List use this\n\n/**\n * Default keys that trigger navigation.\n */\n// tslint:disable:enforce-name-casing Following Enum style\nexport const NavigableKeys = {\n  ArrowDown: 'ArrowDown',\n  ArrowLeft: 'ArrowLeft',\n  ArrowUp: 'ArrowUp',\n  ArrowRight: 'ArrowRight',\n  Home: 'Home',\n  End: 'End',\n} as const;\n// tslint:enable:enforce-name-casing\n\n/**\n * Default set of navigable keys.\n */\nexport type NavigableKeys = (typeof NavigableKeys)[keyof typeof NavigableKeys];\n\n/**\n * The configuration object to customize the behavior of the List Controller\n */\nexport interface ListControllerConfig<Item extends ListItem> {\n  /**\n   * A function that determines whether or not the given element is an Item\n   */\n  isItem: (item: HTMLElement) => item is Item;\n  /**\n   * A function that returns an array of elements to consider as items. For\n   * example, all the slotted elements.\n   */\n  getPossibleItems: () => HTMLElement[];\n  /**\n   * A function that returns whether or not the list is in an RTL context.\n   */\n  isRtl: () => boolean;\n  /**\n   * Deactivates an item such as setting the tabindex to -1 and or sets selected\n   * to false.\n   */\n  deactivateItem: (item: Item) => void;\n  /**\n   * Activates an item such as setting the tabindex to 1 and or sets selected to\n   * true (but does not focus).\n   */\n  activateItem: (item: Item) => void;\n  /**\n   * Whether or not the key should be handled by the list for navigation.\n   */\n  isNavigableKey: (key: string) => boolean;\n  /**\n   * Whether or not the item can be activated. Defaults to items that are not\n   * disabled.\n   */\n  isActivatable?: (item: Item) => boolean;\n  /**\n   * Whether or not navigating past the end of the list wraps to the beginning\n   * and vice versa. Defaults to true.\n   */\n  wrapNavigation?: () => boolean;\n}\n\n/**\n * A controller that handles list keyboard navigation and item management.\n */\nexport class ListController<Item extends ListItem> {\n  isItem: (item: HTMLElement) => item is Item;\n  private readonly getPossibleItems: () => HTMLElement[];\n  private readonly isRtl: () => boolean;\n  private readonly deactivateItem: (item: Item) => void;\n  private readonly activateItem: (item: Item) => void;\n  private readonly isNavigableKey: (key: string) => boolean;\n  private readonly isActivatable?: (item: Item) => boolean;\n  private readonly wrapNavigation: () => boolean;\n\n  constructor(config: ListControllerConfig<Item>) {\n    const {\n      isItem,\n      getPossibleItems,\n      isRtl,\n      deactivateItem,\n      activateItem,\n      isNavigableKey,\n      isActivatable,\n      wrapNavigation,\n    } = config;\n    this.isItem = isItem;\n    this.getPossibleItems = getPossibleItems;\n    this.isRtl = isRtl;\n    this.deactivateItem = deactivateItem;\n    this.activateItem = activateItem;\n    this.isNavigableKey = isNavigableKey;\n    this.isActivatable = isActivatable;\n    this.wrapNavigation = wrapNavigation ?? (() => true);\n  }\n\n  /**\n   * The items being managed by the list. Additionally, attempts to see if the\n   * object has a sub-item in the `.item` property.\n   */\n  get items(): Item[] {\n    const maybeItems = this.getPossibleItems();\n    const items: Item[] = [];\n\n    for (const itemOrParent of maybeItems) {\n      const isItem = this.isItem(itemOrParent);\n      // if the item is a list item, add it to the list of items\n      if (isItem) {\n        items.push(itemOrParent);\n        continue;\n      }\n\n      // If the item exposes an `item` property check if it is a list item.\n      const subItem = (itemOrParent as HTMLElement & {item?: Item}).item;\n      if (subItem && this.isItem(subItem)) {\n        items.push(subItem);\n      }\n    }\n\n    return items;\n  }\n\n  /**\n   * Handles keyboard navigation. Should be bound to the node that will act as\n   * the List.\n   */\n  handleKeydown = (event: KeyboardEvent) => {\n    const key = event.key;\n    if (event.defaultPrevented || !this.isNavigableKey(key)) {\n      return;\n    }\n    // do not use this.items directly in upcoming calculations so we don't\n    // re-query the DOM unnecessarily\n    const items = this.items;\n\n    if (!items.length) {\n      return;\n    }\n\n    const activeItemRecord = getActiveItem(items, this.isActivatable);\n\n    event.preventDefault();\n\n    const isRtl = this.isRtl();\n    const inlinePrevious = isRtl\n      ? NavigableKeys.ArrowRight\n      : NavigableKeys.ArrowLeft;\n    const inlineNext = isRtl\n      ? NavigableKeys.ArrowLeft\n      : NavigableKeys.ArrowRight;\n\n    let nextActiveItem: Item | null = null;\n    switch (key) {\n      // Activate the next item\n      case NavigableKeys.ArrowDown:\n      case inlineNext:\n        nextActiveItem = activateNextItem(\n          items,\n          activeItemRecord,\n          this.isActivatable,\n          this.wrapNavigation(),\n        );\n        break;\n\n      // Activate the previous item\n      case NavigableKeys.ArrowUp:\n      case inlinePrevious:\n        nextActiveItem = activatePreviousItem(\n          items,\n          activeItemRecord,\n          this.isActivatable,\n          this.wrapNavigation(),\n        );\n        break;\n\n      // Activate the first item\n      case NavigableKeys.Home:\n        nextActiveItem = activateFirstItem(items, this.isActivatable);\n        break;\n\n      // Activate the last item\n      case NavigableKeys.End:\n        nextActiveItem = activateLastItem(items, this.isActivatable);\n        break;\n\n      default:\n        break;\n    }\n\n    if (\n      nextActiveItem &&\n      activeItemRecord &&\n      activeItemRecord.item !== nextActiveItem\n    ) {\n      // If a new item was activated, remove the tabindex of the previous\n      // activated item.\n      activeItemRecord.item.tabIndex = -1;\n    }\n  };\n\n  /**\n   * Activates the next item in the list. If at the end of the list, the first\n   * item will be activated.\n   *\n   * @return The activated list item or `null` if there are no items.\n   */\n  activateNextItem(): Item | null {\n    const items = this.items;\n    const activeItemRecord = getActiveItem(items, this.isActivatable);\n    if (activeItemRecord) {\n      activeItemRecord.item.tabIndex = -1;\n    }\n    return activateNextItem(\n      items,\n      activeItemRecord,\n      this.isActivatable,\n      this.wrapNavigation(),\n    );\n  }\n\n  /**\n   * Activates the previous item in the list. If at the start of the list, the\n   * last item will be activated.\n   *\n   * @return The activated list item or `null` if there are no items.\n   */\n  activatePreviousItem(): Item | null {\n    const items = this.items;\n    const activeItemRecord = getActiveItem(items, this.isActivatable);\n    if (activeItemRecord) {\n      activeItemRecord.item.tabIndex = -1;\n    }\n    return activatePreviousItem(\n      items,\n      activeItemRecord,\n      this.isActivatable,\n      this.wrapNavigation(),\n    );\n  }\n\n  /**\n   * Listener to be bound to the `deactivate-items` item event.\n   */\n  onDeactivateItems = () => {\n    const items = this.items;\n\n    for (const item of items) {\n      this.deactivateItem(item);\n    }\n  };\n\n  /**\n   * Listener to be bound to the `request-activation` item event..\n   */\n  onRequestActivation = (event: Event) => {\n    this.onDeactivateItems();\n    const target = event.target as Item;\n    this.activateItem(target);\n    target.focus();\n  };\n\n  /**\n   * Listener to be bound to the `slotchange` event for the slot that renders\n   * the items.\n   */\n  onSlotchange = () => {\n    const items = this.items;\n    // Whether we have encountered an item that has been activated\n    let encounteredActivated = false;\n\n    for (const item of items) {\n      const isActivated = !item.disabled && item.tabIndex > -1;\n\n      if (isActivated && !encounteredActivated) {\n        encounteredActivated = true;\n        item.tabIndex = 0;\n        continue;\n      }\n\n      // Deactivate the rest including disabled\n      item.tabIndex = -1;\n    }\n\n    if (encounteredActivated) {\n      return;\n    }\n\n    const firstActivatableItem = getFirstActivatableItem(\n      items,\n      this.isActivatable,\n    );\n\n    if (!firstActivatableItem) {\n      return;\n    }\n\n    firstActivatableItem.tabIndex = 0;\n  };\n}\n"], "mappings": ";;;;;;;;;;AAsBO,IAAM,gBAAgB;EAC3B,WAAW;EACX,WAAW;EACX,SAAS;EACT,YAAY;EACZ,MAAM;EACN,KAAK;;AAuDD,IAAO,iBAAP,MAAqB;EAUzB,YAAY,QAAkC;AAmD9C,SAAA,gBAAgB,CAAC,UAAwB;AACvC,YAAM,MAAM,MAAM;AAClB,UAAI,MAAM,oBAAoB,CAAC,KAAK,eAAe,GAAG,GAAG;AACvD;MACF;AAGA,YAAM,QAAQ,KAAK;AAEnB,UAAI,CAAC,MAAM,QAAQ;AACjB;MACF;AAEA,YAAM,mBAAmB,cAAc,OAAO,KAAK,aAAa;AAEhE,YAAM,eAAc;AAEpB,YAAMA,SAAQ,KAAK,MAAK;AACxB,YAAM,iBAAiBA,SACnB,cAAc,aACd,cAAc;AAClB,YAAM,aAAaA,SACf,cAAc,YACd,cAAc;AAElB,UAAI,iBAA8B;AAClC,cAAQ,KAAK;;QAEX,KAAK,cAAc;QACnB,KAAK;AACH,2BAAiB,iBACf,OACA,kBACA,KAAK,eACL,KAAK,eAAc,CAAE;AAEvB;;QAGF,KAAK,cAAc;QACnB,KAAK;AACH,2BAAiB,qBACf,OACA,kBACA,KAAK,eACL,KAAK,eAAc,CAAE;AAEvB;;QAGF,KAAK,cAAc;AACjB,2BAAiB,kBAAkB,OAAO,KAAK,aAAa;AAC5D;;QAGF,KAAK,cAAc;AACjB,2BAAiB,iBAAiB,OAAO,KAAK,aAAa;AAC3D;QAEF;AACE;MACJ;AAEA,UACE,kBACA,oBACA,iBAAiB,SAAS,gBAC1B;AAGA,yBAAiB,KAAK,WAAW;MACnC;IACF;AA6CA,SAAA,oBAAoB,MAAK;AACvB,YAAM,QAAQ,KAAK;AAEnB,iBAAW,QAAQ,OAAO;AACxB,aAAK,eAAe,IAAI;MAC1B;IACF;AAKA,SAAA,sBAAsB,CAAC,UAAgB;AACrC,WAAK,kBAAiB;AACtB,YAAM,SAAS,MAAM;AACrB,WAAK,aAAa,MAAM;AACxB,aAAO,MAAK;IACd;AAMA,SAAA,eAAe,MAAK;AAClB,YAAM,QAAQ,KAAK;AAEnB,UAAI,uBAAuB;AAE3B,iBAAW,QAAQ,OAAO;AACxB,cAAM,cAAc,CAAC,KAAK,YAAY,KAAK,WAAW;AAEtD,YAAI,eAAe,CAAC,sBAAsB;AACxC,iCAAuB;AACvB,eAAK,WAAW;AAChB;QACF;AAGA,aAAK,WAAW;MAClB;AAEA,UAAI,sBAAsB;AACxB;MACF;AAEA,YAAM,uBAAuB,wBAC3B,OACA,KAAK,aAAa;AAGpB,UAAI,CAAC,sBAAsB;AACzB;MACF;AAEA,2BAAqB,WAAW;IAClC;AA7NE,UAAM,EACJ,QACA,kBACA,OACA,gBACA,cACA,gBACA,eACA,eAAc,IACZ;AACJ,SAAK,SAAS;AACd,SAAK,mBAAmB;AACxB,SAAK,QAAQ;AACb,SAAK,iBAAiB;AACtB,SAAK,eAAe;AACpB,SAAK,iBAAiB;AACtB,SAAK,gBAAgB;AACrB,SAAK,iBAAiB,mBAAmB,MAAM;EACjD;;;;;EAMA,IAAI,QAAK;AACP,UAAM,aAAa,KAAK,iBAAgB;AACxC,UAAM,QAAgB,CAAA;AAEtB,eAAW,gBAAgB,YAAY;AACrC,YAAM,SAAS,KAAK,OAAO,YAAY;AAEvC,UAAI,QAAQ;AACV,cAAM,KAAK,YAAY;AACvB;MACF;AAGA,YAAM,UAAW,aAA6C;AAC9D,UAAI,WAAW,KAAK,OAAO,OAAO,GAAG;AACnC,cAAM,KAAK,OAAO;MACpB;IACF;AAEA,WAAO;EACT;;;;;;;EAsFA,mBAAgB;AACd,UAAM,QAAQ,KAAK;AACnB,UAAM,mBAAmB,cAAc,OAAO,KAAK,aAAa;AAChE,QAAI,kBAAkB;AACpB,uBAAiB,KAAK,WAAW;IACnC;AACA,WAAO,iBACL,OACA,kBACA,KAAK,eACL,KAAK,eAAc,CAAE;EAEzB;;;;;;;EAQA,uBAAoB;AAClB,UAAM,QAAQ,KAAK;AACnB,UAAM,mBAAmB,cAAc,OAAO,KAAK,aAAa;AAChE,QAAI,kBAAkB;AACpB,uBAAiB,KAAK,WAAW;IACnC;AACA,WAAO,qBACL,OACA,kBACA,KAAK,eACL,KAAK,eAAc,CAAE;EAEzB;;", "names": ["isRtl"]}