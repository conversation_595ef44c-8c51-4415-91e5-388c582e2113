import {
  ListController,
  NavigableKeys
} from "./chunk-7M7U55S7.js";
import "./chunk-SHO7BEJJ.js";
import {
  customElement,
  queryAssignedElements
} from "./chunk-T3WMJB5E.js";
import {
  LitElement,
  css,
  html,
  isServer
} from "./chunk-4GZ3EDRH.js";
import {
  __decorate
} from "./chunk-HMZZ7KLC.js";
import "./chunk-G3PMV62Z.js";

// node_modules/@material/web/list/internal/list.js
var NAVIGABLE_KEY_SET = new Set(Object.values(NavigableKeys));
var List = class extends LitElement {
  /** @export */
  get items() {
    return this.listController.items;
  }
  constructor() {
    super();
    this.listController = new ListController({
      isItem: (item) => item.hasAttribute("md-list-item"),
      getPossibleItems: () => this.slotItems,
      isRtl: () => getComputedStyle(this).direction === "rtl",
      deactivateItem: (item) => {
        item.tabIndex = -1;
      },
      activateItem: (item) => {
        item.tabIndex = 0;
      },
      isNavigableKey: (key) => NAVIGABLE_KEY_SET.has(key),
      isActivatable: (item) => !item.disabled && item.type !== "text"
    });
    this.internals = // Cast needed for closure
    this.attachInternals();
    if (!isServer) {
      this.internals.role = "list";
      this.addEventListener("keydown", this.listController.handleKeydown);
    }
  }
  render() {
    return html`
      <slot
        @deactivate-items=${this.listController.onDeactivateItems}
        @request-activation=${this.listController.onRequestActivation}
        @slotchange=${this.listController.onSlotchange}>
      </slot>
    `;
  }
  /**
   * Activates the next item in the list. If at the end of the list, the first
   * item will be activated.
   *
   * @return The activated list item or `null` if there are no items.
   */
  activateNextItem() {
    return this.listController.activateNextItem();
  }
  /**
   * Activates the previous item in the list. If at the start of the list, the
   * last item will be activated.
   *
   * @return The activated list item or `null` if there are no items.
   */
  activatePreviousItem() {
    return this.listController.activatePreviousItem();
  }
};
__decorate([
  queryAssignedElements({ flatten: true })
], List.prototype, "slotItems", void 0);

// node_modules/@material/web/list/internal/list-styles.js
var styles = css`:host{background:var(--md-list-container-color, var(--md-sys-color-surface, #fef7ff));color:unset;display:flex;flex-direction:column;outline:none;padding:8px 0;position:relative}
`;

// node_modules/@material/web/list/list.js
var MdList = class MdList2 extends List {
};
MdList.styles = [styles];
MdList = __decorate([
  customElement("md-list")
], MdList);
export {
  MdList
};
/*! Bundled license information:

@material/web/list/internal/list.js:
@material/web/list/list.js:
  (**
   * @license
   * Copyright 2021 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   *)

@material/web/list/internal/list-styles.js:
  (**
   * @license
   * Copyright 2024 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   *)
*/
//# sourceMappingURL=@material_web_list_list__js.js.map
