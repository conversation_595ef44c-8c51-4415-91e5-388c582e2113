{"version": 3, "sources": ["../../@material/web/labs/navigationdrawer/internal/shared-styles.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2024 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n// Generated stylesheet for ./labs/navigationdrawer/internal/shared-styles.css.\nimport {css} from 'lit';\nexport const styles = css`.md3-navigation-drawer-modal,.md3-navigation-drawer{background-color:var(--_container-color);border-radius:var(--_container-shape);height:var(--_container-height)}.md3-navigation-drawer-modal.md3-navigation-drawer-modal--opened,.md3-navigation-drawer.md3-navigation-drawer--opened{inline-size:var(--_container-width)}.md3-navigation-drawer-modal .md3-navigation-drawer-modal__slot-content,.md3-navigation-drawer .md3-navigation-drawer__slot-content{min-inline-size:var(--_container-width);max-inline-size:var(--_container-width)}\n`;\n"], "mappings": ";;;;;AAOO,IAAM,SAAS;;", "names": []}