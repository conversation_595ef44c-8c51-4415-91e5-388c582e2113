import {
  mixinOnReportValidity,
  onReportValidity
} from "./chunk-5XCMY3ZR.js";
import {
  styleMap
} from "./chunk-DAKWLTHT.js";
import {
  html as html2
} from "./chunk-FNIFQ77A.js";
import {
  Val<PERSON><PERSON>,
  createValidator,
  getValidityAnchor,
  mixinConstraintValidation
} from "./chunk-QLIBTWMU.js";
import {
  getFormValue,
  mixinFormAssociated
} from "./chunk-V3YHLUDS.js";
import {
  mixinElementInternals
} from "./chunk-N2ETY4JQ.js";
import {
  redispatchEvent
} from "./chunk-4X3LRXT2.js";
import {
  mixinDelegatesAria
} from "./chunk-GXE4MBY5.js";
import {
  Directive,
  PartType,
  classMap,
  directive
} from "./chunk-SZQCPKZF.js";
import {
  property,
  query,
  queryAssignedElements,
  state
} from "./chunk-T3WMJB5E.js";
import {
  LitElement,
  _$LH,
  css,
  html,
  noChange,
  nothing
} from "./chunk-4GZ3EDRH.js";
import {
  __decorate
} from "./chunk-HMZZ7KLC.js";

// node_modules/@material/web/textfield/internal/shared-styles.js
var styles = css`:host{display:inline-flex;outline:none;resize:both;text-align:start;-webkit-tap-highlight-color:rgba(0,0,0,0)}.text-field,.field{width:100%}.text-field{display:inline-flex}.field{cursor:text}.disabled .field{cursor:default}.text-field,.textarea .field{resize:inherit}slot[name=container]{border-radius:inherit}.icon{color:currentColor;display:flex;align-items:center;justify-content:center;fill:currentColor;position:relative}.icon ::slotted(*){display:flex;position:absolute}[has-start] .icon.leading{font-size:var(--_leading-icon-size);height:var(--_leading-icon-size);width:var(--_leading-icon-size)}[has-end] .icon.trailing{font-size:var(--_trailing-icon-size);height:var(--_trailing-icon-size);width:var(--_trailing-icon-size)}.input-wrapper{display:flex}.input-wrapper>*{all:inherit;padding:0}.input{caret-color:var(--_caret-color);overflow-x:hidden;text-align:inherit}.input::placeholder{color:currentColor;opacity:1}.input::-webkit-calendar-picker-indicator{display:none}.input::-webkit-search-decoration,.input::-webkit-search-cancel-button{display:none}@media(forced-colors: active){.input{background:none}}.no-spinner .input::-webkit-inner-spin-button,.no-spinner .input::-webkit-outer-spin-button{display:none}.no-spinner .input[type=number]{-moz-appearance:textfield}:focus-within .input{caret-color:var(--_focus-caret-color)}.error:focus-within .input{caret-color:var(--_error-focus-caret-color)}.text-field:not(.disabled) .prefix{color:var(--_input-text-prefix-color)}.text-field:not(.disabled) .suffix{color:var(--_input-text-suffix-color)}.text-field:not(.disabled) .input::placeholder{color:var(--_input-text-placeholder-color)}.prefix,.suffix{text-wrap:nowrap;width:min-content}.prefix{padding-inline-end:var(--_input-text-prefix-trailing-space)}.suffix{padding-inline-start:var(--_input-text-suffix-leading-space)}
`;

// node_modules/lit-html/development/directive-helpers.js
var { _ChildPart: ChildPart } = _$LH;
var ENABLE_SHADYDOM_NOPATCH = true;
var wrap = ENABLE_SHADYDOM_NOPATCH && window.ShadyDOM?.inUse && window.ShadyDOM?.noPatch === true ? window.ShadyDOM.wrap : (node) => node;
var isSingleExpression = (part) => part.strings === void 0;
var RESET_VALUE = {};
var setCommittedValue = (part, value = RESET_VALUE) => part._$committedValue = value;

// node_modules/lit-html/development/directives/live.js
var LiveDirective = class extends Directive {
  constructor(partInfo) {
    super(partInfo);
    if (!(partInfo.type === PartType.PROPERTY || partInfo.type === PartType.ATTRIBUTE || partInfo.type === PartType.BOOLEAN_ATTRIBUTE)) {
      throw new Error("The `live` directive is not allowed on child or event bindings");
    }
    if (!isSingleExpression(partInfo)) {
      throw new Error("`live` bindings can only contain a single expression");
    }
  }
  render(value) {
    return value;
  }
  update(part, [value]) {
    if (value === noChange || value === nothing) {
      return value;
    }
    const element = part.element;
    const name = part.name;
    if (part.type === PartType.PROPERTY) {
      if (value === element[name]) {
        return noChange;
      }
    } else if (part.type === PartType.BOOLEAN_ATTRIBUTE) {
      if (!!value === element.hasAttribute(name)) {
        return noChange;
      }
    } else if (part.type === PartType.ATTRIBUTE) {
      if (element.getAttribute(name) === String(value)) {
        return noChange;
      }
    }
    setCommittedValue(part);
    return value;
  }
};
var live = directive(LiveDirective);

// node_modules/@material/web/internal/controller/string-converter.js
var stringConverter = {
  fromAttribute(value) {
    return value ?? "";
  },
  toAttribute(value) {
    return value || null;
  }
};

// node_modules/@material/web/labs/behaviors/validators/text-field-validator.js
var TextFieldValidator = class extends Validator {
  computeValidity({ state: state2, renderedControl }) {
    let inputOrTextArea = renderedControl;
    if (isInputState(state2) && !inputOrTextArea) {
      inputOrTextArea = this.inputControl || document.createElement("input");
      this.inputControl = inputOrTextArea;
    } else if (!inputOrTextArea) {
      inputOrTextArea = this.textAreaControl || document.createElement("textarea");
      this.textAreaControl = inputOrTextArea;
    }
    const input = isInputState(state2) ? inputOrTextArea : null;
    if (input) {
      input.type = state2.type;
    }
    if (inputOrTextArea.value !== state2.value) {
      inputOrTextArea.value = state2.value;
    }
    inputOrTextArea.required = state2.required;
    if (input) {
      const inputState = state2;
      if (inputState.pattern) {
        input.pattern = inputState.pattern;
      } else {
        input.removeAttribute("pattern");
      }
      if (inputState.min) {
        input.min = inputState.min;
      } else {
        input.removeAttribute("min");
      }
      if (inputState.max) {
        input.max = inputState.max;
      } else {
        input.removeAttribute("max");
      }
      if (inputState.step) {
        input.step = inputState.step;
      } else {
        input.removeAttribute("step");
      }
    }
    if ((state2.minLength ?? -1) > -1) {
      inputOrTextArea.setAttribute("minlength", String(state2.minLength));
    } else {
      inputOrTextArea.removeAttribute("minlength");
    }
    if ((state2.maxLength ?? -1) > -1) {
      inputOrTextArea.setAttribute("maxlength", String(state2.maxLength));
    } else {
      inputOrTextArea.removeAttribute("maxlength");
    }
    return {
      validity: inputOrTextArea.validity,
      validationMessage: inputOrTextArea.validationMessage
    };
  }
  equals({ state: prev }, { state: next }) {
    const inputOrTextAreaEqual = prev.type === next.type && prev.value === next.value && prev.required === next.required && prev.minLength === next.minLength && prev.maxLength === next.maxLength;
    if (!isInputState(prev) || !isInputState(next)) {
      return inputOrTextAreaEqual;
    }
    return inputOrTextAreaEqual && prev.pattern === next.pattern && prev.min === next.min && prev.max === next.max && prev.step === next.step;
  }
  copy({ state: state2 }) {
    return {
      state: isInputState(state2) ? this.copyInput(state2) : this.copyTextArea(state2),
      renderedControl: null
    };
  }
  copyInput(state2) {
    const { type, pattern, min, max, step } = state2;
    return {
      ...this.copySharedState(state2),
      type,
      pattern,
      min,
      max,
      step
    };
  }
  copyTextArea(state2) {
    return {
      ...this.copySharedState(state2),
      type: state2.type
    };
  }
  copySharedState({ value, required, minLength, maxLength }) {
    return { value, required, minLength, maxLength };
  }
};
function isInputState(state2) {
  return state2.type !== "textarea";
}

// node_modules/@material/web/textfield/internal/text-field.js
var textFieldBaseClass = mixinDelegatesAria(mixinOnReportValidity(mixinConstraintValidation(mixinFormAssociated(mixinElementInternals(LitElement)))));
var TextField = class extends textFieldBaseClass {
  constructor() {
    super(...arguments);
    this.error = false;
    this.errorText = "";
    this.label = "";
    this.noAsterisk = false;
    this.required = false;
    this.value = "";
    this.prefixText = "";
    this.suffixText = "";
    this.hasLeadingIcon = false;
    this.hasTrailingIcon = false;
    this.supportingText = "";
    this.textDirection = "";
    this.rows = 2;
    this.cols = 20;
    this.inputMode = "";
    this.max = "";
    this.maxLength = -1;
    this.min = "";
    this.minLength = -1;
    this.noSpinner = false;
    this.pattern = "";
    this.placeholder = "";
    this.readOnly = false;
    this.multiple = false;
    this.step = "";
    this.type = "text";
    this.autocomplete = "";
    this.dirty = false;
    this.focused = false;
    this.nativeError = false;
    this.nativeErrorText = "";
  }
  /**
   * Gets or sets the direction in which selection occurred.
   */
  get selectionDirection() {
    return this.getInputOrTextarea().selectionDirection;
  }
  set selectionDirection(value) {
    this.getInputOrTextarea().selectionDirection = value;
  }
  /**
   * Gets or sets the end position or offset of a text selection.
   */
  get selectionEnd() {
    return this.getInputOrTextarea().selectionEnd;
  }
  set selectionEnd(value) {
    this.getInputOrTextarea().selectionEnd = value;
  }
  /**
   * Gets or sets the starting position or offset of a text selection.
   */
  get selectionStart() {
    return this.getInputOrTextarea().selectionStart;
  }
  set selectionStart(value) {
    this.getInputOrTextarea().selectionStart = value;
  }
  /**
   * The text field's value as a number.
   */
  get valueAsNumber() {
    const input = this.getInput();
    if (!input) {
      return NaN;
    }
    return input.valueAsNumber;
  }
  set valueAsNumber(value) {
    const input = this.getInput();
    if (!input) {
      return;
    }
    input.valueAsNumber = value;
    this.value = input.value;
  }
  /**
   * The text field's value as a Date.
   */
  get valueAsDate() {
    const input = this.getInput();
    if (!input) {
      return null;
    }
    return input.valueAsDate;
  }
  set valueAsDate(value) {
    const input = this.getInput();
    if (!input) {
      return;
    }
    input.valueAsDate = value;
    this.value = input.value;
  }
  get hasError() {
    return this.error || this.nativeError;
  }
  /**
   * Selects all the text in the text field.
   *
   * https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/select
   */
  select() {
    this.getInputOrTextarea().select();
  }
  setRangeText(...args) {
    this.getInputOrTextarea().setRangeText(...args);
    this.value = this.getInputOrTextarea().value;
  }
  /**
   * Sets the start and end positions of a selection in the text field.
   *
   * https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/setSelectionRange
   *
   * @param start The offset into the text field for the start of the selection.
   * @param end The offset into the text field for the end of the selection.
   * @param direction The direction in which the selection is performed.
   */
  setSelectionRange(start, end, direction) {
    this.getInputOrTextarea().setSelectionRange(start, end, direction);
  }
  /**
   * Shows the browser picker for an input element of type "date", "time", etc.
   *
   * For a full list of supported types, see:
   * https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/showPicker#browser_compatibility
   *
   * https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/showPicker
   */
  showPicker() {
    const input = this.getInput();
    if (!input) {
      return;
    }
    input.showPicker();
  }
  /**
   * Decrements the value of a numeric type text field by `step` or `n` `step`
   * number of times.
   *
   * https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/stepDown
   *
   * @param stepDecrement The number of steps to decrement, defaults to 1.
   */
  stepDown(stepDecrement) {
    const input = this.getInput();
    if (!input) {
      return;
    }
    input.stepDown(stepDecrement);
    this.value = input.value;
  }
  /**
   * Increments the value of a numeric type text field by `step` or `n` `step`
   * number of times.
   *
   * https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/stepUp
   *
   * @param stepIncrement The number of steps to increment, defaults to 1.
   */
  stepUp(stepIncrement) {
    const input = this.getInput();
    if (!input) {
      return;
    }
    input.stepUp(stepIncrement);
    this.value = input.value;
  }
  /**
   * Reset the text field to its default value.
   */
  reset() {
    this.dirty = false;
    this.value = this.getAttribute("value") ?? "";
    this.nativeError = false;
    this.nativeErrorText = "";
  }
  attributeChangedCallback(attribute, newValue, oldValue) {
    if (attribute === "value" && this.dirty) {
      return;
    }
    super.attributeChangedCallback(attribute, newValue, oldValue);
  }
  render() {
    const classes = {
      "disabled": this.disabled,
      "error": !this.disabled && this.hasError,
      "textarea": this.type === "textarea",
      "no-spinner": this.noSpinner
    };
    return html`
      <span class="text-field ${classMap(classes)}">
        ${this.renderField()}
      </span>
    `;
  }
  updated(changedProperties) {
    const value = this.getInputOrTextarea().value;
    if (this.value !== value) {
      this.value = value;
    }
  }
  renderField() {
    return html2`<${this.fieldTag}
      class="field"
      count=${this.value.length}
      ?disabled=${this.disabled}
      ?error=${this.hasError}
      error-text=${this.getErrorText()}
      ?focused=${this.focused}
      ?has-end=${this.hasTrailingIcon}
      ?has-start=${this.hasLeadingIcon}
      label=${this.label}
      ?no-asterisk=${this.noAsterisk}
      max=${this.maxLength}
      ?populated=${!!this.value}
      ?required=${this.required}
      ?resizable=${this.type === "textarea"}
      supporting-text=${this.supportingText}
    >
      ${this.renderLeadingIcon()}
      ${this.renderInputOrTextarea()}
      ${this.renderTrailingIcon()}
      <div id="description" slot="aria-describedby"></div>
      <slot name="container" slot="container"></slot>
    </${this.fieldTag}>`;
  }
  renderLeadingIcon() {
    return html`
      <span class="icon leading" slot="start">
        <slot name="leading-icon" @slotchange=${this.handleIconChange}></slot>
      </span>
    `;
  }
  renderTrailingIcon() {
    return html`
      <span class="icon trailing" slot="end">
        <slot name="trailing-icon" @slotchange=${this.handleIconChange}></slot>
      </span>
    `;
  }
  renderInputOrTextarea() {
    const style = { "direction": this.textDirection };
    const ariaLabel = this.ariaLabel || this.label || nothing;
    const autocomplete = this.autocomplete;
    const hasMaxLength = (this.maxLength ?? -1) > -1;
    const hasMinLength = (this.minLength ?? -1) > -1;
    if (this.type === "textarea") {
      return html`
        <textarea
          class="input"
          style=${styleMap(style)}
          aria-describedby="description"
          aria-invalid=${this.hasError}
          aria-label=${ariaLabel}
          autocomplete=${autocomplete || nothing}
          name=${this.name || nothing}
          ?disabled=${this.disabled}
          maxlength=${hasMaxLength ? this.maxLength : nothing}
          minlength=${hasMinLength ? this.minLength : nothing}
          placeholder=${this.placeholder || nothing}
          ?readonly=${this.readOnly}
          ?required=${this.required}
          rows=${this.rows}
          cols=${this.cols}
          .value=${live(this.value)}
          @change=${this.redispatchEvent}
          @focus=${this.handleFocusChange}
          @blur=${this.handleFocusChange}
          @input=${this.handleInput}
          @select=${this.redispatchEvent}></textarea>
      `;
    }
    const prefix = this.renderPrefix();
    const suffix = this.renderSuffix();
    const inputMode = this.inputMode;
    return html`
      <div class="input-wrapper">
        ${prefix}
        <input
          class="input"
          style=${styleMap(style)}
          aria-describedby="description"
          aria-invalid=${this.hasError}
          aria-label=${ariaLabel}
          autocomplete=${autocomplete || nothing}
          name=${this.name || nothing}
          ?disabled=${this.disabled}
          inputmode=${inputMode || nothing}
          max=${this.max || nothing}
          maxlength=${hasMaxLength ? this.maxLength : nothing}
          min=${this.min || nothing}
          minlength=${hasMinLength ? this.minLength : nothing}
          pattern=${this.pattern || nothing}
          placeholder=${this.placeholder || nothing}
          ?readonly=${this.readOnly}
          ?required=${this.required}
          ?multiple=${this.multiple}
          step=${this.step || nothing}
          type=${this.type}
          .value=${live(this.value)}
          @change=${this.redispatchEvent}
          @focus=${this.handleFocusChange}
          @blur=${this.handleFocusChange}
          @input=${this.handleInput}
          @select=${this.redispatchEvent} />
        ${suffix}
      </div>
    `;
  }
  renderPrefix() {
    return this.renderAffix(
      this.prefixText,
      /* isSuffix */
      false
    );
  }
  renderSuffix() {
    return this.renderAffix(
      this.suffixText,
      /* isSuffix */
      true
    );
  }
  renderAffix(text, isSuffix) {
    if (!text) {
      return nothing;
    }
    const classes = {
      "suffix": isSuffix,
      "prefix": !isSuffix
    };
    return html`<span class="${classMap(classes)}">${text}</span>`;
  }
  getErrorText() {
    return this.error ? this.errorText : this.nativeErrorText;
  }
  handleFocusChange() {
    this.focused = this.inputOrTextarea?.matches(":focus") ?? false;
  }
  handleInput(event) {
    this.dirty = true;
    this.value = event.target.value;
  }
  redispatchEvent(event) {
    redispatchEvent(this, event);
  }
  getInputOrTextarea() {
    if (!this.inputOrTextarea) {
      this.connectedCallback();
      this.scheduleUpdate();
    }
    if (this.isUpdatePending) {
      this.scheduleUpdate();
    }
    return this.inputOrTextarea;
  }
  getInput() {
    if (this.type === "textarea") {
      return null;
    }
    return this.getInputOrTextarea();
  }
  handleIconChange() {
    this.hasLeadingIcon = this.leadingIcons.length > 0;
    this.hasTrailingIcon = this.trailingIcons.length > 0;
  }
  [getFormValue]() {
    return this.value;
  }
  formResetCallback() {
    this.reset();
  }
  formStateRestoreCallback(state2) {
    this.value = state2;
  }
  focus() {
    this.getInputOrTextarea().focus();
  }
  [createValidator]() {
    return new TextFieldValidator(() => ({
      state: this,
      renderedControl: this.inputOrTextarea
    }));
  }
  [getValidityAnchor]() {
    return this.inputOrTextarea;
  }
  [onReportValidity](invalidEvent) {
    invalidEvent?.preventDefault();
    const prevMessage = this.getErrorText();
    this.nativeError = !!invalidEvent;
    this.nativeErrorText = this.validationMessage;
    if (prevMessage === this.getErrorText()) {
      this.field?.reannounceError();
    }
  }
};
TextField.shadowRootOptions = {
  ...LitElement.shadowRootOptions,
  delegatesFocus: true
};
__decorate([
  property({ type: Boolean, reflect: true })
], TextField.prototype, "error", void 0);
__decorate([
  property({ attribute: "error-text" })
], TextField.prototype, "errorText", void 0);
__decorate([
  property()
], TextField.prototype, "label", void 0);
__decorate([
  property({ type: Boolean, attribute: "no-asterisk" })
], TextField.prototype, "noAsterisk", void 0);
__decorate([
  property({ type: Boolean, reflect: true })
], TextField.prototype, "required", void 0);
__decorate([
  property()
], TextField.prototype, "value", void 0);
__decorate([
  property({ attribute: "prefix-text" })
], TextField.prototype, "prefixText", void 0);
__decorate([
  property({ attribute: "suffix-text" })
], TextField.prototype, "suffixText", void 0);
__decorate([
  property({ type: Boolean, attribute: "has-leading-icon" })
], TextField.prototype, "hasLeadingIcon", void 0);
__decorate([
  property({ type: Boolean, attribute: "has-trailing-icon" })
], TextField.prototype, "hasTrailingIcon", void 0);
__decorate([
  property({ attribute: "supporting-text" })
], TextField.prototype, "supportingText", void 0);
__decorate([
  property({ attribute: "text-direction" })
], TextField.prototype, "textDirection", void 0);
__decorate([
  property({ type: Number })
], TextField.prototype, "rows", void 0);
__decorate([
  property({ type: Number })
], TextField.prototype, "cols", void 0);
__decorate([
  property({ reflect: true })
], TextField.prototype, "inputMode", void 0);
__decorate([
  property()
], TextField.prototype, "max", void 0);
__decorate([
  property({ type: Number })
], TextField.prototype, "maxLength", void 0);
__decorate([
  property()
], TextField.prototype, "min", void 0);
__decorate([
  property({ type: Number })
], TextField.prototype, "minLength", void 0);
__decorate([
  property({ type: Boolean, attribute: "no-spinner" })
], TextField.prototype, "noSpinner", void 0);
__decorate([
  property()
], TextField.prototype, "pattern", void 0);
__decorate([
  property({ reflect: true, converter: stringConverter })
], TextField.prototype, "placeholder", void 0);
__decorate([
  property({ type: Boolean, reflect: true })
], TextField.prototype, "readOnly", void 0);
__decorate([
  property({ type: Boolean, reflect: true })
], TextField.prototype, "multiple", void 0);
__decorate([
  property()
], TextField.prototype, "step", void 0);
__decorate([
  property({ reflect: true })
], TextField.prototype, "type", void 0);
__decorate([
  property({ reflect: true })
], TextField.prototype, "autocomplete", void 0);
__decorate([
  state()
], TextField.prototype, "dirty", void 0);
__decorate([
  state()
], TextField.prototype, "focused", void 0);
__decorate([
  state()
], TextField.prototype, "nativeError", void 0);
__decorate([
  state()
], TextField.prototype, "nativeErrorText", void 0);
__decorate([
  query(".input")
], TextField.prototype, "inputOrTextarea", void 0);
__decorate([
  query(".field")
], TextField.prototype, "field", void 0);
__decorate([
  queryAssignedElements({ slot: "leading-icon" })
], TextField.prototype, "leadingIcons", void 0);
__decorate([
  queryAssignedElements({ slot: "trailing-icon" })
], TextField.prototype, "trailingIcons", void 0);

export {
  TextField,
  styles
};
/*! Bundled license information:

@material/web/textfield/internal/shared-styles.js:
  (**
   * @license
   * Copyright 2024 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   *)

lit-html/development/directive-helpers.js:
lit-html/development/directives/live.js:
  (**
   * @license
   * Copyright 2020 Google LLC
   * SPDX-License-Identifier: BSD-3-Clause
   *)

@material/web/internal/controller/string-converter.js:
  (**
   * @license
   * Copyright 2022 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   *)

@material/web/labs/behaviors/validators/text-field-validator.js:
  (**
   * @license
   * Copyright 2023 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   *)

@material/web/textfield/internal/text-field.js:
  (**
   * @license
   * Copyright 2021 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   *)
*/
//# sourceMappingURL=chunk-ASS3M6M6.js.map
