import {
  Card,
  styles
} from "./chunk-JAQOZDDJ.js";
import "./chunk-HODMDVFX.js";
import {
  customElement
} from "./chunk-T3WMJB5E.js";
import {
  css
} from "./chunk-4GZ3EDRH.js";
import {
  __decorate
} from "./chunk-HMZZ7KLC.js";
import "./chunk-G3PMV62Z.js";

// node_modules/@material/web/labs/card/internal/elevated-styles.js
var styles2 = css`:host{--_container-color: var(--md-elevated-card-container-color, var(--md-sys-color-surface-container-low, #f7f2fa));--_container-elevation: var(--md-elevated-card-container-elevation, 1);--_container-shadow-color: var(--md-elevated-card-container-shadow-color, var(--md-sys-color-shadow, #000));--_container-shape: var(--md-elevated-card-container-shape, var(--md-sys-shape-corner-medium, 12px))}
`;

// node_modules/@material/web/labs/card/elevated-card.js
var MdElevatedCard = class MdElevatedCard2 extends Card {
};
MdElevatedCard.styles = [styles, styles2];
MdElevatedCard = __decorate([
  customElement("md-elevated-card")
], MdElevatedCard);
export {
  MdElevatedCard
};
/*! Bundled license information:

@material/web/labs/card/internal/elevated-styles.js:
  (**
   * @license
   * Copyright 2024 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   *)

@material/web/labs/card/elevated-card.js:
  (**
   * @license
   * Copyright 2023 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   *)
*/
//# sourceMappingURL=@material_web_labs_card_elevated-card__js.js.map
