{"version": 3, "sources": ["../../@material/web/chips/internal/selectable-styles.ts", "../../@material/web/chips/internal/trailing-icon-styles.ts", "../../@material/web/chips/internal/multi-action-chip.ts", "../../@material/web/chips/internal/trailing-icons.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2024 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n// Generated stylesheet for ./chips/internal/selectable-styles.css.\nimport {css} from 'lit';\nexport const styles = css`.selected{--md-ripple-hover-color: var(--_selected-hover-state-layer-color);--md-ripple-hover-opacity: var(--_selected-hover-state-layer-opacity);--md-ripple-pressed-color: var(--_selected-pressed-state-layer-color);--md-ripple-pressed-opacity: var(--_selected-pressed-state-layer-opacity)}:where(.selected)::before{background:var(--_selected-container-color)}:where(.selected) .outline{border-width:var(--_selected-outline-width)}:where(.selected.disabled)::before{background:var(--_disabled-selected-container-color);opacity:var(--_disabled-selected-container-opacity)}:where(.selected) .label{color:var(--_selected-label-text-color)}:where(.selected:hover) .label{color:var(--_selected-hover-label-text-color)}:where(.selected:focus) .label{color:var(--_selected-focus-label-text-color)}:where(.selected:active) .label{color:var(--_selected-pressed-label-text-color)}:where(.selected) .leading.icon{color:var(--_selected-leading-icon-color)}:where(.selected:hover) .leading.icon{color:var(--_selected-hover-leading-icon-color)}:where(.selected:focus) .leading.icon{color:var(--_selected-focus-leading-icon-color)}:where(.selected:active) .leading.icon{color:var(--_selected-pressed-leading-icon-color)}@media(forced-colors: active){:where(.selected:not(.elevated))::before{border:1px solid CanvasText}:where(.selected) .outline{border-width:1px}}\n`;\n", "/**\n * @license\n * Copyright 2024 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n// Generated stylesheet for ./chips/internal/trailing-icon-styles.css.\nimport {css} from 'lit';\nexport const styles = css`.trailing.action{align-items:center;justify-content:center;padding-inline-start:var(--_icon-label-space);padding-inline-end:var(--_with-trailing-icon-trailing-space)}.trailing.action :is(md-ripple,md-focus-ring){border-radius:50%;height:calc(1.***********var(--_icon-size));width:calc(1.***********var(--_icon-size))}.trailing.action md-focus-ring{inset:unset}.has-trailing .primary.action{padding-inline-end:0}.trailing.icon{color:var(--_trailing-icon-color);height:var(--_icon-size);width:var(--_icon-size)}:where(:hover) .trailing.icon{color:var(--_hover-trailing-icon-color)}:where(:focus) .trailing.icon{color:var(--_focus-trailing-icon-color)}:where(:active) .trailing.icon{color:var(--_pressed-trailing-icon-color)}:where(.disabled) .trailing.icon{color:var(--_disabled-trailing-icon-color);opacity:var(--_disabled-trailing-icon-opacity)}:where(.selected) .trailing.icon{color:var(--_selected-trailing-icon-color)}:where(.selected:hover) .trailing.icon{color:var(--_selected-hover-trailing-icon-color)}:where(.selected:focus) .trailing.icon{color:var(--_selected-focus-trailing-icon-color)}:where(.selected:active) .trailing.icon{color:var(--_selected-pressed-trailing-icon-color)}@media(forced-colors: active){.trailing.icon{color:ButtonText}:where(.disabled) .trailing.icon{color:GrayText;opacity:1}}\n`;\n", "/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {html, isServer} from 'lit';\n\nimport {ARIAMixinStrict} from '../../internal/aria/aria.js';\n\nimport {Chip} from './chip.js';\n\nconst ARIA_LABEL_REMOVE = 'aria-label-remove';\n\n/**\n * A chip component with multiple actions.\n */\nexport abstract class MultiActionChip extends Chip {\n  get ariaLabelRemove(): string | null {\n    if (this.hasAttribute(ARIA_LABEL_REMOVE)) {\n      return this.getAttribute(ARIA_LABEL_REMOVE)!;\n    }\n\n    const {ariaLabel} = this as ARIAMixinStrict;\n\n    // TODO(b/350810013): remove `this.label` when label property is removed.\n    if (ariaLabel || this.label) {\n      return `Remove ${ariaLabel || this.label}`;\n    }\n\n    return null;\n  }\n\n  set ariaLabelRemove(ariaLabel: string | null) {\n    const prev = this.ariaLabelRemove;\n    if (ariaLabel === prev) {\n      return;\n    }\n\n    if (ariaLabel === null) {\n      this.removeAttribute(ARIA_LABEL_REMOVE);\n    } else {\n      this.setAttribute(ARIA_LABEL_REMOVE, ariaLabel);\n    }\n\n    this.requestUpdate();\n  }\n\n  protected abstract readonly primaryAction: HTMLElement | null;\n  protected abstract readonly trailingAction: HTMLElement | null;\n\n  constructor() {\n    super();\n    this.handleTrailingActionFocus = this.handleTrailingActionFocus.bind(this);\n    if (!isServer) {\n      this.addEventListener('keydown', this.handleKeyDown.bind(this));\n    }\n  }\n\n  override focus(options?: FocusOptions & {trailing?: boolean}) {\n    const isFocusable = this.alwaysFocusable || !this.disabled;\n    if (isFocusable && options?.trailing && this.trailingAction) {\n      this.trailingAction.focus(options);\n      return;\n    }\n\n    super.focus(options as FocusOptions);\n  }\n\n  protected override renderContainerContent() {\n    return html`\n      ${super.renderContainerContent()}\n      ${this.renderTrailingAction(this.handleTrailingActionFocus)}\n    `;\n  }\n\n  protected abstract renderTrailingAction(\n    focusListener: EventListener,\n  ): unknown;\n\n  private handleKeyDown(event: KeyboardEvent) {\n    const isLeft = event.key === 'ArrowLeft';\n    const isRight = event.key === 'ArrowRight';\n    // Ignore non-navigation keys.\n    if (!isLeft && !isRight) {\n      return;\n    }\n\n    if (!this.primaryAction || !this.trailingAction) {\n      // Does not have multiple actions.\n      return;\n    }\n\n    // Check if moving forwards or backwards\n    const isRtl = getComputedStyle(this).direction === 'rtl';\n    const forwards = isRtl ? isLeft : isRight;\n    const isPrimaryFocused = this.primaryAction?.matches(':focus-within');\n    const isTrailingFocused = this.trailingAction?.matches(':focus-within');\n\n    if ((forwards && isTrailingFocused) || (!forwards && isPrimaryFocused)) {\n      // Moving outside of the chip, it will be handled by the chip set.\n      return;\n    }\n\n    // Prevent default interactions, such as scrolling.\n    event.preventDefault();\n    // Don't let the chip set handle this navigation event.\n    event.stopPropagation();\n    const actionToFocus = forwards ? this.trailingAction : this.primaryAction;\n    actionToFocus.focus();\n  }\n\n  private handleTrailingActionFocus() {\n    const {primaryAction, trailingAction} = this;\n    if (!primaryAction || !trailingAction) {\n      return;\n    }\n\n    // Temporarily turn off the primary action's focusability. This allows\n    // shift+tab from the trailing action to move to the previous chip rather\n    // than the primary action in the same chip.\n    primaryAction.tabIndex = -1;\n    trailingAction.addEventListener(\n      'focusout',\n      () => {\n        primaryAction.tabIndex = 0;\n      },\n      {once: true},\n    );\n  }\n}\n", "/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport '../../focus/md-focus-ring.js';\nimport '../../ripple/ripple.js';\n\nimport {html, nothing} from 'lit';\n\nimport {Chip} from './chip.js';\n\ninterface RemoveButtonProperties {\n  ariaLabel: string | null;\n  disabled: boolean;\n  focusListener: EventListener;\n  tabbable?: boolean;\n}\n\n/** @protected */\nexport function renderRemoveButton({\n  ariaLabel,\n  disabled,\n  focusListener,\n  tabbable = false,\n}: RemoveButtonProperties) {\n  // When an aria-label is not provided, we use two spans with aria-labelledby\n  // to create the \"Remove <textContent>\" label for the remove button. The first\n  // is this #remove-label span, the second is the chip's #label slot span.\n  return html`\n    <span id=\"remove-label\" hidden aria-hidden=\"true\">Remove</span>\n    <button\n      class=\"trailing action\"\n      aria-label=${ariaLabel || nothing}\n      aria-labelledby=${!ariaLabel ? 'remove-label label' : nothing}\n      tabindex=${!tabbable ? -1 : nothing}\n      @click=${handleRemoveClick}\n      @focus=${focusListener}>\n      <md-focus-ring part=\"trailing-focus-ring\"></md-focus-ring>\n      <md-ripple ?disabled=${disabled}></md-ripple>\n      <span class=\"trailing icon\" aria-hidden=\"true\">\n        <slot name=\"remove-trailing-icon\">\n          <svg viewBox=\"0 96 960 960\">\n            <path\n              d=\"m249 849-42-42 231-231-231-231 42-42 231 231 231-231 42 42-231 231 231 231-42 42-231-231-231 231Z\" />\n          </svg>\n        </slot>\n      </span>\n      <span class=\"touch\"></span>\n    </button>\n  `;\n}\n\nfunction handleRemoveClick(this: Chip, event: Event) {\n  if (this.disabled || this.softDisabled) {\n    return;\n  }\n\n  event.stopPropagation();\n  const preventDefault = !this.dispatchEvent(\n    new Event('remove', {cancelable: true}),\n  );\n  if (preventDefault) {\n    return;\n  }\n\n  this.remove();\n}\n"], "mappings": ";;;;;;;;;;;AAOO,IAAM,SAAS;;;;ACAf,IAAMA,UAAS;;;;ACKtB,IAAM,oBAAoB;AAKpB,IAAgB,kBAAhB,cAAwC,KAAI;EAChD,IAAI,kBAAe;AACjB,QAAI,KAAK,aAAa,iBAAiB,GAAG;AACxC,aAAO,KAAK,aAAa,iBAAiB;IAC5C;AAEA,UAAM,EAAC,UAAS,IAAI;AAGpB,QAAI,aAAa,KAAK,OAAO;AAC3B,aAAO,UAAU,aAAa,KAAK,KAAK;IAC1C;AAEA,WAAO;EACT;EAEA,IAAI,gBAAgB,WAAwB;AAC1C,UAAM,OAAO,KAAK;AAClB,QAAI,cAAc,MAAM;AACtB;IACF;AAEA,QAAI,cAAc,MAAM;AACtB,WAAK,gBAAgB,iBAAiB;IACxC,OAAO;AACL,WAAK,aAAa,mBAAmB,SAAS;IAChD;AAEA,SAAK,cAAa;EACpB;EAKA,cAAA;AACE,UAAK;AACL,SAAK,4BAA4B,KAAK,0BAA0B,KAAK,IAAI;AACzE,QAAI,CAAC,UAAU;AACb,WAAK,iBAAiB,WAAW,KAAK,cAAc,KAAK,IAAI,CAAC;IAChE;EACF;EAES,MAAM,SAA6C;AAC1D,UAAM,cAAc,KAAK,mBAAmB,CAAC,KAAK;AAClD,QAAI,eAAe,SAAS,YAAY,KAAK,gBAAgB;AAC3D,WAAK,eAAe,MAAM,OAAO;AACjC;IACF;AAEA,UAAM,MAAM,OAAuB;EACrC;EAEmB,yBAAsB;AACvC,WAAO;QACH,MAAM,uBAAsB,CAAE;QAC9B,KAAK,qBAAqB,KAAK,yBAAyB,CAAC;;EAE/D;EAMQ,cAAc,OAAoB;AACxC,UAAM,SAAS,MAAM,QAAQ;AAC7B,UAAM,UAAU,MAAM,QAAQ;AAE9B,QAAI,CAAC,UAAU,CAAC,SAAS;AACvB;IACF;AAEA,QAAI,CAAC,KAAK,iBAAiB,CAAC,KAAK,gBAAgB;AAE/C;IACF;AAGA,UAAM,QAAQ,iBAAiB,IAAI,EAAE,cAAc;AACnD,UAAM,WAAW,QAAQ,SAAS;AAClC,UAAM,mBAAmB,KAAK,eAAe,QAAQ,eAAe;AACpE,UAAM,oBAAoB,KAAK,gBAAgB,QAAQ,eAAe;AAEtE,QAAK,YAAY,qBAAuB,CAAC,YAAY,kBAAmB;AAEtE;IACF;AAGA,UAAM,eAAc;AAEpB,UAAM,gBAAe;AACrB,UAAM,gBAAgB,WAAW,KAAK,iBAAiB,KAAK;AAC5D,kBAAc,MAAK;EACrB;EAEQ,4BAAyB;AAC/B,UAAM,EAAC,eAAe,eAAc,IAAI;AACxC,QAAI,CAAC,iBAAiB,CAAC,gBAAgB;AACrC;IACF;AAKA,kBAAc,WAAW;AACzB,mBAAe,iBACb,YACA,MAAK;AACH,oBAAc,WAAW;IAC3B,GACA,EAAC,MAAM,KAAI,CAAC;EAEhB;;;;AC5GI,SAAU,mBAAmB,EACjC,WACA,UACA,eACA,WAAW,MAAK,GACO;AAIvB,SAAO;;;;mBAIU,aAAa,OAAO;wBACf,CAAC,YAAY,uBAAuB,OAAO;iBAClD,CAAC,WAAW,KAAK,OAAO;eAC1B,iBAAiB;eACjB,aAAa;;6BAEC,QAAQ;;;;;;;;;;;;AAYrC;AAEA,SAAS,kBAA8B,OAAY;AACjD,MAAI,KAAK,YAAY,KAAK,cAAc;AACtC;EACF;AAEA,QAAM,gBAAe;AACrB,QAAM,iBAAiB,CAAC,KAAK,cAC3B,IAAI,MAAM,UAAU,EAAC,YAAY,KAAI,CAAC,CAAC;AAEzC,MAAI,gBAAgB;AAClB;EACF;AAEA,OAAK,OAAM;AACb;", "names": ["styles"]}