{"version": 3, "sources": ["../../@material/web/list/internal/list.ts", "../../@material/web/list/internal/list-styles.ts", "../../@material/web/list/list.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {html, isServer, LitElement} from 'lit';\nimport {queryAssignedElements} from 'lit/decorators.js';\n\nimport {ListController, NavigableKeys} from './list-controller.js';\nimport {ListItem as SharedListItem} from './list-navigation-helpers.js';\n\nconst NAVIGABLE_KEY_SET = new Set<string>(Object.values(NavigableKeys));\n\ninterface ListItem extends SharedListItem {\n  type: 'text' | 'button' | 'link';\n}\n\n// tslint:disable-next-line:enforce-comments-on-exported-symbols\nexport class List extends LitElement {\n  /**\n   * An array of activatable and disableable list items. Queries every assigned\n   * element that has the `md-list-item` attribute.\n   *\n   * _NOTE:_ This is a shallow, flattened query via\n   * `HTMLSlotElement.queryAssignedElements` and thus will _only_ include direct\n   * children / directly slotted elements.\n   */\n  @queryAssignedElements({flatten: true})\n  protected slotItems!: Array<ListItem | (HTMLElement & {item?: ListItem})>;\n\n  /** @export */\n  get items() {\n    return this.listController.items;\n  }\n\n  private readonly listController = new ListController<ListItem>({\n    isItem: (item: HTMLElement): item is ListItem =>\n      item.hasAttribute('md-list-item'),\n    getPossibleItems: () => this.slotItems,\n    isRtl: () => getComputedStyle(this).direction === 'rtl',\n    deactivateItem: (item) => {\n      item.tabIndex = -1;\n    },\n    activateItem: (item) => {\n      item.tabIndex = 0;\n    },\n    isNavigableKey: (key) => NAVIGABLE_KEY_SET.has(key),\n    isActivatable: (item) => !item.disabled && item.type !== 'text',\n  });\n\n  private readonly internals =\n    // Cast needed for closure\n    (this as HTMLElement).attachInternals();\n\n  constructor() {\n    super();\n    if (!isServer) {\n      this.internals.role = 'list';\n      this.addEventListener('keydown', this.listController.handleKeydown);\n    }\n  }\n\n  protected override render() {\n    return html`\n      <slot\n        @deactivate-items=${this.listController.onDeactivateItems}\n        @request-activation=${this.listController.onRequestActivation}\n        @slotchange=${this.listController.onSlotchange}>\n      </slot>\n    `;\n  }\n\n  /**\n   * Activates the next item in the list. If at the end of the list, the first\n   * item will be activated.\n   *\n   * @return The activated list item or `null` if there are no items.\n   */\n  activateNextItem(): ListItem | null {\n    return this.listController.activateNextItem();\n  }\n\n  /**\n   * Activates the previous item in the list. If at the start of the list, the\n   * last item will be activated.\n   *\n   * @return The activated list item or `null` if there are no items.\n   */\n  activatePreviousItem(): ListItem | null {\n    return this.listController.activatePreviousItem();\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n// Generated stylesheet for ./list/internal/list-styles.css.\nimport {css} from 'lit';\nexport const styles = css`:host{background:var(--md-list-container-color, var(--md-sys-color-surface, #fef7ff));color:unset;display:flex;flex-direction:column;outline:none;padding:8px 0;position:relative}\n`;\n", "/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {List} from './internal/list.js';\nimport {styles} from './internal/list-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-list': MdList;\n  }\n}\n\n/**\n * @summary Lists are continuous, vertical indexes of text or images.\n *\n * @description\n * Lists consist of one or more list items, and can contain actions represented\n * by icons and text. List items come in three sizes: one-line, two-line, and\n * three-line.\n *\n * __Takeaways:__\n *\n * - Lists should be sorted in logical ways that make content easy to scan, such\n *   as alphabetical, numerical, chronological, or by user preference.\n * - Lists present content in a way that makes it easy to identify a specific\n *   item in a collection and act on it.\n * - Lists should present icons, text, and actions in a consistent format.\n *\n * @final\n * @suppress {visibility}\n */\n@customElement('md-list')\nexport class MdList extends List {\n  static override styles: CSSResultOrNative[] = [styles];\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAYA,IAAM,oBAAoB,IAAI,IAAY,OAAO,OAAO,aAAa,CAAC;AAOhE,IAAO,OAAP,cAAoB,WAAU;;EAalC,IAAI,QAAK;AACP,WAAO,KAAK,eAAe;EAC7B;EAqBA,cAAA;AACE,UAAK;AApBU,SAAA,iBAAiB,IAAI,eAAyB;MAC7D,QAAQ,CAAC,SACP,KAAK,aAAa,cAAc;MAClC,kBAAkB,MAAM,KAAK;MAC7B,OAAO,MAAM,iBAAiB,IAAI,EAAE,cAAc;MAClD,gBAAgB,CAAC,SAAQ;AACvB,aAAK,WAAW;MAClB;MACA,cAAc,CAAC,SAAQ;AACrB,aAAK,WAAW;MAClB;MACA,gBAAgB,CAAC,QAAQ,kBAAkB,IAAI,GAAG;MAClD,eAAe,CAAC,SAAS,CAAC,KAAK,YAAY,KAAK,SAAS;KAC1D;AAEgB,SAAA;IAEd,KAAqB,gBAAe;AAIrC,QAAI,CAAC,UAAU;AACb,WAAK,UAAU,OAAO;AACtB,WAAK,iBAAiB,WAAW,KAAK,eAAe,aAAa;IACpE;EACF;EAEmB,SAAM;AACvB,WAAO;;4BAEiB,KAAK,eAAe,iBAAiB;8BACnC,KAAK,eAAe,mBAAmB;sBAC/C,KAAK,eAAe,YAAY;;;EAGpD;;;;;;;EAQA,mBAAgB;AACd,WAAO,KAAK,eAAe,iBAAgB;EAC7C;;;;;;;EAQA,uBAAoB;AAClB,WAAO,KAAK,eAAe,qBAAoB;EACjD;;AA9DU,WAAA;EADT,sBAAsB,EAAC,SAAS,KAAI,CAAC;;;;ACrBjC,IAAM,SAAS;;;;AC+Bf,IAAM,SAAN,MAAMA,gBAAe,KAAI;;AACd,OAAA,SAA8B,CAAC,MAAM;AAD1C,SAAM,WAAA;EADlB,cAAc,SAAS;GACX,MAAM;", "names": ["MdList"]}