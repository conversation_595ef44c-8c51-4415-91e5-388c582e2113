{"version": 3, "sources": ["../../lit-html/src/directive.ts", "../../lit-html/src/directives/class-map.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {Disconnectable, Part} from './lit-html.js';\n\nexport {\n  AttributePart,\n  BooleanAttributePart,\n  ChildPart,\n  ElementPart,\n  EventPart,\n  Part,\n  PropertyPart,\n} from './lit-html.js';\n\nexport interface DirectiveClass {\n  new (part: PartInfo): Directive;\n}\n\n/**\n * This utility type extracts the signature of a directive class's render()\n * method so we can use it for the type of the generated directive function.\n */\nexport type DirectiveParameters<C extends Directive> = Parameters<C['render']>;\n\n/**\n * A generated directive function doesn't evaluate the directive, but just\n * returns a DirectiveResult object that captures the arguments.\n */\nexport interface DirectiveResult<C extends DirectiveClass = DirectiveClass> {\n  /**\n   * This property needs to remain unminified.\n   * @internal\n   */\n  ['_$litDirective$']: C;\n  /** @internal */\n  values: DirectiveParameters<InstanceType<C>>;\n}\n\nexport const PartType = {\n  ATTRIBUTE: 1,\n  CHILD: 2,\n  PROPERTY: 3,\n  BOOLEAN_ATTRIBUTE: 4,\n  EVENT: 5,\n  ELEMENT: 6,\n} as const;\n\nexport type PartType = (typeof PartType)[keyof typeof PartType];\n\nexport interface ChildPartInfo {\n  readonly type: typeof PartType.CHILD;\n}\n\nexport interface AttributePartInfo {\n  readonly type:\n    | typeof PartType.ATTRIBUTE\n    | typeof PartType.PROPERTY\n    | typeof PartType.BOOLEAN_ATTRIBUTE\n    | typeof PartType.EVENT;\n  readonly strings?: ReadonlyArray<string>;\n  readonly name: string;\n  readonly tagName: string;\n}\n\nexport interface ElementPartInfo {\n  readonly type: typeof PartType.ELEMENT;\n}\n\n/**\n * Information about the part a directive is bound to.\n *\n * This is useful for checking that a directive is attached to a valid part,\n * such as with directive that can only be used on attribute bindings.\n */\nexport type PartInfo = ChildPartInfo | AttributePartInfo | ElementPartInfo;\n\n/**\n * Creates a user-facing directive function from a Directive class. This\n * function has the same parameters as the directive's render() method.\n */\nexport const directive =\n  <C extends DirectiveClass>(c: C) =>\n  (...values: DirectiveParameters<InstanceType<C>>): DirectiveResult<C> => ({\n    // This property needs to remain unminified.\n    ['_$litDirective$']: c,\n    values,\n  });\n\n/**\n * Base class for creating custom directives. Users should extend this class,\n * implement `render` and/or `update`, and then pass their subclass to\n * `directive`.\n */\nexport abstract class Directive implements Disconnectable {\n  //@internal\n  __part!: Part;\n  //@internal\n  __attributeIndex: number | undefined;\n  //@internal\n  __directive?: Directive;\n\n  //@internal\n  _$parent!: Disconnectable;\n\n  // These will only exist on the AsyncDirective subclass\n  //@internal\n  _$disconnectableChildren?: Set<Disconnectable>;\n  // This property needs to remain unminified.\n  //@internal\n  ['_$notifyDirectiveConnectionChanged']?(isConnected: boolean): void;\n\n  constructor(_partInfo: PartInfo) {}\n\n  // See comment in Disconnectable interface for why this is a getter\n  get _$isConnected() {\n    return this._$parent._$isConnected;\n  }\n\n  /** @internal */\n  _$initialize(\n    part: Part,\n    parent: Disconnectable,\n    attributeIndex: number | undefined\n  ) {\n    this.__part = part;\n    this._$parent = parent;\n    this.__attributeIndex = attributeIndex;\n  }\n  /** @internal */\n  _$resolve(part: Part, props: Array<unknown>): unknown {\n    return this.update(part, props);\n  }\n\n  abstract render(...props: Array<unknown>): unknown;\n\n  update(_part: Part, props: Array<unknown>): unknown {\n    return this.render(...props);\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {AttributePart, noChange} from '../lit-html.js';\nimport {\n  directive,\n  Directive,\n  DirectiveParameters,\n  PartInfo,\n  PartType,\n} from '../directive.js';\n\n/**\n * A key-value set of class names to truthy values.\n */\nexport interface ClassInfo {\n  readonly [name: string]: string | boolean | number;\n}\n\nclass ClassMapDirective extends Directive {\n  /**\n   * Stores the ClassInfo object applied to a given AttributePart.\n   * Used to unset existing values when a new ClassInfo object is applied.\n   */\n  private _previousClasses?: Set<string>;\n  private _staticClasses?: Set<string>;\n\n  constructor(partInfo: PartInfo) {\n    super(partInfo);\n    if (\n      partInfo.type !== PartType.ATTRIBUTE ||\n      partInfo.name !== 'class' ||\n      (partInfo.strings?.length as number) > 2\n    ) {\n      throw new Error(\n        '`classMap()` can only be used in the `class` attribute ' +\n          'and must be the only part in the attribute.'\n      );\n    }\n  }\n\n  render(classInfo: ClassInfo) {\n    // Add spaces to ensure separation from static classes\n    return (\n      ' ' +\n      Object.keys(classInfo)\n        .filter((key) => classInfo[key])\n        .join(' ') +\n      ' '\n    );\n  }\n\n  override update(part: AttributePart, [classInfo]: DirectiveParameters<this>) {\n    // Remember dynamic classes on the first render\n    if (this._previousClasses === undefined) {\n      this._previousClasses = new Set();\n      if (part.strings !== undefined) {\n        this._staticClasses = new Set(\n          part.strings\n            .join(' ')\n            .split(/\\s/)\n            .filter((s) => s !== '')\n        );\n      }\n      for (const name in classInfo) {\n        if (classInfo[name] && !this._staticClasses?.has(name)) {\n          this._previousClasses.add(name);\n        }\n      }\n      return this.render(classInfo);\n    }\n\n    const classList = part.element.classList;\n\n    // Remove old classes that no longer apply\n    for (const name of this._previousClasses) {\n      if (!(name in classInfo)) {\n        classList.remove(name);\n        this._previousClasses!.delete(name);\n      }\n    }\n\n    // Add or remove classes based on their classMap value\n    for (const name in classInfo) {\n      // We explicitly want a loose truthy check of `value` because it seems\n      // more convenient that '' and 0 are skipped.\n      const value = !!classInfo[name];\n      if (\n        value !== this._previousClasses.has(name) &&\n        !this._staticClasses?.has(name)\n      ) {\n        if (value) {\n          classList.add(name);\n          this._previousClasses.add(name);\n        } else {\n          classList.remove(name);\n          this._previousClasses.delete(name);\n        }\n      }\n    }\n    return noChange;\n  }\n}\n\n/**\n * A directive that applies dynamic CSS classes.\n *\n * This must be used in the `class` attribute and must be the only part used in\n * the attribute. It takes each property in the `classInfo` argument and adds\n * the property name to the element's `classList` if the property value is\n * truthy; if the property value is falsy, the property name is removed from\n * the element's `class`.\n *\n * For example `{foo: bar}` applies the class `foo` if the value of `bar` is\n * truthy.\n *\n * @param classInfo\n */\nexport const classMap = directive(ClassMapDirective);\n\n/**\n * The type of the class that powers this directive. Necessary for naming the\n * directive's return type.\n */\nexport type {ClassMapDirective};\n"], "mappings": ";;;;;AA0CO,IAAM,WAAW;EACtB,WAAW;EACX,OAAO;EACP,UAAU;EACV,mBAAmB;EACnB,OAAO;EACP,SAAS;;AAoCJ,IAAM,YACX,CAA2B,MAC3B,IAAI,YAAsE;;EAExE,CAAC,iBAAiB,GAAG;EACrB;;AAQE,IAAgB,YAAhB,MAAyB;EAkB7B,YAAY,WAAmB;EAAG;;EAGlC,IAAI,gBAAa;AACf,WAAO,KAAK,SAAS;EACvB;;EAGA,aACE,MACA,QACA,gBAAkC;AAElC,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,SAAK,mBAAmB;EAC1B;;EAEA,UAAU,MAAY,OAAqB;AACzC,WAAO,KAAK,OAAO,MAAM,KAAK;EAChC;EAIA,OAAO,OAAa,OAAqB;AACvC,WAAO,KAAK,OAAO,GAAG,KAAK;EAC7B;;;;ACvHF,IAAM,oBAAN,cAAgC,UAAS;EAQvC,YAAY,UAAkB;AAC5B,UAAM,QAAQ;AACd,QACE,SAAS,SAAS,SAAS,aAC3B,SAAS,SAAS,WACjB,SAAS,SAAS,SAAoB,GACvC;AACA,YAAM,IAAI,MACR,oGAC+C;IAEnD;EACF;EAEA,OAAO,WAAoB;AAEzB,WACE,MACA,OAAO,KAAK,SAAS,EAClB,OAAO,CAAC,QAAQ,UAAU,GAAG,CAAC,EAC9B,KAAK,GAAG,IACX;EAEJ;EAES,OAAO,MAAqB,CAAC,SAAS,GAA4B;AAEzE,QAAI,KAAK,qBAAqB,QAAW;AACvC,WAAK,mBAAmB,oBAAI,IAAG;AAC/B,UAAI,KAAK,YAAY,QAAW;AAC9B,aAAK,iBAAiB,IAAI,IACxB,KAAK,QACF,KAAK,GAAG,EACR,MAAM,IAAI,EACV,OAAO,CAAC,MAAM,MAAM,EAAE,CAAC;MAE9B;AACA,iBAAW,QAAQ,WAAW;AAC5B,YAAI,UAAU,IAAI,KAAK,CAAC,KAAK,gBAAgB,IAAI,IAAI,GAAG;AACtD,eAAK,iBAAiB,IAAI,IAAI;QAChC;MACF;AACA,aAAO,KAAK,OAAO,SAAS;IAC9B;AAEA,UAAM,YAAY,KAAK,QAAQ;AAG/B,eAAW,QAAQ,KAAK,kBAAkB;AACxC,UAAI,EAAE,QAAQ,YAAY;AACxB,kBAAU,OAAO,IAAI;AACrB,aAAK,iBAAkB,OAAO,IAAI;MACpC;IACF;AAGA,eAAW,QAAQ,WAAW;AAG5B,YAAM,QAAQ,CAAC,CAAC,UAAU,IAAI;AAC9B,UACE,UAAU,KAAK,iBAAiB,IAAI,IAAI,KACxC,CAAC,KAAK,gBAAgB,IAAI,IAAI,GAC9B;AACA,YAAI,OAAO;AACT,oBAAU,IAAI,IAAI;AAClB,eAAK,iBAAiB,IAAI,IAAI;QAChC,OAAO;AACL,oBAAU,OAAO,IAAI;AACrB,eAAK,iBAAiB,OAAO,IAAI;QACnC;MACF;IACF;AACA,WAAO;EACT;;AAiBK,IAAM,WAAW,UAAU,iBAAiB;", "names": []}