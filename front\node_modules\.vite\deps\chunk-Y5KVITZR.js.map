{"version": 3, "sources": ["../../@material/web/menu/internal/menu-styles.ts", "../../@material/web/menu/menu.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2024 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n// Generated stylesheet for ./menu/internal/menu-styles.css.\nimport {css} from 'lit';\nexport const styles = css`:host{--md-elevation-level: var(--md-menu-container-elevation, 2);--md-elevation-shadow-color: var(--md-menu-container-shadow-color, var(--md-sys-color-shadow, #000));min-width:112px;color:unset;display:contents}md-focus-ring{--md-focus-ring-shape: var(--md-menu-container-shape, var(--md-sys-shape-corner-extra-small, 4px))}.menu{border-radius:var(--md-menu-container-shape, var(--md-sys-shape-corner-extra-small, 4px));display:none;inset:auto;border:none;padding:0px;overflow:visible;background-color:rgba(0,0,0,0);color:inherit;opacity:0;z-index:20;position:absolute;user-select:none;max-height:inherit;height:inherit;min-width:inherit;max-width:inherit;scrollbar-width:inherit}.menu::backdrop{display:none}.fixed{position:fixed}.items{display:block;list-style-type:none;margin:0;outline:none;box-sizing:border-box;background-color:var(--md-menu-container-color, var(--md-sys-color-surface-container, #f3edf7));height:inherit;max-height:inherit;overflow:auto;min-width:inherit;max-width:inherit;border-radius:inherit;scrollbar-width:inherit}.item-padding{padding-block:var(--md-menu-top-space, 8px) var(--md-menu-bottom-space, 8px)}.has-overflow:not([popover]) .items{overflow:visible}.has-overflow.animating .items,.animating .items{overflow:hidden}.has-overflow.animating .items{pointer-events:none}.animating ::slotted(.md-menu-hidden){opacity:0}slot{display:block;height:inherit;max-height:inherit}::slotted(:is(md-divider,[role=separator])){margin:8px 0}@media(forced-colors: active){.menu{border-style:solid;border-color:CanvasText;border-width:1px}}\n`;\n", "/**\n * @license\n * Copyright 2022 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {Menu} from './internal/menu.js';\nimport {styles} from './internal/menu-styles.js';\n\nexport {type ListItem} from '../list/internal/list-navigation-helpers.js';\nexport {type MenuItem} from './internal/controllers/menuItemController.js';\nexport {\n  CloseReason,\n  FocusState,\n  type CloseMenuEvent,\n  type Menu,\n} from './internal/controllers/shared.js';\nexport {Corner} from './internal/menu.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-menu': MdMenu;\n  }\n}\n\n/**\n * @summary Menus display a list of choices on a temporary surface.\n *\n * @description\n * Menus appear when users interact with a button, action, or other control.\n *\n * They can be opened from a variety of elements, most commonly icon buttons,\n * buttons, and text fields.\n *\n * md-menu listens for the `close-menu` and `deselect-items` events.\n *\n * - `close-menu` closes the menu when dispatched from a child element.\n * - `deselect-items` deselects all of its immediate menu-item children.\n *\n * @example\n * ```html\n * <div style=\"position:relative;\">\n *   <button\n *       id=\"anchor\"\n *       @click=${() => this.menuRef.value.show()}>\n *     Click to open menu\n *   </button>\n *   <!--\n *     `has-overflow` is required when using a submenu which overflows the\n *     menu's contents.\n *\n *     Additionally, `anchor` ingests an idref which do not pass through shadow\n *     roots. You can also set `.anchorElement` to an element reference if\n *     necessary.\n *   -->\n *   <md-menu anchor=\"anchor\" has-overflow ${ref(menuRef)}>\n *     <md-menu-item headline=\"This is a headline\"></md-menu-item>\n *     <md-sub-menu>\n *       <md-menu-item\n *           slot=\"item\"\n *           headline=\"this is a submenu item\">\n *       </md-menu-item>\n *       <md-menu slot=\"menu\">\n *         <md-menu-item headline=\"This is an item inside a submenu\">\n *         </md-menu-item>\n *       </md-menu>\n *     </md-sub-menu>\n *   </md-menu>\n * </div>\n * ```\n *\n * @final\n * @suppress {visibility}\n */\n@customElement('md-menu')\nexport class MdMenu extends Menu {\n  static override styles: CSSResultOrNative[] = [styles];\n}\n"], "mappings": ";;;;;;;;;;;;AAOO,IAAM,SAAS;;;;ACuEf,IAAM,SAAN,MAAMA,gBAAe,KAAI;;AACd,OAAA,SAA8B,CAAC,MAAM;AAD1C,SAAM,WAAA;EADlB,cAAc,SAAS;GACX,MAAM;", "names": ["MdMenu"]}