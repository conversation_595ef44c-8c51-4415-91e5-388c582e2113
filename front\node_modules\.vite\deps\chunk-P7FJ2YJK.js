import {
  mixinOnReportValidity,
  onReportValidity
} from "./chunk-5XCMY3ZR.js";
import {
  DEFAULT_TYPEAHEAD_BUFFER_TIME,
  TYPEAHEAD_RECORD
} from "./chunk-42Q7IDHC.js";
import {
  FocusState,
  isElementInSubtree,
  isSelectableKey
} from "./chunk-DMRCI6IZ.js";
import {
  styleMap
} from "./chunk-DAKWLTHT.js";
import {
  getActiveItem
} from "./chunk-SHO7BEJJ.js";
import {
  html as html2
} from "./chunk-FNIFQ77A.js";
import {
  Validator,
  createValidator,
  getValidityAnchor,
  mixinConstraintValidation
} from "./chunk-QLIBTWMU.js";
import {
  getFormValue,
  mixinFormAssociated
} from "./chunk-V3YHLUDS.js";
import {
  mixinElementInternals
} from "./chunk-N2ETY4JQ.js";
import {
  redispatchEvent
} from "./chunk-4X3LRXT2.js";
import {
  mixinDelegatesAria
} from "./chunk-GXE4MBY5.js";
import {
  classMap
} from "./chunk-SZQCPKZF.js";
import {
  property,
  query,
  queryAssignedElements,
  state
} from "./chunk-T3WMJB5E.js";
import {
  LitElement,
  css,
  html,
  isServer,
  nothing,
  render
} from "./chunk-4GZ3EDRH.js";
import {
  __decorate
} from "./chunk-HMZZ7KLC.js";

// node_modules/@material/web/select/internal/shared-styles.js
var styles = css`:host{color:unset;min-width:210px;display:flex}.field{cursor:default;outline:none}.select{position:relative;flex-direction:column}.icon.trailing svg,.icon ::slotted(*){fill:currentColor}.icon ::slotted(*){width:inherit;height:inherit;font-size:inherit}.icon slot{display:flex;height:100%;width:100%;align-items:center;justify-content:center}.icon.trailing :is(.up,.down){opacity:0;transition:opacity 75ms linear 75ms}.select:not(.open) .down,.select.open .up{opacity:1}.field,.select,md-menu{min-width:inherit;width:inherit;max-width:inherit;display:flex}md-menu{min-width:var(--__menu-min-width);max-width:var(--__menu-max-width, inherit)}.menu-wrapper{width:0px;height:0px;max-width:inherit}md-menu ::slotted(:not[disabled]){cursor:pointer}.field,.select{width:100%}:host{display:inline-flex}:host([disabled]){pointer-events:none}
`;

// node_modules/@material/web/labs/behaviors/validators/select-validator.js
var SelectValidator = class extends Validator {
  computeValidity(state2) {
    if (!this.selectControl) {
      this.selectControl = document.createElement("select");
    }
    render(html`<option value=${state2.value}></option>`, this.selectControl);
    this.selectControl.value = state2.value;
    this.selectControl.required = state2.required;
    return {
      validity: this.selectControl.validity,
      validationMessage: this.selectControl.validationMessage
    };
  }
  equals(prev, next) {
    return prev.value === next.value && prev.required === next.required;
  }
  copy({ value, required }) {
    return { value, required };
  }
};

// node_modules/@material/web/select/internal/shared.js
function getSelectedItems(items) {
  const selectedItemRecords = [];
  for (let i = 0; i < items.length; i++) {
    const item = items[i];
    if (item.selected) {
      selectedItemRecords.push([item, i]);
    }
  }
  return selectedItemRecords;
}

// node_modules/@material/web/select/internal/select.js
var _a;
var VALUE = Symbol("value");
var selectBaseClass = mixinDelegatesAria(mixinOnReportValidity(mixinConstraintValidation(mixinFormAssociated(mixinElementInternals(LitElement)))));
var Select = class extends selectBaseClass {
  /**
   * The value of the currently selected option.
   *
   * Note: For SSR, set `[selected]` on the requested option and `displayText`
   * rather than setting `value` setting `value` will incur a DOM query.
   */
  get value() {
    return this[VALUE];
  }
  set value(value) {
    if (isServer)
      return;
    this.lastUserSetValue = value;
    this.select(value);
  }
  get options() {
    return this.menu?.items ?? [];
  }
  /**
   * The index of the currently selected option.
   *
   * Note: For SSR, set `[selected]` on the requested option and `displayText`
   * rather than setting `selectedIndex` setting `selectedIndex` will incur a
   * DOM query.
   */
  get selectedIndex() {
    const [_option, index] = (this.getSelectedOptions() ?? [])[0] ?? [];
    return index ?? -1;
  }
  set selectedIndex(index) {
    this.lastUserSetSelectedIndex = index;
    this.selectIndex(index);
  }
  /**
   * Returns an array of selected options.
   *
   * NOTE: md-select only supports single selection.
   */
  get selectedOptions() {
    return (this.getSelectedOptions() ?? []).map(([option]) => option);
  }
  get hasError() {
    return this.error || this.nativeError;
  }
  constructor() {
    super();
    this.quick = false;
    this.required = false;
    this.errorText = "";
    this.label = "";
    this.noAsterisk = false;
    this.supportingText = "";
    this.error = false;
    this.menuPositioning = "popover";
    this.clampMenuWidth = false;
    this.typeaheadDelay = DEFAULT_TYPEAHEAD_BUFFER_TIME;
    this.hasLeadingIcon = false;
    this.displayText = "";
    this.menuAlign = "start";
    this[_a] = "";
    this.lastUserSetValue = null;
    this.lastUserSetSelectedIndex = null;
    this.lastSelectedOption = null;
    this.lastSelectedOptionRecords = [];
    this.nativeError = false;
    this.nativeErrorText = "";
    this.focused = false;
    this.open = false;
    this.defaultFocus = FocusState.NONE;
    this.prevOpen = this.open;
    this.selectWidth = 0;
    if (isServer) {
      return;
    }
    this.addEventListener("focus", this.handleFocus.bind(this));
    this.addEventListener("blur", this.handleBlur.bind(this));
  }
  /**
   * Selects an option given the value of the option, and updates MdSelect's
   * value.
   */
  select(value) {
    const optionToSelect = this.options.find((option) => option.value === value);
    if (optionToSelect) {
      this.selectItem(optionToSelect);
    }
  }
  /**
   * Selects an option given the index of the option, and updates MdSelect's
   * value.
   */
  selectIndex(index) {
    const optionToSelect = this.options[index];
    if (optionToSelect) {
      this.selectItem(optionToSelect);
    }
  }
  /**
   * Reset the select to its default value.
   */
  reset() {
    for (const option of this.options) {
      option.selected = option.hasAttribute("selected");
    }
    this.updateValueAndDisplayText();
    this.nativeError = false;
    this.nativeErrorText = "";
  }
  [(_a = VALUE, onReportValidity)](invalidEvent) {
    invalidEvent?.preventDefault();
    const prevMessage = this.getErrorText();
    this.nativeError = !!invalidEvent;
    this.nativeErrorText = this.validationMessage;
    if (prevMessage === this.getErrorText()) {
      this.field?.reannounceError();
    }
  }
  update(changed) {
    if (!this.hasUpdated) {
      this.initUserSelection();
    }
    if (this.prevOpen !== this.open && this.open) {
      const selectRect = this.getBoundingClientRect();
      this.selectWidth = selectRect.width;
    }
    this.prevOpen = this.open;
    super.update(changed);
  }
  render() {
    return html`
      <span
        class="select ${classMap(this.getRenderClasses())}"
        @focusout=${this.handleFocusout}>
        ${this.renderField()} ${this.renderMenu()}
      </span>
    `;
  }
  async firstUpdated(changed) {
    await this.menu?.updateComplete;
    if (!this.lastSelectedOptionRecords.length) {
      this.initUserSelection();
    }
    if (!this.lastSelectedOptionRecords.length && !isServer && !this.options.length) {
      setTimeout(() => {
        this.updateValueAndDisplayText();
      });
    }
    super.firstUpdated(changed);
  }
  getRenderClasses() {
    return {
      "disabled": this.disabled,
      "error": this.error,
      "open": this.open
    };
  }
  renderField() {
    const ariaLabel = this.ariaLabel || this.label;
    return html2`
      <${this.fieldTag}
          aria-haspopup="listbox"
          role="combobox"
          part="field"
          id="field"
          tabindex=${this.disabled ? "-1" : "0"}
          aria-label=${ariaLabel || nothing}
          aria-describedby="description"
          aria-expanded=${this.open ? "true" : "false"}
          aria-controls="listbox"
          class="field"
          label=${this.label}
          ?no-asterisk=${this.noAsterisk}
          .focused=${this.focused || this.open}
          .populated=${!!this.displayText}
          .disabled=${this.disabled}
          .required=${this.required}
          .error=${this.hasError}
          ?has-start=${this.hasLeadingIcon}
          has-end
          supporting-text=${this.supportingText}
          error-text=${this.getErrorText()}
          @keydown=${this.handleKeydown}
          @click=${this.handleClick}>
         ${this.renderFieldContent()}
         <div id="description" slot="aria-describedby"></div>
      </${this.fieldTag}>`;
  }
  renderFieldContent() {
    return [
      this.renderLeadingIcon(),
      this.renderLabel(),
      this.renderTrailingIcon()
    ];
  }
  renderLeadingIcon() {
    return html`
      <span class="icon leading" slot="start">
        <slot name="leading-icon" @slotchange=${this.handleIconChange}></slot>
      </span>
    `;
  }
  renderTrailingIcon() {
    return html`
      <span class="icon trailing" slot="end">
        <slot name="trailing-icon" @slotchange=${this.handleIconChange}>
          <svg height="5" viewBox="7 10 10 5" focusable="false">
            <polygon
              class="down"
              stroke="none"
              fill-rule="evenodd"
              points="7 10 12 15 17 10"></polygon>
            <polygon
              class="up"
              stroke="none"
              fill-rule="evenodd"
              points="7 15 12 10 17 15"></polygon>
          </svg>
        </slot>
      </span>
    `;
  }
  renderLabel() {
    return html`<div id="label">${this.displayText || html`&nbsp;`}</div>`;
  }
  renderMenu() {
    const ariaLabel = this.label || this.ariaLabel;
    return html`<div class="menu-wrapper">
      <md-menu
        id="listbox"
        .defaultFocus=${this.defaultFocus}
        role="listbox"
        tabindex="-1"
        aria-label=${ariaLabel || nothing}
        stay-open-on-focusout
        part="menu"
        exportparts="focus-ring: menu-focus-ring"
        anchor="field"
        style=${styleMap({
      "--__menu-min-width": `${this.selectWidth}px`,
      "--__menu-max-width": this.clampMenuWidth ? `${this.selectWidth}px` : void 0
    })}
        no-navigation-wrap
        .open=${this.open}
        .quick=${this.quick}
        .positioning=${this.menuPositioning}
        .typeaheadDelay=${this.typeaheadDelay}
        .anchorCorner=${this.menuAlign === "start" ? "end-start" : "end-end"}
        .menuCorner=${this.menuAlign === "start" ? "start-start" : "start-end"}
        @opening=${this.handleOpening}
        @opened=${this.redispatchEvent}
        @closing=${this.redispatchEvent}
        @closed=${this.handleClosed}
        @close-menu=${this.handleCloseMenu}
        @request-selection=${this.handleRequestSelection}
        @request-deselection=${this.handleRequestDeselection}>
        ${this.renderMenuContent()}
      </md-menu>
    </div>`;
  }
  renderMenuContent() {
    return html`<slot></slot>`;
  }
  /**
   * Handles opening the select on keydown and typahead selection when the menu
   * is closed.
   */
  handleKeydown(event) {
    if (this.open || this.disabled || !this.menu) {
      return;
    }
    const typeaheadController = this.menu.typeaheadController;
    const isOpenKey = event.code === "Space" || event.code === "ArrowDown" || event.code === "ArrowUp" || event.code === "End" || event.code === "Home" || event.code === "Enter";
    if (!typeaheadController.isTypingAhead && isOpenKey) {
      event.preventDefault();
      this.open = true;
      switch (event.code) {
        case "Space":
        case "ArrowDown":
        case "Enter":
          this.defaultFocus = FocusState.NONE;
          break;
        case "End":
          this.defaultFocus = FocusState.LAST_ITEM;
          break;
        case "ArrowUp":
        case "Home":
          this.defaultFocus = FocusState.FIRST_ITEM;
          break;
        default:
          break;
      }
      return;
    }
    const isPrintableKey = event.key.length === 1;
    if (isPrintableKey) {
      typeaheadController.onKeydown(event);
      event.preventDefault();
      const { lastActiveRecord } = typeaheadController;
      if (!lastActiveRecord) {
        return;
      }
      this.labelEl?.setAttribute?.("aria-live", "polite");
      const hasChanged = this.selectItem(lastActiveRecord[TYPEAHEAD_RECORD.ITEM]);
      if (hasChanged) {
        this.dispatchInteractionEvents();
      }
    }
  }
  handleClick() {
    this.open = !this.open;
  }
  handleFocus() {
    this.focused = true;
  }
  handleBlur() {
    this.focused = false;
  }
  /**
   * Handles closing the menu when the focus leaves the select's subtree.
   */
  handleFocusout(event) {
    if (event.relatedTarget && isElementInSubtree(event.relatedTarget, this)) {
      return;
    }
    this.open = false;
  }
  /**
   * Gets a list of all selected select options as a list item record array.
   *
   * @return An array of selected list option records.
   */
  getSelectedOptions() {
    if (!this.menu) {
      this.lastSelectedOptionRecords = [];
      return null;
    }
    const items = this.menu.items;
    this.lastSelectedOptionRecords = getSelectedItems(items);
    return this.lastSelectedOptionRecords;
  }
  async getUpdateComplete() {
    await this.menu?.updateComplete;
    return super.getUpdateComplete();
  }
  /**
   * Gets the selected options from the DOM, and updates the value and display
   * text to the first selected option's value and headline respectively.
   *
   * @return Whether or not the selected option has changed since last update.
   */
  updateValueAndDisplayText() {
    const selectedOptions = this.getSelectedOptions() ?? [];
    let hasSelectedOptionChanged = false;
    if (selectedOptions.length) {
      const [firstSelectedOption] = selectedOptions[0];
      hasSelectedOptionChanged = this.lastSelectedOption !== firstSelectedOption;
      this.lastSelectedOption = firstSelectedOption;
      this[VALUE] = firstSelectedOption.value;
      this.displayText = firstSelectedOption.displayText;
    } else {
      hasSelectedOptionChanged = this.lastSelectedOption !== null;
      this.lastSelectedOption = null;
      this[VALUE] = "";
      this.displayText = "";
    }
    return hasSelectedOptionChanged;
  }
  /**
   * Focuses and activates the last selected item upon opening, and resets other
   * active items.
   */
  async handleOpening(e) {
    this.labelEl?.removeAttribute?.("aria-live");
    this.redispatchEvent(e);
    if (this.defaultFocus !== FocusState.NONE) {
      return;
    }
    const items = this.menu.items;
    const activeItem = getActiveItem(items)?.item;
    let [selectedItem] = this.lastSelectedOptionRecords[0] ?? [null];
    if (activeItem && activeItem !== selectedItem) {
      activeItem.tabIndex = -1;
    }
    selectedItem = selectedItem ?? items[0];
    if (selectedItem) {
      selectedItem.tabIndex = 0;
      selectedItem.focus();
    }
  }
  redispatchEvent(e) {
    redispatchEvent(this, e);
  }
  handleClosed(e) {
    this.open = false;
    this.redispatchEvent(e);
  }
  /**
   * Determines the reason for closing, and updates the UI accordingly.
   */
  handleCloseMenu(event) {
    const reason = event.detail.reason;
    const item = event.detail.itemPath[0];
    this.open = false;
    let hasChanged = false;
    if (reason.kind === "click-selection") {
      hasChanged = this.selectItem(item);
    } else if (reason.kind === "keydown" && isSelectableKey(reason.key)) {
      hasChanged = this.selectItem(item);
    } else {
      item.tabIndex = -1;
      item.blur();
    }
    if (hasChanged) {
      this.dispatchInteractionEvents();
    }
  }
  /**
   * Selects a given option, deselects other options, and updates the UI.
   *
   * @return Whether the last selected option has changed.
   */
  selectItem(item) {
    const selectedOptions = this.getSelectedOptions() ?? [];
    selectedOptions.forEach(([option]) => {
      if (item !== option) {
        option.selected = false;
      }
    });
    item.selected = true;
    return this.updateValueAndDisplayText();
  }
  /**
   * Handles updating selection when an option element requests selection via
   * property / attribute change.
   */
  handleRequestSelection(event) {
    const requestingOptionEl = event.target;
    if (this.lastSelectedOptionRecords.some(([option]) => option === requestingOptionEl)) {
      return;
    }
    this.selectItem(requestingOptionEl);
  }
  /**
   * Handles updating selection when an option element requests deselection via
   * property / attribute change.
   */
  handleRequestDeselection(event) {
    const requestingOptionEl = event.target;
    if (!this.lastSelectedOptionRecords.some(([option]) => option === requestingOptionEl)) {
      return;
    }
    this.updateValueAndDisplayText();
  }
  /**
   * Attempts to initialize the selected option from user-settable values like
   * SSR, setting `value`, or `selectedIndex` at startup.
   */
  initUserSelection() {
    if (this.lastUserSetValue && !this.lastSelectedOptionRecords.length) {
      this.select(this.lastUserSetValue);
    } else if (this.lastUserSetSelectedIndex !== null && !this.lastSelectedOptionRecords.length) {
      this.selectIndex(this.lastUserSetSelectedIndex);
    } else {
      this.updateValueAndDisplayText();
    }
  }
  handleIconChange() {
    this.hasLeadingIcon = this.leadingIcons.length > 0;
  }
  /**
   * Dispatches the `input` and `change` events.
   */
  dispatchInteractionEvents() {
    this.dispatchEvent(new Event("input", { bubbles: true, composed: true }));
    this.dispatchEvent(new Event("change", { bubbles: true }));
  }
  getErrorText() {
    return this.error ? this.errorText : this.nativeErrorText;
  }
  [getFormValue]() {
    return this.value;
  }
  formResetCallback() {
    this.reset();
  }
  formStateRestoreCallback(state2) {
    this.value = state2;
  }
  click() {
    this.field?.click();
  }
  [createValidator]() {
    return new SelectValidator(() => this);
  }
  [getValidityAnchor]() {
    return this.field;
  }
};
Select.shadowRootOptions = {
  ...LitElement.shadowRootOptions,
  delegatesFocus: true
};
__decorate([
  property({ type: Boolean })
], Select.prototype, "quick", void 0);
__decorate([
  property({ type: Boolean })
], Select.prototype, "required", void 0);
__decorate([
  property({ type: String, attribute: "error-text" })
], Select.prototype, "errorText", void 0);
__decorate([
  property()
], Select.prototype, "label", void 0);
__decorate([
  property({ type: Boolean, attribute: "no-asterisk" })
], Select.prototype, "noAsterisk", void 0);
__decorate([
  property({ type: String, attribute: "supporting-text" })
], Select.prototype, "supportingText", void 0);
__decorate([
  property({ type: Boolean, reflect: true })
], Select.prototype, "error", void 0);
__decorate([
  property({ attribute: "menu-positioning" })
], Select.prototype, "menuPositioning", void 0);
__decorate([
  property({ type: Boolean, attribute: "clamp-menu-width" })
], Select.prototype, "clampMenuWidth", void 0);
__decorate([
  property({ type: Number, attribute: "typeahead-delay" })
], Select.prototype, "typeaheadDelay", void 0);
__decorate([
  property({ type: Boolean, attribute: "has-leading-icon" })
], Select.prototype, "hasLeadingIcon", void 0);
__decorate([
  property({ attribute: "display-text" })
], Select.prototype, "displayText", void 0);
__decorate([
  property({ attribute: "menu-align" })
], Select.prototype, "menuAlign", void 0);
__decorate([
  property()
], Select.prototype, "value", null);
__decorate([
  property({ type: Number, attribute: "selected-index" })
], Select.prototype, "selectedIndex", null);
__decorate([
  state()
], Select.prototype, "nativeError", void 0);
__decorate([
  state()
], Select.prototype, "nativeErrorText", void 0);
__decorate([
  state()
], Select.prototype, "focused", void 0);
__decorate([
  state()
], Select.prototype, "open", void 0);
__decorate([
  state()
], Select.prototype, "defaultFocus", void 0);
__decorate([
  query(".field")
], Select.prototype, "field", void 0);
__decorate([
  query("md-menu")
], Select.prototype, "menu", void 0);
__decorate([
  query("#label")
], Select.prototype, "labelEl", void 0);
__decorate([
  queryAssignedElements({ slot: "leading-icon", flatten: true })
], Select.prototype, "leadingIcons", void 0);

export {
  Select,
  styles
};
/*! Bundled license information:

@material/web/select/internal/shared-styles.js:
  (**
   * @license
   * Copyright 2024 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   *)

@material/web/labs/behaviors/validators/select-validator.js:
@material/web/select/internal/shared.js:
@material/web/select/internal/select.js:
  (**
   * @license
   * Copyright 2023 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   *)
*/
//# sourceMappingURL=chunk-P7FJ2YJK.js.map
