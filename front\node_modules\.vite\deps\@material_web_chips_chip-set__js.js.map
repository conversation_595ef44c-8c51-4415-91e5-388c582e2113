{"version": 3, "sources": ["../../@material/web/chips/internal/chip-set.ts", "../../@material/web/chips/internal/chip-set-styles.ts", "../../@material/web/chips/chip-set.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {html, isServer, LitElement} from 'lit';\nimport {queryAssignedElements} from 'lit/decorators.js';\n\nimport {Chip} from './chip.js';\n\n/**\n * A chip set component.\n */\nexport class ChipSet extends LitElement {\n  get chips() {\n    return this.childElements.filter(\n      (child): child is Chip => child instanceof Chip,\n    );\n  }\n\n  @queryAssignedElements() private readonly childElements!: HTMLElement[];\n  private readonly internals =\n    // Cast needed for closure\n    (this as HTMLElement).attachInternals();\n\n  constructor() {\n    super();\n    if (!isServer) {\n      this.addEventListener('focusin', this.updateTabIndices.bind(this));\n      this.addEventListener('update-focus', this.updateTabIndices.bind(this));\n      this.addEventListener('keydown', this.handleKeyDown.bind(this));\n      this.internals.role = 'toolbar';\n    }\n  }\n\n  protected override render() {\n    return html`<slot @slotchange=${this.updateTabIndices}></slot>`;\n  }\n\n  private handleKeyDown(event: KeyboardEvent) {\n    const isLeft = event.key === 'ArrowLeft';\n    const isRight = event.key === 'ArrowRight';\n    const isHome = event.key === 'Home';\n    const isEnd = event.key === 'End';\n    // Ignore non-navigation keys\n    if (!isLeft && !isRight && !isHome && !isEnd) {\n      return;\n    }\n\n    const {chips} = this as {chips: MaybeMultiActionChip[]};\n    // Don't try to select another chip if there aren't any.\n    if (chips.length < 2) {\n      return;\n    }\n\n    // Prevent default interactions, such as scrolling.\n    event.preventDefault();\n\n    if (isHome || isEnd) {\n      const index = isHome ? 0 : chips.length - 1;\n      chips[index].focus({trailing: isEnd});\n      this.updateTabIndices();\n      return;\n    }\n\n    // Check if moving forwards or backwards\n    const isRtl = getComputedStyle(this).direction === 'rtl';\n    const forwards = isRtl ? isLeft : isRight;\n    const focusedChip = chips.find((chip) => chip.matches(':focus-within'));\n    if (!focusedChip) {\n      // If there is not already a chip focused, select the first or last chip\n      // based on the direction we're traveling.\n      const nextChip = forwards ? chips[0] : chips[chips.length - 1];\n      nextChip.focus({trailing: !forwards});\n      this.updateTabIndices();\n      return;\n    }\n\n    const currentIndex = chips.indexOf(focusedChip);\n    let nextIndex = forwards ? currentIndex + 1 : currentIndex - 1;\n    // Search for the next sibling that is not disabled to select.\n    // If we return to the host index, there is nothing to select.\n    while (nextIndex !== currentIndex) {\n      if (nextIndex >= chips.length) {\n        // Return to start if moving past the last item.\n        nextIndex = 0;\n      } else if (nextIndex < 0) {\n        // Go to end if moving before the first item.\n        nextIndex = chips.length - 1;\n      }\n\n      // Check if the next sibling is disabled. If so,\n      // move the index and continue searching.\n      //\n      // Some toolbar items may be focusable when disabled for increased\n      // visibility.\n      const nextChip = chips[nextIndex];\n      if (nextChip.disabled && !nextChip.alwaysFocusable) {\n        if (forwards) {\n          nextIndex++;\n        } else {\n          nextIndex--;\n        }\n\n        continue;\n      }\n\n      nextChip.focus({trailing: !forwards});\n      this.updateTabIndices();\n      break;\n    }\n  }\n\n  private updateTabIndices() {\n    // The chip that should be focusable is either the chip that currently has\n    // focus or the first chip that can be focused.\n    const {chips} = this;\n    let chipToFocus: Chip | undefined;\n    for (const chip of chips) {\n      const isChipFocusable = chip.alwaysFocusable || !chip.disabled;\n      const chipIsFocused = chip.matches(':focus-within');\n      if (chipIsFocused && isChipFocusable) {\n        // Found the first chip that is actively focused. This overrides the\n        // first focusable chip found.\n        chipToFocus = chip;\n        continue;\n      }\n\n      if (isChipFocusable && !chipToFocus) {\n        chipToFocus = chip;\n      }\n\n      // Disable non-focused chips. If we disable all of them, we'll grant focus\n      // to the first focusable child that was found.\n      chip.tabIndex = -1;\n    }\n\n    if (chipToFocus) {\n      chipToFocus.tabIndex = 0;\n    }\n  }\n}\n\ninterface MaybeMultiActionChip extends Chip {\n  focus(options?: FocusOptions & {trailing?: boolean}): void;\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n// Generated stylesheet for ./chips/internal/chip-set-styles.css.\nimport {css} from 'lit';\nexport const styles = css`:host{display:flex;flex-wrap:wrap;gap:8px}\n`;\n", "/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {ChipSet} from './internal/chip-set.js';\nimport {styles} from './internal/chip-set-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-chip-set': MdChipSet;\n  }\n}\n\n/**\n * TODO(b/243982145): add docs\n *\n * @final\n * @suppress {visibility}\n */\n@customElement('md-chip-set')\nexport class MdChipSet extends ChipSet {\n  static override styles: CSSResultOrNative[] = [styles];\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAcM,IAAO,UAAP,cAAuB,WAAU;EACrC,IAAI,QAAK;AACP,WAAO,KAAK,cAAc,OACxB,CAAC,UAAyB,iBAAiB,IAAI;EAEnD;EAOA,cAAA;AACE,UAAK;AALU,SAAA;IAEd,KAAqB,gBAAe;AAIrC,QAAI,CAAC,UAAU;AACb,WAAK,iBAAiB,WAAW,KAAK,iBAAiB,KAAK,IAAI,CAAC;AACjE,WAAK,iBAAiB,gBAAgB,KAAK,iBAAiB,KAAK,IAAI,CAAC;AACtE,WAAK,iBAAiB,WAAW,KAAK,cAAc,KAAK,IAAI,CAAC;AAC9D,WAAK,UAAU,OAAO;IACxB;EACF;EAEmB,SAAM;AACvB,WAAO,yBAAyB,KAAK,gBAAgB;EACvD;EAEQ,cAAc,OAAoB;AACxC,UAAM,SAAS,MAAM,QAAQ;AAC7B,UAAM,UAAU,MAAM,QAAQ;AAC9B,UAAM,SAAS,MAAM,QAAQ;AAC7B,UAAM,QAAQ,MAAM,QAAQ;AAE5B,QAAI,CAAC,UAAU,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO;AAC5C;IACF;AAEA,UAAM,EAAC,MAAK,IAAI;AAEhB,QAAI,MAAM,SAAS,GAAG;AACpB;IACF;AAGA,UAAM,eAAc;AAEpB,QAAI,UAAU,OAAO;AACnB,YAAM,QAAQ,SAAS,IAAI,MAAM,SAAS;AAC1C,YAAM,KAAK,EAAE,MAAM,EAAC,UAAU,MAAK,CAAC;AACpC,WAAK,iBAAgB;AACrB;IACF;AAGA,UAAM,QAAQ,iBAAiB,IAAI,EAAE,cAAc;AACnD,UAAM,WAAW,QAAQ,SAAS;AAClC,UAAM,cAAc,MAAM,KAAK,CAAC,SAAS,KAAK,QAAQ,eAAe,CAAC;AACtE,QAAI,CAAC,aAAa;AAGhB,YAAM,WAAW,WAAW,MAAM,CAAC,IAAI,MAAM,MAAM,SAAS,CAAC;AAC7D,eAAS,MAAM,EAAC,UAAU,CAAC,SAAQ,CAAC;AACpC,WAAK,iBAAgB;AACrB;IACF;AAEA,UAAM,eAAe,MAAM,QAAQ,WAAW;AAC9C,QAAI,YAAY,WAAW,eAAe,IAAI,eAAe;AAG7D,WAAO,cAAc,cAAc;AACjC,UAAI,aAAa,MAAM,QAAQ;AAE7B,oBAAY;MACd,WAAW,YAAY,GAAG;AAExB,oBAAY,MAAM,SAAS;MAC7B;AAOA,YAAM,WAAW,MAAM,SAAS;AAChC,UAAI,SAAS,YAAY,CAAC,SAAS,iBAAiB;AAClD,YAAI,UAAU;AACZ;QACF,OAAO;AACL;QACF;AAEA;MACF;AAEA,eAAS,MAAM,EAAC,UAAU,CAAC,SAAQ,CAAC;AACpC,WAAK,iBAAgB;AACrB;IACF;EACF;EAEQ,mBAAgB;AAGtB,UAAM,EAAC,MAAK,IAAI;AAChB,QAAI;AACJ,eAAW,QAAQ,OAAO;AACxB,YAAM,kBAAkB,KAAK,mBAAmB,CAAC,KAAK;AACtD,YAAM,gBAAgB,KAAK,QAAQ,eAAe;AAClD,UAAI,iBAAiB,iBAAiB;AAGpC,sBAAc;AACd;MACF;AAEA,UAAI,mBAAmB,CAAC,aAAa;AACnC,sBAAc;MAChB;AAIA,WAAK,WAAW;IAClB;AAEA,QAAI,aAAa;AACf,kBAAY,WAAW;IACzB;EACF;;AAxH0C,WAAA;EAAzC,sBAAqB;;;;ACdjB,IAAM,SAAS;;;;ACkBf,IAAM,YAAN,MAAMA,mBAAkB,QAAO;;AACpB,UAAA,SAA8B,CAAC,MAAM;AAD1C,YAAS,WAAA;EADrB,cAAc,aAAa;GACf,SAAS;", "names": ["MdChipSet"]}