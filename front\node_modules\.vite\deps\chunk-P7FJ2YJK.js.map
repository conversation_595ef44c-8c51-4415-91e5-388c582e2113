{"version": 3, "sources": ["../../@material/web/select/internal/shared-styles.ts", "../../@material/web/labs/behaviors/validators/select-validator.ts", "../../@material/web/select/internal/shared.ts", "../../@material/web/select/internal/select.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2024 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n// Generated stylesheet for ./select/internal/shared-styles.css.\nimport {css} from 'lit';\nexport const styles = css`:host{color:unset;min-width:210px;display:flex}.field{cursor:default;outline:none}.select{position:relative;flex-direction:column}.icon.trailing svg,.icon ::slotted(*){fill:currentColor}.icon ::slotted(*){width:inherit;height:inherit;font-size:inherit}.icon slot{display:flex;height:100%;width:100%;align-items:center;justify-content:center}.icon.trailing :is(.up,.down){opacity:0;transition:opacity 75ms linear 75ms}.select:not(.open) .down,.select.open .up{opacity:1}.field,.select,md-menu{min-width:inherit;width:inherit;max-width:inherit;display:flex}md-menu{min-width:var(--__menu-min-width);max-width:var(--__menu-max-width, inherit)}.menu-wrapper{width:0px;height:0px;max-width:inherit}md-menu ::slotted(:not[disabled]){cursor:pointer}.field,.select{width:100%}:host{display:inline-flex}:host([disabled]){pointer-events:none}\n`;\n", "/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {html, render} from 'lit';\n\nimport {Validator} from './validator.js';\n\n/**\n * Constraint validation properties for a select dropdown.\n */\nexport interface SelectState {\n  /**\n   * The current selected value.\n   */\n  readonly value: string;\n\n  /**\n   * Whether the select is required.\n   */\n  readonly required: boolean;\n}\n\n/**\n * A validator that provides constraint validation that emulates `<select>`\n * validation.\n */\nexport class SelectValidator extends Validator<SelectState> {\n  private selectControl?: HTMLSelectElement;\n\n  protected override computeValidity(state: SelectState) {\n    if (!this.selectControl) {\n      // Lazily create the platform select\n      this.selectControl = document.createElement('select');\n    }\n\n    render(html`<option value=${state.value}></option>`, this.selectControl);\n\n    this.selectControl.value = state.value;\n    this.selectControl.required = state.required;\n    return {\n      validity: this.selectControl.validity,\n      validationMessage: this.selectControl.validationMessage,\n    };\n  }\n\n  protected override equals(prev: SelectState, next: SelectState) {\n    return prev.value === next.value && prev.required === next.required;\n  }\n\n  protected override copy({value, required}: SelectState) {\n    return {value, required};\n  }\n}\n", "/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {SelectOption} from './selectoption/select-option.js';\n\n/**\n * A type that describes a SelectOption and its index.\n */\nexport type SelectOptionRecord = [SelectOption, number];\n\n/**\n * Given a list of select options, this function will return an array of\n * SelectOptionRecords that are selected.\n *\n * @return An array of SelectOptionRecords describing the options that are\n * selected.\n */\nexport function getSelectedItems(items: SelectOption[]) {\n  const selectedItemRecords: SelectOptionRecord[] = [];\n\n  for (let i = 0; i < items.length; i++) {\n    const item = items[i];\n    if (item.selected) {\n      selectedItemRecords.push([item, i]);\n    }\n  }\n\n  return selectedItemRecords;\n}\n", "/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport '../../menu/menu.js';\n\nimport {html, isServer, LitElement, nothing, PropertyValues} from 'lit';\nimport {property, query, queryAssignedElements, state} from 'lit/decorators.js';\nimport {ClassInfo, classMap} from 'lit/directives/class-map.js';\nimport {styleMap} from 'lit/directives/style-map.js';\nimport {html as staticHtml, StaticValue} from 'lit/static-html.js';\n\nimport {Field} from '../../field/internal/field.js';\nimport {ARIAMixinStrict} from '../../internal/aria/aria.js';\nimport {mixinDelegatesAria} from '../../internal/aria/delegate.js';\nimport {redispatchEvent} from '../../internal/events/redispatch-event.js';\nimport {\n  createValidator,\n  getValidityAnchor,\n  mixinConstraintValidation,\n} from '../../labs/behaviors/constraint-validation.js';\nimport {mixinElementInternals} from '../../labs/behaviors/element-internals.js';\nimport {\n  getFormValue,\n  mixinFormAssociated,\n} from '../../labs/behaviors/form-associated.js';\nimport {\n  mixinOnReportValidity,\n  onReportValidity,\n} from '../../labs/behaviors/on-report-validity.js';\nimport {SelectValidator} from '../../labs/behaviors/validators/select-validator.js';\nimport {getActiveItem} from '../../list/internal/list-navigation-helpers.js';\nimport {\n  CloseMenuEvent,\n  FocusState,\n  isElementInSubtree,\n  isSelectableKey,\n} from '../../menu/internal/controllers/shared.js';\nimport {TYPEAHEAD_RECORD} from '../../menu/internal/controllers/typeaheadController.js';\nimport {DEFAULT_TYPEAHEAD_BUFFER_TIME, Menu} from '../../menu/internal/menu.js';\nimport {SelectOption} from './selectoption/select-option.js';\nimport {\n  createRequestDeselectionEvent,\n  createRequestSelectionEvent,\n} from './selectoption/selectOptionController.js';\nimport {getSelectedItems, SelectOptionRecord} from './shared.js';\n\nconst VALUE = Symbol('value');\n\n// Separate variable needed for closure.\nconst selectBaseClass = mixinDelegatesAria(\n  mixinOnReportValidity(\n    mixinConstraintValidation(\n      mixinFormAssociated(mixinElementInternals(LitElement)),\n    ),\n  ),\n);\n\n/**\n * @fires change {Event} The native `change` event on\n * [`<input>`](https://developer.mozilla.org/en-US/docs/Web/API/HTMLElement/change_event)\n * --bubbles\n * @fires input {InputEvent} The native `input` event on\n * [`<input>`](https://developer.mozilla.org/en-US/docs/Web/API/HTMLElement/input_event)\n * --bubbles --composed\n * @fires opening {Event} Fired when the select's menu is about to open.\n * @fires opened {Event} Fired when the select's menu has finished animations\n * and opened.\n * @fires closing {Event} Fired when the select's menu is about to close.\n * @fires closed {Event} Fired when the select's menu has finished animations\n * and closed.\n */\nexport abstract class Select extends selectBaseClass {\n  /** @nocollapse */\n  static override shadowRootOptions = {\n    ...LitElement.shadowRootOptions,\n    delegatesFocus: true,\n  };\n\n  /**\n   * Opens the menu synchronously with no animation.\n   */\n  @property({type: Boolean}) quick = false;\n\n  /**\n   * Whether or not the select is required.\n   */\n  @property({type: Boolean}) required = false;\n\n  /**\n   * The error message that replaces supporting text when `error` is true. If\n   * `errorText` is an empty string, then the supporting text will continue to\n   * show.\n   *\n   * This error message overrides the error message displayed by\n   * `reportValidity()`.\n   */\n  @property({type: String, attribute: 'error-text'}) errorText = '';\n\n  /**\n   * The floating label for the field.\n   */\n  @property() label = '';\n\n  /**\n   * Disables the asterisk on the floating label, when the select is\n   * required.\n   */\n  @property({type: Boolean, attribute: 'no-asterisk'}) noAsterisk = false;\n\n  /**\n   * Conveys additional information below the select, such as how it should\n   * be used.\n   */\n  @property({type: String, attribute: 'supporting-text'}) supportingText = '';\n\n  /**\n   * Gets or sets whether or not the select is in a visually invalid state.\n   *\n   * This error state overrides the error state controlled by\n   * `reportValidity()`.\n   */\n  @property({type: Boolean, reflect: true}) error = false;\n\n  /**\n   * Whether or not the underlying md-menu should be position: fixed to display\n   * in a top-level manner, or position: absolute.\n   *\n   * position:fixed is useful for cases where select is inside of another\n   * element with stacking context and hidden overflows such as `md-dialog`.\n   */\n  @property({attribute: 'menu-positioning'})\n  menuPositioning: 'absolute' | 'fixed' | 'popover' = 'popover';\n\n  /**\n   * Clamps the menu-width to the width of the select.\n   */\n  @property({type: Boolean, attribute: 'clamp-menu-width'})\n  clampMenuWidth = false;\n\n  /**\n   * The max time between the keystrokes of the typeahead select / menu behavior\n   * before it clears the typeahead buffer.\n   */\n  @property({type: Number, attribute: 'typeahead-delay'})\n  typeaheadDelay = DEFAULT_TYPEAHEAD_BUFFER_TIME;\n\n  /**\n   * Whether or not the text field has a leading icon. Used for SSR.\n   */\n  @property({type: Boolean, attribute: 'has-leading-icon'})\n  hasLeadingIcon = false;\n\n  /**\n   * Text to display in the field. Only set for SSR.\n   */\n  @property({attribute: 'display-text'}) displayText = '';\n\n  /**\n   * Whether the menu should be aligned to the start or the end of the select's\n   * textbox.\n   */\n  @property({attribute: 'menu-align'}) menuAlign: 'start' | 'end' = 'start';\n\n  /**\n   * The value of the currently selected option.\n   *\n   * Note: For SSR, set `[selected]` on the requested option and `displayText`\n   * rather than setting `value` setting `value` will incur a DOM query.\n   */\n  @property()\n  get value(): string {\n    return this[VALUE];\n  }\n\n  set value(value: string) {\n    if (isServer) return;\n    this.lastUserSetValue = value;\n    this.select(value);\n  }\n\n  [VALUE] = '';\n\n  get options() {\n    // NOTE: this does a DOM query.\n    return (this.menu?.items ?? []) as SelectOption[];\n  }\n\n  /**\n   * The index of the currently selected option.\n   *\n   * Note: For SSR, set `[selected]` on the requested option and `displayText`\n   * rather than setting `selectedIndex` setting `selectedIndex` will incur a\n   * DOM query.\n   */\n  @property({type: Number, attribute: 'selected-index'})\n  get selectedIndex(): number {\n    // tslint:disable-next-line:enforce-name-casing\n    const [_option, index] = (this.getSelectedOptions() ?? [])[0] ?? [];\n    return index ?? -1;\n  }\n\n  set selectedIndex(index: number) {\n    this.lastUserSetSelectedIndex = index;\n    this.selectIndex(index);\n  }\n\n  /**\n   * Returns an array of selected options.\n   *\n   * NOTE: md-select only supports single selection.\n   */\n  get selectedOptions() {\n    return (this.getSelectedOptions() ?? []).map(([option]) => option);\n  }\n\n  protected abstract readonly fieldTag: StaticValue;\n\n  /**\n   * Used for initializing select when the user sets the `value` directly.\n   */\n  private lastUserSetValue: string | null = null;\n\n  /**\n   * Used for initializing select when the user sets the `selectedIndex`\n   * directly.\n   */\n  private lastUserSetSelectedIndex: number | null = null;\n\n  /**\n   * Used for `input` and `change` event change detection.\n   */\n  private lastSelectedOption: SelectOption | null = null;\n\n  // tslint:disable-next-line:enforce-name-casing\n  private lastSelectedOptionRecords: SelectOptionRecord[] = [];\n\n  /**\n   * Whether or not a native error has been reported via `reportValidity()`.\n   */\n  @state() private nativeError = false;\n\n  /**\n   * The validation message displayed from a native error via\n   * `reportValidity()`.\n   */\n  @state() private nativeErrorText = '';\n  private get hasError() {\n    return this.error || this.nativeError;\n  }\n\n  @state() private focused = false;\n  @state() private open = false;\n  @state() private defaultFocus: FocusState = FocusState.NONE;\n  @query('.field') private readonly field!: Field | null;\n  @query('md-menu') private readonly menu!: Menu | null;\n  @query('#label') private readonly labelEl!: HTMLElement;\n  @queryAssignedElements({slot: 'leading-icon', flatten: true})\n  private readonly leadingIcons!: Element[];\n  // Have to keep track of previous open because it's state and private and thus\n  // cannot be tracked in PropertyValues<this> map.\n  private prevOpen = this.open;\n  private selectWidth = 0;\n\n  constructor() {\n    super();\n    if (isServer) {\n      return;\n    }\n\n    this.addEventListener('focus', this.handleFocus.bind(this));\n    this.addEventListener('blur', this.handleBlur.bind(this));\n  }\n\n  /**\n   * Selects an option given the value of the option, and updates MdSelect's\n   * value.\n   */\n  select(value: string) {\n    const optionToSelect = this.options.find(\n      (option) => option.value === value,\n    );\n    if (optionToSelect) {\n      this.selectItem(optionToSelect);\n    }\n  }\n\n  /**\n   * Selects an option given the index of the option, and updates MdSelect's\n   * value.\n   */\n  selectIndex(index: number) {\n    const optionToSelect = this.options[index];\n    if (optionToSelect) {\n      this.selectItem(optionToSelect);\n    }\n  }\n\n  /**\n   * Reset the select to its default value.\n   */\n  reset() {\n    for (const option of this.options) {\n      option.selected = option.hasAttribute('selected');\n    }\n\n    this.updateValueAndDisplayText();\n    this.nativeError = false;\n    this.nativeErrorText = '';\n  }\n\n  override [onReportValidity](invalidEvent: Event | null) {\n    // Prevent default pop-up behavior.\n    invalidEvent?.preventDefault();\n\n    const prevMessage = this.getErrorText();\n    this.nativeError = !!invalidEvent;\n    this.nativeErrorText = this.validationMessage;\n\n    if (prevMessage === this.getErrorText()) {\n      this.field?.reannounceError();\n    }\n  }\n\n  protected override update(changed: PropertyValues<Select>) {\n    // In SSR the options will be ready to query, so try to figure out what\n    // the value and display text should be.\n    if (!this.hasUpdated) {\n      this.initUserSelection();\n    }\n\n    // We have just opened the menu.\n    // We are only able to check for the select's rect in `update()` instead of\n    // having to wait for `updated()` because the menu can never be open on\n    // first render since it is not settable and Lit SSR does not support click\n    // events which would open the menu.\n    if (this.prevOpen !== this.open && this.open) {\n      const selectRect = this.getBoundingClientRect();\n      this.selectWidth = selectRect.width;\n    }\n\n    this.prevOpen = this.open;\n    super.update(changed);\n  }\n\n  protected override render() {\n    return html`\n      <span\n        class=\"select ${classMap(this.getRenderClasses())}\"\n        @focusout=${this.handleFocusout}>\n        ${this.renderField()} ${this.renderMenu()}\n      </span>\n    `;\n  }\n\n  protected override async firstUpdated(changed: PropertyValues<Select>) {\n    await this.menu?.updateComplete;\n    // If this has been handled on update already due to SSR, try again.\n    if (!this.lastSelectedOptionRecords.length) {\n      this.initUserSelection();\n    }\n\n    // Case for when the DOM is streaming, there are no children, and a child\n    // has [selected] set on it, we need to wait for DOM to render something.\n    if (\n      !this.lastSelectedOptionRecords.length &&\n      !isServer &&\n      !this.options.length\n    ) {\n      setTimeout(() => {\n        this.updateValueAndDisplayText();\n      });\n    }\n\n    super.firstUpdated(changed);\n  }\n\n  private getRenderClasses(): ClassInfo {\n    return {\n      'disabled': this.disabled,\n      'error': this.error,\n      'open': this.open,\n    };\n  }\n\n  private renderField() {\n    const ariaLabel = (this as ARIAMixinStrict).ariaLabel || this.label;\n    return staticHtml`\n      <${this.fieldTag}\n          aria-haspopup=\"listbox\"\n          role=\"combobox\"\n          part=\"field\"\n          id=\"field\"\n          tabindex=${this.disabled ? '-1' : '0'}\n          aria-label=${ariaLabel || nothing}\n          aria-describedby=\"description\"\n          aria-expanded=${this.open ? 'true' : 'false'}\n          aria-controls=\"listbox\"\n          class=\"field\"\n          label=${this.label}\n          ?no-asterisk=${this.noAsterisk}\n          .focused=${this.focused || this.open}\n          .populated=${!!this.displayText}\n          .disabled=${this.disabled}\n          .required=${this.required}\n          .error=${this.hasError}\n          ?has-start=${this.hasLeadingIcon}\n          has-end\n          supporting-text=${this.supportingText}\n          error-text=${this.getErrorText()}\n          @keydown=${this.handleKeydown}\n          @click=${this.handleClick}>\n         ${this.renderFieldContent()}\n         <div id=\"description\" slot=\"aria-describedby\"></div>\n      </${this.fieldTag}>`;\n  }\n\n  private renderFieldContent() {\n    return [\n      this.renderLeadingIcon(),\n      this.renderLabel(),\n      this.renderTrailingIcon(),\n    ];\n  }\n\n  private renderLeadingIcon() {\n    return html`\n      <span class=\"icon leading\" slot=\"start\">\n        <slot name=\"leading-icon\" @slotchange=${this.handleIconChange}></slot>\n      </span>\n    `;\n  }\n\n  private renderTrailingIcon() {\n    return html`\n      <span class=\"icon trailing\" slot=\"end\">\n        <slot name=\"trailing-icon\" @slotchange=${this.handleIconChange}>\n          <svg height=\"5\" viewBox=\"7 10 10 5\" focusable=\"false\">\n            <polygon\n              class=\"down\"\n              stroke=\"none\"\n              fill-rule=\"evenodd\"\n              points=\"7 10 12 15 17 10\"></polygon>\n            <polygon\n              class=\"up\"\n              stroke=\"none\"\n              fill-rule=\"evenodd\"\n              points=\"7 15 12 10 17 15\"></polygon>\n          </svg>\n        </slot>\n      </span>\n    `;\n  }\n\n  private renderLabel() {\n    // need to render &nbsp; so that line-height can apply and give it a\n    // non-zero height\n    return html`<div id=\"label\">${this.displayText || html`&nbsp;`}</div>`;\n  }\n\n  private renderMenu() {\n    const ariaLabel = this.label || (this as ARIAMixinStrict).ariaLabel;\n    return html`<div class=\"menu-wrapper\">\n      <md-menu\n        id=\"listbox\"\n        .defaultFocus=${this.defaultFocus}\n        role=\"listbox\"\n        tabindex=\"-1\"\n        aria-label=${ariaLabel || nothing}\n        stay-open-on-focusout\n        part=\"menu\"\n        exportparts=\"focus-ring: menu-focus-ring\"\n        anchor=\"field\"\n        style=${styleMap({\n          '--__menu-min-width': `${this.selectWidth}px`,\n          '--__menu-max-width': this.clampMenuWidth\n            ? `${this.selectWidth}px`\n            : undefined,\n        })}\n        no-navigation-wrap\n        .open=${this.open}\n        .quick=${this.quick}\n        .positioning=${this.menuPositioning}\n        .typeaheadDelay=${this.typeaheadDelay}\n        .anchorCorner=${this.menuAlign === 'start' ? 'end-start' : 'end-end'}\n        .menuCorner=${this.menuAlign === 'start' ? 'start-start' : 'start-end'}\n        @opening=${this.handleOpening}\n        @opened=${this.redispatchEvent}\n        @closing=${this.redispatchEvent}\n        @closed=${this.handleClosed}\n        @close-menu=${this.handleCloseMenu}\n        @request-selection=${this.handleRequestSelection}\n        @request-deselection=${this.handleRequestDeselection}>\n        ${this.renderMenuContent()}\n      </md-menu>\n    </div>`;\n  }\n\n  private renderMenuContent() {\n    return html`<slot></slot>`;\n  }\n\n  /**\n   * Handles opening the select on keydown and typahead selection when the menu\n   * is closed.\n   */\n  private handleKeydown(event: KeyboardEvent) {\n    if (this.open || this.disabled || !this.menu) {\n      return;\n    }\n\n    const typeaheadController = this.menu.typeaheadController;\n    const isOpenKey =\n      event.code === 'Space' ||\n      event.code === 'ArrowDown' ||\n      event.code === 'ArrowUp' ||\n      event.code === 'End' ||\n      event.code === 'Home' ||\n      event.code === 'Enter';\n\n    // Do not open if currently typing ahead because the user may be typing the\n    // spacebar to match a word with a space\n    if (!typeaheadController.isTypingAhead && isOpenKey) {\n      event.preventDefault();\n      this.open = true;\n\n      // https://www.w3.org/WAI/ARIA/apg/patterns/combobox/examples/combobox-select-only/#kbd_label\n      switch (event.code) {\n        case 'Space':\n        case 'ArrowDown':\n        case 'Enter':\n          // We will handle focusing last selected item in this.handleOpening()\n          this.defaultFocus = FocusState.NONE;\n          break;\n        case 'End':\n          this.defaultFocus = FocusState.LAST_ITEM;\n          break;\n        case 'ArrowUp':\n        case 'Home':\n          this.defaultFocus = FocusState.FIRST_ITEM;\n          break;\n        default:\n          break;\n      }\n      return;\n    }\n\n    const isPrintableKey = event.key.length === 1;\n\n    // Handles typing ahead when the menu is closed by delegating the event to\n    // the underlying menu's typeaheadController\n    if (isPrintableKey) {\n      typeaheadController.onKeydown(event);\n      event.preventDefault();\n\n      const {lastActiveRecord} = typeaheadController;\n\n      if (!lastActiveRecord) {\n        return;\n      }\n\n      this.labelEl?.setAttribute?.('aria-live', 'polite');\n      const hasChanged = this.selectItem(\n        lastActiveRecord[TYPEAHEAD_RECORD.ITEM] as SelectOption,\n      );\n\n      if (hasChanged) {\n        this.dispatchInteractionEvents();\n      }\n    }\n  }\n\n  private handleClick() {\n    this.open = !this.open;\n  }\n\n  private handleFocus() {\n    this.focused = true;\n  }\n\n  private handleBlur() {\n    this.focused = false;\n  }\n\n  /**\n   * Handles closing the menu when the focus leaves the select's subtree.\n   */\n  private handleFocusout(event: FocusEvent) {\n    // Don't close the menu if we are switching focus between menu,\n    // select-option, and field\n    if (event.relatedTarget && isElementInSubtree(event.relatedTarget, this)) {\n      return;\n    }\n\n    this.open = false;\n  }\n\n  /**\n   * Gets a list of all selected select options as a list item record array.\n   *\n   * @return An array of selected list option records.\n   */\n  private getSelectedOptions() {\n    if (!this.menu) {\n      this.lastSelectedOptionRecords = [];\n      return null;\n    }\n\n    const items = this.menu.items as SelectOption[];\n    this.lastSelectedOptionRecords = getSelectedItems(items);\n    return this.lastSelectedOptionRecords;\n  }\n\n  override async getUpdateComplete() {\n    await this.menu?.updateComplete;\n    return super.getUpdateComplete();\n  }\n\n  /**\n   * Gets the selected options from the DOM, and updates the value and display\n   * text to the first selected option's value and headline respectively.\n   *\n   * @return Whether or not the selected option has changed since last update.\n   */\n  private updateValueAndDisplayText() {\n    const selectedOptions = this.getSelectedOptions() ?? [];\n    // Used to determine whether or not we need to fire an input / change event\n    // which fire whenever the option element changes (value or selectedIndex)\n    // on user interaction.\n    let hasSelectedOptionChanged = false;\n\n    if (selectedOptions.length) {\n      const [firstSelectedOption] = selectedOptions[0];\n      hasSelectedOptionChanged =\n        this.lastSelectedOption !== firstSelectedOption;\n      this.lastSelectedOption = firstSelectedOption;\n      this[VALUE] = firstSelectedOption.value;\n      this.displayText = firstSelectedOption.displayText;\n    } else {\n      hasSelectedOptionChanged = this.lastSelectedOption !== null;\n      this.lastSelectedOption = null;\n      this[VALUE] = '';\n      this.displayText = '';\n    }\n\n    return hasSelectedOptionChanged;\n  }\n\n  /**\n   * Focuses and activates the last selected item upon opening, and resets other\n   * active items.\n   */\n  private async handleOpening(e: Event) {\n    this.labelEl?.removeAttribute?.('aria-live');\n    this.redispatchEvent(e);\n\n    // FocusState.NONE means we want to handle focus ourselves and focus the\n    // last selected item.\n    if (this.defaultFocus !== FocusState.NONE) {\n      return;\n    }\n\n    const items = this.menu!.items as SelectOption[];\n    const activeItem = getActiveItem(items)?.item;\n    let [selectedItem] = this.lastSelectedOptionRecords[0] ?? [null];\n\n    // This is true if the user keys through the list but clicks out of the menu\n    // thus no close-menu event is fired by an item and we can't clean up in\n    // handleCloseMenu.\n    if (activeItem && activeItem !== selectedItem) {\n      activeItem.tabIndex = -1;\n    }\n\n    // in the case that nothing is selected, focus the first item\n    selectedItem = selectedItem ?? items[0];\n\n    if (selectedItem) {\n      selectedItem.tabIndex = 0;\n      selectedItem.focus();\n    }\n  }\n\n  private redispatchEvent(e: Event) {\n    redispatchEvent(this, e);\n  }\n\n  private handleClosed(e: Event) {\n    this.open = false;\n    this.redispatchEvent(e);\n  }\n\n  /**\n   * Determines the reason for closing, and updates the UI accordingly.\n   */\n  private handleCloseMenu(event: CloseMenuEvent) {\n    const reason = event.detail.reason;\n    const item = event.detail.itemPath[0] as SelectOption;\n    this.open = false;\n    let hasChanged = false;\n\n    if (reason.kind === 'click-selection') {\n      hasChanged = this.selectItem(item);\n    } else if (reason.kind === 'keydown' && isSelectableKey(reason.key)) {\n      hasChanged = this.selectItem(item);\n    } else {\n      // This can happen on ESC being pressed\n      item.tabIndex = -1;\n      item.blur();\n    }\n\n    // Dispatch interaction events since selection has been made via keyboard\n    // or mouse.\n    if (hasChanged) {\n      this.dispatchInteractionEvents();\n    }\n  }\n\n  /**\n   * Selects a given option, deselects other options, and updates the UI.\n   *\n   * @return Whether the last selected option has changed.\n   */\n  private selectItem(item: SelectOption) {\n    const selectedOptions = this.getSelectedOptions() ?? [];\n    selectedOptions.forEach(([option]) => {\n      if (item !== option) {\n        option.selected = false;\n      }\n    });\n    item.selected = true;\n\n    return this.updateValueAndDisplayText();\n  }\n\n  /**\n   * Handles updating selection when an option element requests selection via\n   * property / attribute change.\n   */\n  private handleRequestSelection(\n    event: ReturnType<typeof createRequestSelectionEvent>,\n  ) {\n    const requestingOptionEl = event.target as SelectOption & HTMLElement;\n\n    // No-op if this item is already selected.\n    if (\n      this.lastSelectedOptionRecords.some(\n        ([option]) => option === requestingOptionEl,\n      )\n    ) {\n      return;\n    }\n\n    this.selectItem(requestingOptionEl);\n  }\n\n  /**\n   * Handles updating selection when an option element requests deselection via\n   * property / attribute change.\n   */\n  private handleRequestDeselection(\n    event: ReturnType<typeof createRequestDeselectionEvent>,\n  ) {\n    const requestingOptionEl = event.target as SelectOption & HTMLElement;\n\n    // No-op if this item is not even in the list of tracked selected items.\n    if (\n      !this.lastSelectedOptionRecords.some(\n        ([option]) => option === requestingOptionEl,\n      )\n    ) {\n      return;\n    }\n\n    this.updateValueAndDisplayText();\n  }\n\n  /**\n   * Attempts to initialize the selected option from user-settable values like\n   * SSR, setting `value`, or `selectedIndex` at startup.\n   */\n  private initUserSelection() {\n    // User has set `.value` directly, but internals have not yet booted up.\n    if (this.lastUserSetValue && !this.lastSelectedOptionRecords.length) {\n      this.select(this.lastUserSetValue);\n\n      // User has set `.selectedIndex` directly, but internals have not yet\n      // booted up.\n    } else if (\n      this.lastUserSetSelectedIndex !== null &&\n      !this.lastSelectedOptionRecords.length\n    ) {\n      this.selectIndex(this.lastUserSetSelectedIndex);\n\n      // Regular boot up!\n    } else {\n      this.updateValueAndDisplayText();\n    }\n  }\n\n  private handleIconChange() {\n    this.hasLeadingIcon = this.leadingIcons.length > 0;\n  }\n\n  /**\n   * Dispatches the `input` and `change` events.\n   */\n  private dispatchInteractionEvents() {\n    this.dispatchEvent(new Event('input', {bubbles: true, composed: true}));\n    this.dispatchEvent(new Event('change', {bubbles: true}));\n  }\n\n  private getErrorText() {\n    return this.error ? this.errorText : this.nativeErrorText;\n  }\n\n  // Writable mixin properties for lit-html binding, needed for lit-analyzer\n  declare disabled: boolean;\n  declare name: string;\n\n  override [getFormValue]() {\n    return this.value;\n  }\n\n  override formResetCallback() {\n    this.reset();\n  }\n\n  override formStateRestoreCallback(state: string) {\n    this.value = state;\n  }\n\n  override click() {\n    this.field?.click();\n  }\n\n  override [createValidator]() {\n    return new SelectValidator(() => this);\n  }\n\n  override [getValidityAnchor]() {\n    return this.field;\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOO,IAAM,SAAS;;;;ACsBhB,IAAO,kBAAP,cAA+B,UAAsB;EAGtC,gBAAgBA,QAAkB;AACnD,QAAI,CAAC,KAAK,eAAe;AAEvB,WAAK,gBAAgB,SAAS,cAAc,QAAQ;IACtD;AAEA,WAAO,qBAAqBA,OAAM,KAAK,cAAc,KAAK,aAAa;AAEvE,SAAK,cAAc,QAAQA,OAAM;AACjC,SAAK,cAAc,WAAWA,OAAM;AACpC,WAAO;MACL,UAAU,KAAK,cAAc;MAC7B,mBAAmB,KAAK,cAAc;;EAE1C;EAEmB,OAAO,MAAmB,MAAiB;AAC5D,WAAO,KAAK,UAAU,KAAK,SAAS,KAAK,aAAa,KAAK;EAC7D;EAEmB,KAAK,EAAC,OAAO,SAAQ,GAAc;AACpD,WAAO,EAAC,OAAO,SAAQ;EACzB;;;;AClCI,SAAU,iBAAiB,OAAqB;AACpD,QAAM,sBAA4C,CAAA;AAElD,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,UAAM,OAAO,MAAM,CAAC;AACpB,QAAI,KAAK,UAAU;AACjB,0BAAoB,KAAK,CAAC,MAAM,CAAC,CAAC;IACpC;EACF;AAEA,SAAO;AACT;;;;ACkBA,IAAM,QAAQ,OAAO,OAAO;AAG5B,IAAM,kBAAkB,mBACtB,sBACE,0BACE,oBAAoB,sBAAsB,UAAU,CAAC,CAAC,CACvD,CACF;AAiBG,IAAgB,SAAhB,cAA+B,gBAAe;;;;;;;EAmGlD,IAAI,QAAK;AACP,WAAO,KAAK,KAAK;EACnB;EAEA,IAAI,MAAM,OAAa;AACrB,QAAI;AAAU;AACd,SAAK,mBAAmB;AACxB,SAAK,OAAO,KAAK;EACnB;EAIA,IAAI,UAAO;AAET,WAAQ,KAAK,MAAM,SAAS,CAAA;EAC9B;;;;;;;;EAUA,IAAI,gBAAa;AAEf,UAAM,CAAC,SAAS,KAAK,KAAK,KAAK,mBAAkB,KAAM,CAAA,GAAI,CAAC,KAAK,CAAA;AACjE,WAAO,SAAS;EAClB;EAEA,IAAI,cAAc,OAAa;AAC7B,SAAK,2BAA2B;AAChC,SAAK,YAAY,KAAK;EACxB;;;;;;EAOA,IAAI,kBAAe;AACjB,YAAQ,KAAK,mBAAkB,KAAM,CAAA,GAAI,IAAI,CAAC,CAAC,MAAM,MAAM,MAAM;EACnE;EAiCA,IAAY,WAAQ;AAClB,WAAO,KAAK,SAAS,KAAK;EAC5B;EAeA,cAAA;AACE,UAAK;AAvLoB,SAAA,QAAQ;AAKR,SAAA,WAAW;AAUa,SAAA,YAAY;AAKnD,SAAA,QAAQ;AAMiC,SAAA,aAAa;AAMV,SAAA,iBAAiB;AAQ/B,SAAA,QAAQ;AAUlD,SAAA,kBAAoD;AAMpD,SAAA,iBAAiB;AAOjB,SAAA,iBAAiB;AAMjB,SAAA,iBAAiB;AAKsB,SAAA,cAAc;AAMhB,SAAA,YAA6B;AAmBlE,SAAA,EAAA,IAAU;AAwCF,SAAA,mBAAkC;AAMlC,SAAA,2BAA0C;AAK1C,SAAA,qBAA0C;AAG1C,SAAA,4BAAkD,CAAA;AAKzC,SAAA,cAAc;AAMd,SAAA,kBAAkB;AAKlB,SAAA,UAAU;AACV,SAAA,OAAO;AACP,SAAA,eAA2B,WAAW;AAQ/C,SAAA,WAAW,KAAK;AAChB,SAAA,cAAc;AAIpB,QAAI,UAAU;AACZ;IACF;AAEA,SAAK,iBAAiB,SAAS,KAAK,YAAY,KAAK,IAAI,CAAC;AAC1D,SAAK,iBAAiB,QAAQ,KAAK,WAAW,KAAK,IAAI,CAAC;EAC1D;;;;;EAMA,OAAO,OAAa;AAClB,UAAM,iBAAiB,KAAK,QAAQ,KAClC,CAAC,WAAW,OAAO,UAAU,KAAK;AAEpC,QAAI,gBAAgB;AAClB,WAAK,WAAW,cAAc;IAChC;EACF;;;;;EAMA,YAAY,OAAa;AACvB,UAAM,iBAAiB,KAAK,QAAQ,KAAK;AACzC,QAAI,gBAAgB;AAClB,WAAK,WAAW,cAAc;IAChC;EACF;;;;EAKA,QAAK;AACH,eAAW,UAAU,KAAK,SAAS;AACjC,aAAO,WAAW,OAAO,aAAa,UAAU;IAClD;AAEA,SAAK,0BAAyB;AAC9B,SAAK,cAAc;AACnB,SAAK,kBAAkB;EACzB;EAES,EAAA,KAlIR,OAkIS,iBAAgB,EAAE,cAA0B;AAEpD,kBAAc,eAAc;AAE5B,UAAM,cAAc,KAAK,aAAY;AACrC,SAAK,cAAc,CAAC,CAAC;AACrB,SAAK,kBAAkB,KAAK;AAE5B,QAAI,gBAAgB,KAAK,aAAY,GAAI;AACvC,WAAK,OAAO,gBAAe;IAC7B;EACF;EAEmB,OAAO,SAA+B;AAGvD,QAAI,CAAC,KAAK,YAAY;AACpB,WAAK,kBAAiB;IACxB;AAOA,QAAI,KAAK,aAAa,KAAK,QAAQ,KAAK,MAAM;AAC5C,YAAM,aAAa,KAAK,sBAAqB;AAC7C,WAAK,cAAc,WAAW;IAChC;AAEA,SAAK,WAAW,KAAK;AACrB,UAAM,OAAO,OAAO;EACtB;EAEmB,SAAM;AACvB,WAAO;;wBAEa,SAAS,KAAK,iBAAgB,CAAE,CAAC;oBACrC,KAAK,cAAc;UAC7B,KAAK,YAAW,CAAE,IAAI,KAAK,WAAU,CAAE;;;EAG/C;EAEmB,MAAM,aAAa,SAA+B;AACnE,UAAM,KAAK,MAAM;AAEjB,QAAI,CAAC,KAAK,0BAA0B,QAAQ;AAC1C,WAAK,kBAAiB;IACxB;AAIA,QACE,CAAC,KAAK,0BAA0B,UAChC,CAAC,YACD,CAAC,KAAK,QAAQ,QACd;AACA,iBAAW,MAAK;AACd,aAAK,0BAAyB;MAChC,CAAC;IACH;AAEA,UAAM,aAAa,OAAO;EAC5B;EAEQ,mBAAgB;AACtB,WAAO;MACL,YAAY,KAAK;MACjB,SAAS,KAAK;MACd,QAAQ,KAAK;;EAEjB;EAEQ,cAAW;AACjB,UAAM,YAAa,KAAyB,aAAa,KAAK;AAC9D,WAAOC;SACF,KAAK,QAAQ;;;;;qBAKD,KAAK,WAAW,OAAO,GAAG;uBACxB,aAAa,OAAO;;0BAEjB,KAAK,OAAO,SAAS,OAAO;;;kBAGpC,KAAK,KAAK;yBACH,KAAK,UAAU;qBACnB,KAAK,WAAW,KAAK,IAAI;uBACvB,CAAC,CAAC,KAAK,WAAW;sBACnB,KAAK,QAAQ;sBACb,KAAK,QAAQ;mBAChB,KAAK,QAAQ;uBACT,KAAK,cAAc;;4BAEd,KAAK,cAAc;uBACxB,KAAK,aAAY,CAAE;qBACrB,KAAK,aAAa;mBACpB,KAAK,WAAW;WACxB,KAAK,mBAAkB,CAAE;;UAE1B,KAAK,QAAQ;EACrB;EAEQ,qBAAkB;AACxB,WAAO;MACL,KAAK,kBAAiB;MACtB,KAAK,YAAW;MAChB,KAAK,mBAAkB;;EAE3B;EAEQ,oBAAiB;AACvB,WAAO;;gDAEqC,KAAK,gBAAgB;;;EAGnE;EAEQ,qBAAkB;AACxB,WAAO;;iDAEsC,KAAK,gBAAgB;;;;;;;;;;;;;;;;EAgBpE;EAEQ,cAAW;AAGjB,WAAO,uBAAuB,KAAK,eAAe,YAAY;EAChE;EAEQ,aAAU;AAChB,UAAM,YAAY,KAAK,SAAU,KAAyB;AAC1D,WAAO;;;wBAGa,KAAK,YAAY;;;qBAGpB,aAAa,OAAO;;;;;gBAKzB,SAAS;MACf,sBAAsB,GAAG,KAAK,WAAW;MACzC,sBAAsB,KAAK,iBACvB,GAAG,KAAK,WAAW,OACnB;KACL,CAAC;;gBAEM,KAAK,IAAI;iBACR,KAAK,KAAK;uBACJ,KAAK,eAAe;0BACjB,KAAK,cAAc;wBACrB,KAAK,cAAc,UAAU,cAAc,SAAS;sBACtD,KAAK,cAAc,UAAU,gBAAgB,WAAW;mBAC3D,KAAK,aAAa;kBACnB,KAAK,eAAe;mBACnB,KAAK,eAAe;kBACrB,KAAK,YAAY;sBACb,KAAK,eAAe;6BACb,KAAK,sBAAsB;+BACzB,KAAK,wBAAwB;UAClD,KAAK,kBAAiB,CAAE;;;EAGhC;EAEQ,oBAAiB;AACvB,WAAO;EACT;;;;;EAMQ,cAAc,OAAoB;AACxC,QAAI,KAAK,QAAQ,KAAK,YAAY,CAAC,KAAK,MAAM;AAC5C;IACF;AAEA,UAAM,sBAAsB,KAAK,KAAK;AACtC,UAAM,YACJ,MAAM,SAAS,WACf,MAAM,SAAS,eACf,MAAM,SAAS,aACf,MAAM,SAAS,SACf,MAAM,SAAS,UACf,MAAM,SAAS;AAIjB,QAAI,CAAC,oBAAoB,iBAAiB,WAAW;AACnD,YAAM,eAAc;AACpB,WAAK,OAAO;AAGZ,cAAQ,MAAM,MAAM;QAClB,KAAK;QACL,KAAK;QACL,KAAK;AAEH,eAAK,eAAe,WAAW;AAC/B;QACF,KAAK;AACH,eAAK,eAAe,WAAW;AAC/B;QACF,KAAK;QACL,KAAK;AACH,eAAK,eAAe,WAAW;AAC/B;QACF;AACE;MACJ;AACA;IACF;AAEA,UAAM,iBAAiB,MAAM,IAAI,WAAW;AAI5C,QAAI,gBAAgB;AAClB,0BAAoB,UAAU,KAAK;AACnC,YAAM,eAAc;AAEpB,YAAM,EAAC,iBAAgB,IAAI;AAE3B,UAAI,CAAC,kBAAkB;AACrB;MACF;AAEA,WAAK,SAAS,eAAe,aAAa,QAAQ;AAClD,YAAM,aAAa,KAAK,WACtB,iBAAiB,iBAAiB,IAAI,CAAiB;AAGzD,UAAI,YAAY;AACd,aAAK,0BAAyB;MAChC;IACF;EACF;EAEQ,cAAW;AACjB,SAAK,OAAO,CAAC,KAAK;EACpB;EAEQ,cAAW;AACjB,SAAK,UAAU;EACjB;EAEQ,aAAU;AAChB,SAAK,UAAU;EACjB;;;;EAKQ,eAAe,OAAiB;AAGtC,QAAI,MAAM,iBAAiB,mBAAmB,MAAM,eAAe,IAAI,GAAG;AACxE;IACF;AAEA,SAAK,OAAO;EACd;;;;;;EAOQ,qBAAkB;AACxB,QAAI,CAAC,KAAK,MAAM;AACd,WAAK,4BAA4B,CAAA;AACjC,aAAO;IACT;AAEA,UAAM,QAAQ,KAAK,KAAK;AACxB,SAAK,4BAA4B,iBAAiB,KAAK;AACvD,WAAO,KAAK;EACd;EAES,MAAM,oBAAiB;AAC9B,UAAM,KAAK,MAAM;AACjB,WAAO,MAAM,kBAAiB;EAChC;;;;;;;EAQQ,4BAAyB;AAC/B,UAAM,kBAAkB,KAAK,mBAAkB,KAAM,CAAA;AAIrD,QAAI,2BAA2B;AAE/B,QAAI,gBAAgB,QAAQ;AAC1B,YAAM,CAAC,mBAAmB,IAAI,gBAAgB,CAAC;AAC/C,iCACE,KAAK,uBAAuB;AAC9B,WAAK,qBAAqB;AAC1B,WAAK,KAAK,IAAI,oBAAoB;AAClC,WAAK,cAAc,oBAAoB;IACzC,OAAO;AACL,iCAA2B,KAAK,uBAAuB;AACvD,WAAK,qBAAqB;AAC1B,WAAK,KAAK,IAAI;AACd,WAAK,cAAc;IACrB;AAEA,WAAO;EACT;;;;;EAMQ,MAAM,cAAc,GAAQ;AAClC,SAAK,SAAS,kBAAkB,WAAW;AAC3C,SAAK,gBAAgB,CAAC;AAItB,QAAI,KAAK,iBAAiB,WAAW,MAAM;AACzC;IACF;AAEA,UAAM,QAAQ,KAAK,KAAM;AACzB,UAAM,aAAa,cAAc,KAAK,GAAG;AACzC,QAAI,CAAC,YAAY,IAAI,KAAK,0BAA0B,CAAC,KAAK,CAAC,IAAI;AAK/D,QAAI,cAAc,eAAe,cAAc;AAC7C,iBAAW,WAAW;IACxB;AAGA,mBAAe,gBAAgB,MAAM,CAAC;AAEtC,QAAI,cAAc;AAChB,mBAAa,WAAW;AACxB,mBAAa,MAAK;IACpB;EACF;EAEQ,gBAAgB,GAAQ;AAC9B,oBAAgB,MAAM,CAAC;EACzB;EAEQ,aAAa,GAAQ;AAC3B,SAAK,OAAO;AACZ,SAAK,gBAAgB,CAAC;EACxB;;;;EAKQ,gBAAgB,OAAqB;AAC3C,UAAM,SAAS,MAAM,OAAO;AAC5B,UAAM,OAAO,MAAM,OAAO,SAAS,CAAC;AACpC,SAAK,OAAO;AACZ,QAAI,aAAa;AAEjB,QAAI,OAAO,SAAS,mBAAmB;AACrC,mBAAa,KAAK,WAAW,IAAI;IACnC,WAAW,OAAO,SAAS,aAAa,gBAAgB,OAAO,GAAG,GAAG;AACnE,mBAAa,KAAK,WAAW,IAAI;IACnC,OAAO;AAEL,WAAK,WAAW;AAChB,WAAK,KAAI;IACX;AAIA,QAAI,YAAY;AACd,WAAK,0BAAyB;IAChC;EACF;;;;;;EAOQ,WAAW,MAAkB;AACnC,UAAM,kBAAkB,KAAK,mBAAkB,KAAM,CAAA;AACrD,oBAAgB,QAAQ,CAAC,CAAC,MAAM,MAAK;AACnC,UAAI,SAAS,QAAQ;AACnB,eAAO,WAAW;MACpB;IACF,CAAC;AACD,SAAK,WAAW;AAEhB,WAAO,KAAK,0BAAyB;EACvC;;;;;EAMQ,uBACN,OAAqD;AAErD,UAAM,qBAAqB,MAAM;AAGjC,QACE,KAAK,0BAA0B,KAC7B,CAAC,CAAC,MAAM,MAAM,WAAW,kBAAkB,GAE7C;AACA;IACF;AAEA,SAAK,WAAW,kBAAkB;EACpC;;;;;EAMQ,yBACN,OAAuD;AAEvD,UAAM,qBAAqB,MAAM;AAGjC,QACE,CAAC,KAAK,0BAA0B,KAC9B,CAAC,CAAC,MAAM,MAAM,WAAW,kBAAkB,GAE7C;AACA;IACF;AAEA,SAAK,0BAAyB;EAChC;;;;;EAMQ,oBAAiB;AAEvB,QAAI,KAAK,oBAAoB,CAAC,KAAK,0BAA0B,QAAQ;AACnE,WAAK,OAAO,KAAK,gBAAgB;IAInC,WACE,KAAK,6BAA6B,QAClC,CAAC,KAAK,0BAA0B,QAChC;AACA,WAAK,YAAY,KAAK,wBAAwB;IAGhD,OAAO;AACL,WAAK,0BAAyB;IAChC;EACF;EAEQ,mBAAgB;AACtB,SAAK,iBAAiB,KAAK,aAAa,SAAS;EACnD;;;;EAKQ,4BAAyB;AAC/B,SAAK,cAAc,IAAI,MAAM,SAAS,EAAC,SAAS,MAAM,UAAU,KAAI,CAAC,CAAC;AACtE,SAAK,cAAc,IAAI,MAAM,UAAU,EAAC,SAAS,KAAI,CAAC,CAAC;EACzD;EAEQ,eAAY;AAClB,WAAO,KAAK,QAAQ,KAAK,YAAY,KAAK;EAC5C;EAMS,CAAC,YAAY,IAAC;AACrB,WAAO,KAAK;EACd;EAES,oBAAiB;AACxB,SAAK,MAAK;EACZ;EAES,yBAAyBC,QAAa;AAC7C,SAAK,QAAQA;EACf;EAES,QAAK;AACZ,SAAK,OAAO,MAAK;EACnB;EAES,CAAC,eAAe,IAAC;AACxB,WAAO,IAAI,gBAAgB,MAAM,IAAI;EACvC;EAES,CAAC,iBAAiB,IAAC;AAC1B,WAAO,KAAK;EACd;;AA/vBgB,OAAA,oBAAoB;EAClC,GAAG,WAAW;EACd,gBAAgB;;AAMS,WAAA;EAA1B,SAAS,EAAC,MAAM,QAAO,CAAC;;AAKE,WAAA;EAA1B,SAAS,EAAC,MAAM,QAAO,CAAC;;AAU0B,WAAA;EAAlD,SAAS,EAAC,MAAM,QAAQ,WAAW,aAAY,CAAC;;AAKrC,WAAA;EAAX,SAAQ;;AAM4C,WAAA;EAApD,SAAS,EAAC,MAAM,SAAS,WAAW,cAAa,CAAC;;AAMK,WAAA;EAAvD,SAAS,EAAC,MAAM,QAAQ,WAAW,kBAAiB,CAAC;;AAQZ,WAAA;EAAzC,SAAS,EAAC,MAAM,SAAS,SAAS,KAAI,CAAC;;AAUxC,WAAA;EADC,SAAS,EAAC,WAAW,mBAAkB,CAAC;;AAOzC,WAAA;EADC,SAAS,EAAC,MAAM,SAAS,WAAW,mBAAkB,CAAC;;AAQxD,WAAA;EADC,SAAS,EAAC,MAAM,QAAQ,WAAW,kBAAiB,CAAC;;AAOtD,WAAA;EADC,SAAS,EAAC,MAAM,SAAS,WAAW,mBAAkB,CAAC;;AAMjB,WAAA;EAAtC,SAAS,EAAC,WAAW,eAAc,CAAC;;AAMA,WAAA;EAApC,SAAS,EAAC,WAAW,aAAY,CAAC;;AASnC,WAAA;EADC,SAAQ;;AA0BT,WAAA;EADC,SAAS,EAAC,MAAM,QAAQ,WAAW,iBAAgB,CAAC;;AA6CpC,WAAA;EAAhB,MAAK;;AAMW,WAAA;EAAhB,MAAK;;AAKW,WAAA;EAAhB,MAAK;;AACW,WAAA;EAAhB,MAAK;;AACW,WAAA;EAAhB,MAAK;;AAC4B,WAAA;EAAjC,MAAM,QAAQ;;AACoB,WAAA;EAAlC,MAAM,SAAS;;AACkB,WAAA;EAAjC,MAAM,QAAQ;;AAEE,WAAA;EADhB,sBAAsB,EAAC,MAAM,gBAAgB,SAAS,KAAI,CAAC;;", "names": ["state", "html", "state"]}