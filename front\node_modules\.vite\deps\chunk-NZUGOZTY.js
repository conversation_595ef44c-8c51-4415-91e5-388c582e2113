import {
  Chip
} from "./chunk-PHPFMGV5.js";
import {
  __decorate,
  customElement,
  queryAssignedElements
} from "./chunk-PZNDE6JX.js";
import {
  LitElement,
  css,
  html,
  isServer
} from "./chunk-4GZ3EDRH.js";

// node_modules/@material/web/chips/internal/chip-set.js
var ChipSet = class extends LitElement {
  get chips() {
    return this.childElements.filter((child) => child instanceof Chip);
  }
  constructor() {
    super();
    this.internals = // Cast needed for closure
    this.attachInternals();
    if (!isServer) {
      this.addEventListener("focusin", this.updateTabIndices.bind(this));
      this.addEventListener("update-focus", this.updateTabIndices.bind(this));
      this.addEventListener("keydown", this.handleKeyDown.bind(this));
      this.internals.role = "toolbar";
    }
  }
  render() {
    return html`<slot @slotchange=${this.updateTabIndices}></slot>`;
  }
  handleKeyDown(event) {
    const isLeft = event.key === "ArrowLeft";
    const isRight = event.key === "ArrowRight";
    const isHome = event.key === "Home";
    const isEnd = event.key === "End";
    if (!isLeft && !isRight && !isHome && !isEnd) {
      return;
    }
    const { chips } = this;
    if (chips.length < 2) {
      return;
    }
    event.preventDefault();
    if (isHome || isEnd) {
      const index = isHome ? 0 : chips.length - 1;
      chips[index].focus({ trailing: isEnd });
      this.updateTabIndices();
      return;
    }
    const isRtl = getComputedStyle(this).direction === "rtl";
    const forwards = isRtl ? isLeft : isRight;
    const focusedChip = chips.find((chip) => chip.matches(":focus-within"));
    if (!focusedChip) {
      const nextChip = forwards ? chips[0] : chips[chips.length - 1];
      nextChip.focus({ trailing: !forwards });
      this.updateTabIndices();
      return;
    }
    const currentIndex = chips.indexOf(focusedChip);
    let nextIndex = forwards ? currentIndex + 1 : currentIndex - 1;
    while (nextIndex !== currentIndex) {
      if (nextIndex >= chips.length) {
        nextIndex = 0;
      } else if (nextIndex < 0) {
        nextIndex = chips.length - 1;
      }
      const nextChip = chips[nextIndex];
      if (nextChip.disabled && !nextChip.alwaysFocusable) {
        if (forwards) {
          nextIndex++;
        } else {
          nextIndex--;
        }
        continue;
      }
      nextChip.focus({ trailing: !forwards });
      this.updateTabIndices();
      break;
    }
  }
  updateTabIndices() {
    const { chips } = this;
    let chipToFocus;
    for (const chip of chips) {
      const isChipFocusable = chip.alwaysFocusable || !chip.disabled;
      const chipIsFocused = chip.matches(":focus-within");
      if (chipIsFocused && isChipFocusable) {
        chipToFocus = chip;
        continue;
      }
      if (isChipFocusable && !chipToFocus) {
        chipToFocus = chip;
      }
      chip.tabIndex = -1;
    }
    if (chipToFocus) {
      chipToFocus.tabIndex = 0;
    }
  }
};
__decorate([
  queryAssignedElements()
], ChipSet.prototype, "childElements", void 0);

// node_modules/@material/web/chips/internal/chip-set-styles.js
var styles = css`:host{display:flex;flex-wrap:wrap;gap:8px}
`;

// node_modules/@material/web/chips/chip-set.js
var MdChipSet = class MdChipSet2 extends ChipSet {
};
MdChipSet.styles = [styles];
MdChipSet = __decorate([
  customElement("md-chip-set")
], MdChipSet);

export {
  MdChipSet
};
/*! Bundled license information:

@material/web/chips/internal/chip-set.js:
@material/web/chips/chip-set.js:
  (**
   * @license
   * Copyright 2023 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   *)

@material/web/chips/internal/chip-set-styles.js:
  (**
   * @license
   * Copyright 2024 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   *)
*/
//# sourceMappingURL=chunk-NZUGOZTY.js.map
