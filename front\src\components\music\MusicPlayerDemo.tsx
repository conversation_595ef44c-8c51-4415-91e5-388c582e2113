import React from 'react';
import SimpleMusicPlayer from './SimpleMusicPlayer';
import './MusicPlayerDemo.css';

const MusicPlayerDemo: React.FC = () => {
  return (
    <div className="music-page">
      <div className="music-page-header">
        <h1 className="md-typescale-display-small">Music Player</h1>
        <p className="md-typescale-body-large">
          Experience our modern music player with advanced controls and beautiful design.
        </p>
      </div>

      <div className="music-player-container">
        <SimpleMusicPlayer />
      </div>

      <div className="music-page-features">
        <div className="feature-grid">
          <div className="feature-item">
            <md-icon>play_circle</md-icon>
            <h3>High Quality Audio</h3>
            <p>Crystal clear sound with advanced audio processing</p>
          </div>
          <div className="feature-item">
            <md-icon>shuffle</md-icon>
            <h3>Smart Shuffle</h3>
            <p>Intelligent playlist shuffling for better music discovery</p>
          </div>
          <div className="feature-item">
            <md-icon>favorite</md-icon>
            <h3>Personal Library</h3>
            <p>Save your favorite tracks and create custom playlists</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MusicPlayerDemo;
