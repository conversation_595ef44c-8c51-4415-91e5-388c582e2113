{"version": 3, "sources": ["../../@material/web/tabs/internal/tabs.ts", "../../@material/web/tabs/internal/tabs-styles.ts", "../../@material/web/tabs/tabs.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport '../../divider/divider.js';\n\nimport {html, isServer, LitElement} from 'lit';\nimport {property, query, queryAssignedElements} from 'lit/decorators.js';\n\nimport {ANIMATE_INDICATOR, Tab} from './tab.js';\n\n/**\n * @fires change {Event} Fired when the selected tab changes. The target's\n * `activeTabIndex` or `activeTab` provide information about the selection\n * change. The change event is fired when a user interaction like a space/enter\n * key or click cause a selection change. The tab selection based on these\n * actions can be cancelled by calling preventDefault on the triggering\n * `keydown` or `click` event. --bubbles\n *\n * @example\n * // perform an action if a tab is clicked\n * tabs.addEventListener('change', (event: Event) => {\n *   if (event.target.activeTabIndex === 2)\n *     takeAction();\n *   }\n * });\n *\n * // prevent a click from triggering tab selection under some condition\n * tabs.addEventListener('click', (event: Event) => {\n *   if (notReady)\n *     event.preventDefault();\n *   }\n * });\n *\n */\nexport class Tabs extends LitElement {\n  /**\n   * The tabs of this tab bar.\n   */\n  @queryAssignedElements({flatten: true, selector: '[md-tab]'})\n  readonly tabs!: Tab[];\n\n  /**\n   * The currently selected tab, `null` only when there are no tab children.\n   *\n   * @export\n   */\n  get activeTab() {\n    return this.tabs.find((tab) => tab.active) ?? null;\n  }\n  set activeTab(tab: Tab | null) {\n    // Ignore setting activeTab to null. As long as there are children, one tab\n    // must be selected.\n    if (tab) {\n      this.activateTab(tab);\n    }\n  }\n\n  /**\n   * The index of the currently selected tab.\n   *\n   * @export\n   */\n  @property({type: Number, attribute: 'active-tab-index'})\n  get activeTabIndex() {\n    return this.tabs.findIndex((tab) => tab.active);\n  }\n  set activeTabIndex(index: number) {\n    const activateTabAtIndex = () => {\n      const tab = this.tabs[index];\n      // Ignore out-of-bound indices.\n      if (tab) {\n        this.activateTab(tab);\n      }\n    };\n\n    if (!this.slotElement) {\n      // This is needed to support setting the activeTabIndex via a lit property\n      // binding.\n      //\n      // ```ts\n      // html`\n      //   <md-tabs .activeTabIndex=${1}>\n      //     <md-tab>First</md-tab>\n      //     <md-tab>Second</md-tab>\n      //   </md-tabs>\n      // `;\n      // ```\n      //\n      // It's needed since lit's rendering lifecycle is asynchronous, and the\n      // `<slot>` element hasn't rendered, so `tabs` is empty.\n      this.updateComplete.then(activateTabAtIndex);\n      return;\n    }\n\n    activateTabAtIndex();\n  }\n\n  /**\n   * Whether or not to automatically select a tab when it is focused.\n   */\n  @property({type: Boolean, attribute: 'auto-activate'}) autoActivate = false;\n\n  @query('.tabs') private readonly tabsScrollerElement!: HTMLElement | null;\n  @query('slot') private readonly slotElement!: HTMLSlotElement | null;\n\n  private get focusedTab() {\n    return this.tabs.find((tab) => tab.matches(':focus-within'));\n  }\n\n  private readonly internals =\n    // Cast needed for closure\n    (this as HTMLElement).attachInternals();\n\n  constructor() {\n    super();\n    if (!isServer) {\n      this.internals.role = 'tablist';\n      this.addEventListener('keydown', this.handleKeydown.bind(this));\n      this.addEventListener('keyup', this.handleKeyup.bind(this));\n      this.addEventListener('focusout', this.handleFocusout.bind(this));\n    }\n  }\n\n  /**\n   * Scrolls the toolbar, if overflowing, to the active tab, or the provided\n   * tab.\n   *\n   * @param tabToScrollTo The tab that should be scrolled to. Defaults to the\n   *     active tab.\n   * @return A Promise that resolves after the tab has been scrolled to.\n   */\n  async scrollToTab(tabToScrollTo?: Tab | null) {\n    await this.updateComplete;\n    const {tabs} = this;\n    tabToScrollTo ??= this.activeTab;\n    if (\n      !tabToScrollTo ||\n      !tabs.includes(tabToScrollTo) ||\n      !this.tabsScrollerElement\n    ) {\n      return;\n    }\n\n    // wait for tabs to render.\n    for (const tab of this.tabs) {\n      await tab.updateComplete;\n    }\n\n    const offset = tabToScrollTo.offsetLeft;\n    const extent = tabToScrollTo.offsetWidth;\n    const scroll = this.scrollLeft;\n    const hostExtent = this.offsetWidth;\n    const scrollMargin = 48;\n    const min = offset - scrollMargin;\n    const max = offset + extent - hostExtent + scrollMargin;\n    const to = Math.min(min, Math.max(max, scroll));\n    // When a tab is focused, use 'auto' to use the CSS `scroll-behavior`. The\n    // default behavior is smooth scrolling. However, when there is not a tab\n    // focused on initialization, use 'instant' to immediately bring the focused\n    // tab into view.\n    const behavior: ScrollBehavior = !this.focusedTab ? 'instant' : 'auto';\n    this.tabsScrollerElement.scrollTo({behavior, top: 0, left: to});\n  }\n\n  protected override render() {\n    return html`\n      <div class=\"tabs\">\n        <slot\n          @slotchange=${this.handleSlotChange}\n          @click=${this.handleTabClick}></slot>\n      </div>\n      <md-divider part=\"divider\"></md-divider>\n    `;\n  }\n\n  private async handleTabClick(event: Event) {\n    const tab = event.target;\n    // Allow event to bubble\n    await 0;\n    if (event.defaultPrevented || !isTab(tab) || tab.active) {\n      return;\n    }\n\n    this.activateTab(tab);\n  }\n\n  private activateTab(activeTab: Tab) {\n    const {tabs} = this;\n    const previousTab = this.activeTab;\n    if (!tabs.includes(activeTab) || previousTab === activeTab) {\n      // Ignore setting activeTab to a tab element that is not a child.\n      return;\n    }\n\n    for (const tab of tabs) {\n      tab.active = tab === activeTab;\n    }\n\n    if (previousTab) {\n      // Don't dispatch a change event if activating a tab when no previous tabs\n      // were selected, such as when md-tabs auto-selects the first tab.\n      const defaultPrevented = !this.dispatchEvent(\n        new Event('change', {bubbles: true, cancelable: true}),\n      );\n      if (defaultPrevented) {\n        for (const tab of tabs) {\n          tab.active = tab === previousTab;\n        }\n        return;\n      }\n\n      activeTab[ANIMATE_INDICATOR](previousTab);\n    }\n\n    this.updateFocusableTab(activeTab);\n    this.scrollToTab(activeTab);\n  }\n\n  private updateFocusableTab(focusableTab: Tab) {\n    for (const tab of this.tabs) {\n      tab.tabIndex = tab === focusableTab ? 0 : -1;\n    }\n  }\n\n  // focus item on keydown and optionally select it\n  private async handleKeydown(event: KeyboardEvent) {\n    // Allow event to bubble.\n    await 0;\n    const isLeft = event.key === 'ArrowLeft';\n    const isRight = event.key === 'ArrowRight';\n    const isHome = event.key === 'Home';\n    const isEnd = event.key === 'End';\n    // Ignore non-navigation keys\n    if (event.defaultPrevented || (!isLeft && !isRight && !isHome && !isEnd)) {\n      return;\n    }\n\n    const {tabs} = this;\n    // Don't try to select another tab if there aren't any.\n    if (tabs.length < 2) {\n      return;\n    }\n\n    // Prevent default interactions, such as scrolling.\n    event.preventDefault();\n\n    let indexToFocus: number;\n    if (isHome || isEnd) {\n      indexToFocus = isHome ? 0 : tabs.length - 1;\n    } else {\n      // Check if moving forwards or backwards\n      const isRtl = getComputedStyle(this).direction === 'rtl';\n      const forwards = isRtl ? isLeft : isRight;\n      const {focusedTab} = this;\n      if (!focusedTab) {\n        // If there is not already a tab focused, select the first or last tab\n        // based on the direction we're traveling.\n        indexToFocus = forwards ? 0 : tabs.length - 1;\n      } else {\n        const focusedIndex = this.tabs.indexOf(focusedTab);\n        indexToFocus = forwards ? focusedIndex + 1 : focusedIndex - 1;\n        if (indexToFocus >= tabs.length) {\n          // Return to start if moving past the last item.\n          indexToFocus = 0;\n        } else if (indexToFocus < 0) {\n          // Go to end if moving before the first item.\n          indexToFocus = tabs.length - 1;\n        }\n      }\n    }\n\n    const tabToFocus = tabs[indexToFocus];\n    tabToFocus.focus();\n    if (this.autoActivate) {\n      this.activateTab(tabToFocus);\n    } else {\n      this.updateFocusableTab(tabToFocus);\n    }\n  }\n\n  // scroll to item on keyup.\n  private handleKeyup() {\n    this.scrollToTab(this.focusedTab ?? this.activeTab);\n  }\n\n  private handleFocusout() {\n    // restore focus to selected item when blurring the tab bar.\n    if (this.matches(':focus-within')) {\n      return;\n    }\n\n    const {activeTab} = this;\n    if (activeTab) {\n      this.updateFocusableTab(activeTab);\n    }\n  }\n\n  private handleSlotChange() {\n    const firstTab = this.tabs[0];\n    if (!this.activeTab && firstTab) {\n      // If the active tab was removed, auto-select the first one. There should\n      // always be a selected tab while the bar has children.\n      this.activateTab(firstTab);\n    }\n\n    // When children shift, ensure the active tab is visible. For example, if\n    // many children are added before the active tab, it'd be pushed off screen.\n    // This ensures it stays visible.\n    this.scrollToTab(this.activeTab);\n  }\n}\n\nfunction isTab(element: unknown): element is Tab {\n  return element instanceof HTMLElement && element.hasAttribute('md-tab');\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n// Generated stylesheet for ./tabs/internal/tabs-styles.css.\nimport {css} from 'lit';\nexport const styles = css`:host{box-sizing:border-box;display:flex;flex-direction:column;overflow:auto;scroll-behavior:smooth;scrollbar-width:none;position:relative}:host([hidden]){display:none}:host::-webkit-scrollbar{display:none}.tabs{align-items:end;display:flex;height:100%;overflow:inherit;scroll-behavior:inherit;scrollbar-width:inherit;justify-content:space-between;width:100%}::slotted(*){flex:1}::slotted([active]){z-index:1}\n`;\n", "/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {Tabs} from './internal/tabs.js';\nimport {styles} from './internal/tabs-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-tabs': MdTabs;\n  }\n}\n\n// TODO(b/267336507): add docs\n/**\n * @summary Tabs displays a list of selectable tabs.\n *\n * @final\n * @suppress {visibility}\n */\n@customElement('md-tabs')\nexport class MdTabs extends Tabs {\n  static override styles: CSSResultOrNative[] = [styles];\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqCM,IAAO,OAAP,cAAoB,WAAU;;;;;;EAYlC,IAAI,YAAS;AACX,WAAO,KAAK,KAAK,KAAK,CAAC,QAAQ,IAAI,MAAM,KAAK;EAChD;EACA,IAAI,UAAU,KAAe;AAG3B,QAAI,KAAK;AACP,WAAK,YAAY,GAAG;IACtB;EACF;;;;;;EAQA,IAAI,iBAAc;AAChB,WAAO,KAAK,KAAK,UAAU,CAAC,QAAQ,IAAI,MAAM;EAChD;EACA,IAAI,eAAe,OAAa;AAC9B,UAAM,qBAAqB,MAAK;AAC9B,YAAM,MAAM,KAAK,KAAK,KAAK;AAE3B,UAAI,KAAK;AACP,aAAK,YAAY,GAAG;MACtB;IACF;AAEA,QAAI,CAAC,KAAK,aAAa;AAerB,WAAK,eAAe,KAAK,kBAAkB;AAC3C;IACF;AAEA,uBAAkB;EACpB;EAUA,IAAY,aAAU;AACpB,WAAO,KAAK,KAAK,KAAK,CAAC,QAAQ,IAAI,QAAQ,eAAe,CAAC;EAC7D;EAMA,cAAA;AACE,UAAK;AAdgD,SAAA,eAAe;AASrD,SAAA;IAEd,KAAqB,gBAAe;AAIrC,QAAI,CAAC,UAAU;AACb,WAAK,UAAU,OAAO;AACtB,WAAK,iBAAiB,WAAW,KAAK,cAAc,KAAK,IAAI,CAAC;AAC9D,WAAK,iBAAiB,SAAS,KAAK,YAAY,KAAK,IAAI,CAAC;AAC1D,WAAK,iBAAiB,YAAY,KAAK,eAAe,KAAK,IAAI,CAAC;IAClE;EACF;;;;;;;;;EAUA,MAAM,YAAY,eAA0B;AAC1C,UAAM,KAAK;AACX,UAAM,EAAC,KAAI,IAAI;AACf,sBAAkB,KAAK;AACvB,QACE,CAAC,iBACD,CAAC,KAAK,SAAS,aAAa,KAC5B,CAAC,KAAK,qBACN;AACA;IACF;AAGA,eAAW,OAAO,KAAK,MAAM;AAC3B,YAAM,IAAI;IACZ;AAEA,UAAM,SAAS,cAAc;AAC7B,UAAM,SAAS,cAAc;AAC7B,UAAM,SAAS,KAAK;AACpB,UAAM,aAAa,KAAK;AACxB,UAAM,eAAe;AACrB,UAAM,MAAM,SAAS;AACrB,UAAM,MAAM,SAAS,SAAS,aAAa;AAC3C,UAAM,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,MAAM,CAAC;AAK9C,UAAM,WAA2B,CAAC,KAAK,aAAa,YAAY;AAChE,SAAK,oBAAoB,SAAS,EAAC,UAAU,KAAK,GAAG,MAAM,GAAE,CAAC;EAChE;EAEmB,SAAM;AACvB,WAAO;;;wBAGa,KAAK,gBAAgB;mBAC1B,KAAK,cAAc;;;;EAIpC;EAEQ,MAAM,eAAe,OAAY;AACvC,UAAM,MAAM,MAAM;AAElB,UAAM;AACN,QAAI,MAAM,oBAAoB,CAAC,MAAM,GAAG,KAAK,IAAI,QAAQ;AACvD;IACF;AAEA,SAAK,YAAY,GAAG;EACtB;EAEQ,YAAY,WAAc;AAChC,UAAM,EAAC,KAAI,IAAI;AACf,UAAM,cAAc,KAAK;AACzB,QAAI,CAAC,KAAK,SAAS,SAAS,KAAK,gBAAgB,WAAW;AAE1D;IACF;AAEA,eAAW,OAAO,MAAM;AACtB,UAAI,SAAS,QAAQ;IACvB;AAEA,QAAI,aAAa;AAGf,YAAM,mBAAmB,CAAC,KAAK,cAC7B,IAAI,MAAM,UAAU,EAAC,SAAS,MAAM,YAAY,KAAI,CAAC,CAAC;AAExD,UAAI,kBAAkB;AACpB,mBAAW,OAAO,MAAM;AACtB,cAAI,SAAS,QAAQ;QACvB;AACA;MACF;AAEA,gBAAU,iBAAiB,EAAE,WAAW;IAC1C;AAEA,SAAK,mBAAmB,SAAS;AACjC,SAAK,YAAY,SAAS;EAC5B;EAEQ,mBAAmB,cAAiB;AAC1C,eAAW,OAAO,KAAK,MAAM;AAC3B,UAAI,WAAW,QAAQ,eAAe,IAAI;IAC5C;EACF;;EAGQ,MAAM,cAAc,OAAoB;AAE9C,UAAM;AACN,UAAM,SAAS,MAAM,QAAQ;AAC7B,UAAM,UAAU,MAAM,QAAQ;AAC9B,UAAM,SAAS,MAAM,QAAQ;AAC7B,UAAM,QAAQ,MAAM,QAAQ;AAE5B,QAAI,MAAM,oBAAqB,CAAC,UAAU,CAAC,WAAW,CAAC,UAAU,CAAC,OAAQ;AACxE;IACF;AAEA,UAAM,EAAC,KAAI,IAAI;AAEf,QAAI,KAAK,SAAS,GAAG;AACnB;IACF;AAGA,UAAM,eAAc;AAEpB,QAAI;AACJ,QAAI,UAAU,OAAO;AACnB,qBAAe,SAAS,IAAI,KAAK,SAAS;IAC5C,OAAO;AAEL,YAAM,QAAQ,iBAAiB,IAAI,EAAE,cAAc;AACnD,YAAM,WAAW,QAAQ,SAAS;AAClC,YAAM,EAAC,WAAU,IAAI;AACrB,UAAI,CAAC,YAAY;AAGf,uBAAe,WAAW,IAAI,KAAK,SAAS;MAC9C,OAAO;AACL,cAAM,eAAe,KAAK,KAAK,QAAQ,UAAU;AACjD,uBAAe,WAAW,eAAe,IAAI,eAAe;AAC5D,YAAI,gBAAgB,KAAK,QAAQ;AAE/B,yBAAe;QACjB,WAAW,eAAe,GAAG;AAE3B,yBAAe,KAAK,SAAS;QAC/B;MACF;IACF;AAEA,UAAM,aAAa,KAAK,YAAY;AACpC,eAAW,MAAK;AAChB,QAAI,KAAK,cAAc;AACrB,WAAK,YAAY,UAAU;IAC7B,OAAO;AACL,WAAK,mBAAmB,UAAU;IACpC;EACF;;EAGQ,cAAW;AACjB,SAAK,YAAY,KAAK,cAAc,KAAK,SAAS;EACpD;EAEQ,iBAAc;AAEpB,QAAI,KAAK,QAAQ,eAAe,GAAG;AACjC;IACF;AAEA,UAAM,EAAC,UAAS,IAAI;AACpB,QAAI,WAAW;AACb,WAAK,mBAAmB,SAAS;IACnC;EACF;EAEQ,mBAAgB;AACtB,UAAM,WAAW,KAAK,KAAK,CAAC;AAC5B,QAAI,CAAC,KAAK,aAAa,UAAU;AAG/B,WAAK,YAAY,QAAQ;IAC3B;AAKA,SAAK,YAAY,KAAK,SAAS;EACjC;;AA9QS,WAAA;EADR,sBAAsB,EAAC,SAAS,MAAM,UAAU,WAAU,CAAC;;AAyB5D,WAAA;EADC,SAAS,EAAC,MAAM,QAAQ,WAAW,mBAAkB,CAAC;;AAsCA,WAAA;EAAtD,SAAS,EAAC,MAAM,SAAS,WAAW,gBAAe,CAAC;;AAEpB,WAAA;EAAhC,MAAM,OAAO;;AACkB,WAAA;EAA/B,MAAM,MAAM;;AAiNf,SAAS,MAAM,SAAgB;AAC7B,SAAO,mBAAmB,eAAe,QAAQ,aAAa,QAAQ;AACxE;;;ACtTO,IAAM,SAAS;;;;ACmBf,IAAM,SAAN,MAAMA,gBAAe,KAAI;;AACd,OAAA,SAA8B,CAAC,MAAM;AAD1C,SAAM,WAAA;EADlB,cAAc,SAAS;GACX,MAAM;", "names": ["MdTabs"]}