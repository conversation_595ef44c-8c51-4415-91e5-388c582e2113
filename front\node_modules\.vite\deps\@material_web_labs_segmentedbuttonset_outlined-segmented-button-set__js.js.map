{"version": 3, "sources": ["../../@material/web/labs/segmentedbuttonset/internal/segmented-button-set.ts", "../../@material/web/labs/segmentedbuttonset/internal/outlined-segmented-button-set.ts", "../../@material/web/labs/segmentedbuttonset/internal/outlined-styles.ts", "../../@material/web/labs/segmentedbuttonset/internal/shared-styles.ts", "../../@material/web/labs/segmentedbuttonset/outlined-segmented-button-set.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {html, LitElement, nothing} from 'lit';\nimport {property, queryAssignedElements} from 'lit/decorators.js';\n\nimport {ARIAMixinStrict} from '../../../internal/aria/aria.js';\nimport {mixinDelegatesAria} from '../../../internal/aria/delegate.js';\nimport {SegmentedButton} from '../../segmentedbutton/internal/segmented-button.js';\n\n// Separate variable needed for closure.\nconst segmentedButtonSetBaseClass = mixinDelegatesAria(LitElement);\n\n/**\n * SegmentedButtonSet is the parent component for two or more\n * `SegmentedButton` components. **Only** `SegmentedButton` components may be\n * used as children.\n *\n * @fires segmented-button-set-selection {CustomEvent<{button: SegmentedButton, selected: boolean, index: number}>}\n * Dispatched when a button is selected programattically with the\n * `setButtonSelected` or the `toggleSelection` methods as well as on user\n * interaction. --bubbles --composed\n */\nexport class SegmentedButtonSet extends segmentedButtonSetBaseClass {\n  @property({type: Boolean}) multiselect = false;\n\n  @queryAssignedElements({flatten: true}) buttons!: SegmentedButton[];\n\n  getButtonDisabled(index: number): boolean {\n    if (this.indexOutOfBounds(index)) return false;\n    return this.buttons[index].disabled;\n  }\n\n  setButtonDisabled(index: number, disabled: boolean) {\n    if (this.indexOutOfBounds(index)) return;\n    this.buttons[index].disabled = disabled;\n  }\n\n  getButtonSelected(index: number): boolean {\n    if (this.indexOutOfBounds(index)) return false;\n    return this.buttons[index].selected;\n  }\n\n  setButtonSelected(index: number, selected: boolean) {\n    // Ignore out-of-index values.\n    if (this.indexOutOfBounds(index)) return;\n    // Ignore disabled buttons.\n    if (this.getButtonDisabled(index)) return;\n\n    if (this.multiselect) {\n      this.buttons[index].selected = selected;\n      this.emitSelectionEvent(index);\n      return;\n    }\n\n    // Single-select segmented buttons are not unselectable.\n    if (!selected) return;\n\n    this.buttons[index].selected = true;\n    this.emitSelectionEvent(index);\n    // Deselect all other buttons for single-select.\n    for (let i = 0; i < this.buttons.length; i++) {\n      if (i === index) continue;\n      this.buttons[i].selected = false;\n    }\n  }\n\n  private handleSegmentedButtonInteraction(event: CustomEvent) {\n    const index = this.buttons.indexOf(event.target as SegmentedButton);\n    this.toggleSelection(index);\n  }\n\n  private toggleSelection(index: number) {\n    if (this.indexOutOfBounds(index)) return;\n    this.setButtonSelected(index, !this.buttons[index].selected);\n  }\n\n  private indexOutOfBounds(index: number): boolean {\n    return index < 0 || index >= this.buttons.length;\n  }\n\n  private emitSelectionEvent(index: number) {\n    this.dispatchEvent(\n      new CustomEvent('segmented-button-set-selection', {\n        detail: {\n          button: this.buttons[index],\n          selected: this.buttons[index].selected,\n          index,\n        },\n        bubbles: true,\n        composed: true,\n      }),\n    );\n  }\n\n  protected override render() {\n    // Needed for closure conformance\n    const {ariaLabel} = this as ARIAMixinStrict;\n    return html`\n      <span\n        role=\"group\"\n        @segmented-button-interaction=\"${this.handleSegmentedButtonInteraction}\"\n        aria-label=${ariaLabel || nothing}\n        class=\"md3-segmented-button-set\">\n        <slot></slot>\n      </span>\n    `;\n  }\n\n  protected getRenderClasses() {\n    return {};\n  }\n}\n", "/**\n * @license\n * Copyright 2022 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {SegmentedButtonSet} from './segmented-button-set.js';\n\n/**\n * b/265346443 - add docs\n */\nexport class OutlinedSegmentedButtonSet extends SegmentedButtonSet {\n  protected override getRenderClasses() {\n    return {\n      ...super.getRenderClasses(),\n      'md3-segmented-button-set--outlined': true,\n    };\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n// Generated stylesheet for ./labs/segmentedbuttonset/internal/outlined-styles.css.\nimport {css} from 'lit';\nexport const styles = css`:host{--_container-height: var(--md-outlined-segmented-button-container-height, 40px);--_disabled-icon-color: var(--md-outlined-segmented-button-disabled-icon-color, var(--md-sys-color-on-surface, #1d1b20));--_disabled-label-text-color: var(--md-outlined-segmented-button-disabled-label-text-color, var(--md-sys-color-on-surface, #1d1b20));--_disabled-outline-color: var(--md-outlined-segmented-button-disabled-outline-color, var(--md-sys-color-on-surface, #1d1b20));--_hover-state-layer-opacity: var(--md-outlined-segmented-button-hover-state-layer-opacity, 0.08);--_label-text-font: var(--md-outlined-segmented-button-label-text-font, var(--md-sys-typescale-label-large-font, var(--md-ref-typeface-plain, Roboto)));--_label-text-line-height: var(--md-outlined-segmented-button-label-text-line-height, var(--md-sys-typescale-label-large-line-height, 1.25rem));--_label-text-size: var(--md-outlined-segmented-button-label-text-size, var(--md-sys-typescale-label-large-size, 0.875rem));--_label-text-weight: var(--md-outlined-segmented-button-label-text-weight, var(--md-sys-typescale-label-large-weight, var(--md-ref-typeface-weight-medium, 500)));--_outline-color: var(--md-outlined-segmented-button-outline-color, var(--md-sys-color-outline, #79747e));--_pressed-state-layer-opacity: var(--md-outlined-segmented-button-pressed-state-layer-opacity, 0.12);--_selected-container-color: var(--md-outlined-segmented-button-selected-container-color, var(--md-sys-color-secondary-container, #e8def8));--_selected-focus-icon-color: var(--md-outlined-segmented-button-selected-focus-icon-color, var(--md-sys-color-on-secondary-container, #1d192b));--_selected-focus-label-text-color: var(--md-outlined-segmented-button-selected-focus-label-text-color, var(--md-sys-color-on-secondary-container, #1d192b));--_selected-hover-icon-color: var(--md-outlined-segmented-button-selected-hover-icon-color, var(--md-sys-color-on-secondary-container, #1d192b));--_selected-hover-label-text-color: var(--md-outlined-segmented-button-selected-hover-label-text-color, var(--md-sys-color-on-secondary-container, #1d192b));--_selected-hover-state-layer-color: var(--md-outlined-segmented-button-selected-hover-state-layer-color, var(--md-sys-color-on-secondary-container, #1d192b));--_selected-label-text-color: var(--md-outlined-segmented-button-selected-label-text-color, var(--md-sys-color-on-secondary-container, #1d192b));--_selected-pressed-icon-color: var(--md-outlined-segmented-button-selected-pressed-icon-color, var(--md-sys-color-on-secondary-container, #1d192b));--_selected-pressed-label-text-color: var(--md-outlined-segmented-button-selected-pressed-label-text-color, var(--md-sys-color-on-secondary-container, #1d192b));--_selected-pressed-state-layer-color: var(--md-outlined-segmented-button-selected-pressed-state-layer-color, var(--md-sys-color-on-secondary-container, #1d192b));--_unselected-focus-icon-color: var(--md-outlined-segmented-button-unselected-focus-icon-color, var(--md-sys-color-on-surface, #1d1b20));--_unselected-focus-label-text-color: var(--md-outlined-segmented-button-unselected-focus-label-text-color, var(--md-sys-color-on-surface, #1d1b20));--_unselected-hover-icon-color: var(--md-outlined-segmented-button-unselected-hover-icon-color, var(--md-sys-color-on-surface, #1d1b20));--_unselected-hover-label-text-color: var(--md-outlined-segmented-button-unselected-hover-label-text-color, var(--md-sys-color-on-surface, #1d1b20));--_unselected-hover-state-layer-color: var(--md-outlined-segmented-button-unselected-hover-state-layer-color, var(--md-sys-color-on-surface, #1d1b20));--_unselected-label-text-color: var(--md-outlined-segmented-button-unselected-label-text-color, var(--md-sys-color-on-surface, #1d1b20));--_unselected-pressed-icon-color: var(--md-outlined-segmented-button-unselected-pressed-icon-color, var(--md-sys-color-on-surface, #1d1b20));--_unselected-pressed-label-text-color: var(--md-outlined-segmented-button-unselected-pressed-label-text-color, var(--md-sys-color-on-surface, #1d1b20));--_unselected-pressed-state-layer-color: var(--md-outlined-segmented-button-unselected-pressed-state-layer-color, var(--md-sys-color-on-surface, #1d1b20));--_icon-size: var(--md-outlined-segmented-button-icon-size, 18px);--_selected-icon-color: var(--md-outlined-segmented-button-selected-icon-color, var(--md-sys-color-on-secondary-container, #1d192b));--_unselected-icon-color: var(--md-outlined-segmented-button-unselected-icon-color, var(--md-sys-color-on-surface, #1d1b20));--_shape-start-start: var(--md-outlined-segmented-button-shape-start-start, var(--md-outlined-segmented-button-shape, var(--md-sys-shape-corner-full, 9999px)));--_shape-start-end: var(--md-outlined-segmented-button-shape-start-end, var(--md-outlined-segmented-button-shape, var(--md-sys-shape-corner-full, 9999px)));--_shape-end-end: var(--md-outlined-segmented-button-shape-end-end, var(--md-outlined-segmented-button-shape, var(--md-sys-shape-corner-full, 9999px)));--_shape-end-start: var(--md-outlined-segmented-button-shape-end-start, var(--md-outlined-segmented-button-shape, var(--md-sys-shape-corner-full, 9999px)))}\n`;\n", "/**\n * @license\n * Copyright 2024 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n// Generated stylesheet for ./labs/segmentedbuttonset/internal/shared-styles.css.\nimport {css} from 'lit';\nexport const styles = css`:host{display:flex;outline:none}.md3-segmented-button-set{display:grid;grid-auto-columns:1fr;grid-auto-flow:column;grid-auto-rows:auto;width:100%;height:var(--_container-height)}.md3-segmented-button-set ::slotted(:first-child){border-start-start-radius:var(--_shape-start-start);border-end-start-radius:var(--_shape-end-start)}.md3-segmented-button-set ::slotted(:last-child){border-start-end-radius:var(--_shape-start-end);border-end-end-radius:var(--_shape-end-end)}\n`;\n", "/**\n * @license\n * Copyright 2022 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {OutlinedSegmentedButtonSet} from './internal/outlined-segmented-button-set.js';\nimport {styles as outlinedStyles} from './internal/outlined-styles.js';\nimport {styles as sharedStyles} from './internal/shared-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-outlined-segmented-button-set': MdOutlinedSegmentedButtonSet;\n  }\n}\n\n/**\n * MdOutlinedSegmentedButtonSet is the custom element for the Material\n * Design outlined segmented button set component.\n * @final\n * @suppress {visibility}\n */\n@customElement('md-outlined-segmented-button-set')\nexport class MdOutlinedSegmentedButtonSet extends OutlinedSegmentedButtonSet {\n  static override styles: CSSResultOrNative[] = [sharedStyles, outlinedStyles];\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAcA,IAAM,8BAA8B,mBAAmB,UAAU;AAY3D,IAAO,qBAAP,cAAkC,4BAA2B;EAAnE,cAAA;;AAC6B,SAAA,cAAc;EAwF3C;EApFE,kBAAkB,OAAa;AAC7B,QAAI,KAAK,iBAAiB,KAAK;AAAG,aAAO;AACzC,WAAO,KAAK,QAAQ,KAAK,EAAE;EAC7B;EAEA,kBAAkB,OAAe,UAAiB;AAChD,QAAI,KAAK,iBAAiB,KAAK;AAAG;AAClC,SAAK,QAAQ,KAAK,EAAE,WAAW;EACjC;EAEA,kBAAkB,OAAa;AAC7B,QAAI,KAAK,iBAAiB,KAAK;AAAG,aAAO;AACzC,WAAO,KAAK,QAAQ,KAAK,EAAE;EAC7B;EAEA,kBAAkB,OAAe,UAAiB;AAEhD,QAAI,KAAK,iBAAiB,KAAK;AAAG;AAElC,QAAI,KAAK,kBAAkB,KAAK;AAAG;AAEnC,QAAI,KAAK,aAAa;AACpB,WAAK,QAAQ,KAAK,EAAE,WAAW;AAC/B,WAAK,mBAAmB,KAAK;AAC7B;IACF;AAGA,QAAI,CAAC;AAAU;AAEf,SAAK,QAAQ,KAAK,EAAE,WAAW;AAC/B,SAAK,mBAAmB,KAAK;AAE7B,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,QAAQ,KAAK;AAC5C,UAAI,MAAM;AAAO;AACjB,WAAK,QAAQ,CAAC,EAAE,WAAW;IAC7B;EACF;EAEQ,iCAAiC,OAAkB;AACzD,UAAM,QAAQ,KAAK,QAAQ,QAAQ,MAAM,MAAyB;AAClE,SAAK,gBAAgB,KAAK;EAC5B;EAEQ,gBAAgB,OAAa;AACnC,QAAI,KAAK,iBAAiB,KAAK;AAAG;AAClC,SAAK,kBAAkB,OAAO,CAAC,KAAK,QAAQ,KAAK,EAAE,QAAQ;EAC7D;EAEQ,iBAAiB,OAAa;AACpC,WAAO,QAAQ,KAAK,SAAS,KAAK,QAAQ;EAC5C;EAEQ,mBAAmB,OAAa;AACtC,SAAK,cACH,IAAI,YAAY,kCAAkC;MAChD,QAAQ;QACN,QAAQ,KAAK,QAAQ,KAAK;QAC1B,UAAU,KAAK,QAAQ,KAAK,EAAE;QAC9B;;MAEF,SAAS;MACT,UAAU;KACX,CAAC;EAEN;EAEmB,SAAM;AAEvB,UAAM,EAAC,UAAS,IAAI;AACpB,WAAO;;;yCAG8B,KAAK,gCAAgC;qBACzD,aAAa,OAAO;;;;;EAKvC;EAEU,mBAAgB;AACxB,WAAO,CAAA;EACT;;AAvF2B,WAAA;EAA1B,SAAS,EAAC,MAAM,QAAO,CAAC;;AAEe,WAAA;EAAvC,sBAAsB,EAAC,SAAS,KAAI,CAAC;;;;AClBlC,IAAO,6BAAP,cAA0C,mBAAkB;EAC7C,mBAAgB;AACjC,WAAO;MACL,GAAG,MAAM,iBAAgB;MACzB,sCAAsC;;EAE1C;;;;ACVK,IAAM,SAAS;;;;ACAf,IAAMA,UAAS;;;;ACmBf,IAAM,+BAAN,MAAMC,sCAAqC,2BAA0B;;AAC1D,6BAAA,SAA8B,CAACC,SAAc,MAAc;AADhE,+BAA4B,WAAA;EADxC,cAAc,kCAAkC;GACpC,4BAA4B;", "names": ["styles", "MdOutlinedSegmentedButtonSet", "styles"]}