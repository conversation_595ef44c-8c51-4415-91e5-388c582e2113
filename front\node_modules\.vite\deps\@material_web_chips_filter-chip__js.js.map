{"version": 3, "sources": ["../../@material/web/chips/internal/filter-chip.ts", "../../@material/web/chips/internal/filter-styles.ts", "../../@material/web/chips/filter-chip.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport '../../elevation/elevation.js';\n\nimport {html, nothing} from 'lit';\nimport {property, query} from 'lit/decorators.js';\n\nimport {ARIAMixinStrict} from '../../internal/aria/aria.js';\nimport {redispatchEvent} from '../../internal/events/redispatch-event.js';\n\nimport {MultiActionChip} from './multi-action-chip.js';\nimport {renderRemoveButton} from './trailing-icons.js';\n\n/**\n * A filter chip component.\n *\n * @fires remove {Event} Dispatched when the remove button is clicked.\n */\nexport class FilterChip extends MultiActionChip {\n  @property({type: Boolean}) elevated = false;\n  @property({type: Boolean}) removable = false;\n  @property({type: Boolean, reflect: true}) selected = false;\n\n  /**\n   * Only needed for SSR.\n   *\n   * Add this attribute when a filter chip has a `slot=\"selected-icon\"` to avoid\n   * a Flash Of Unstyled Content.\n   */\n  @property({type: <PERSON><PERSON><PERSON>, reflect: true, attribute: 'has-selected-icon'})\n  hasSelectedIcon = false;\n\n  protected get primaryId() {\n    return 'button';\n  }\n\n  @query('.primary.action')\n  protected readonly primaryAction!: HTMLElement | null;\n  @query('.trailing.action')\n  protected readonly trailingAction!: HTMLElement | null;\n\n  protected override getContainerClasses() {\n    return {\n      ...super.getContainerClasses(),\n      elevated: this.elevated,\n      selected: this.selected,\n      'has-trailing': this.removable,\n      'has-icon': this.hasIcon || this.selected,\n    };\n  }\n\n  protected override renderPrimaryAction(content: unknown) {\n    const {ariaLabel} = this as ARIAMixinStrict;\n    return html`\n      <button\n        class=\"primary action\"\n        id=\"button\"\n        aria-label=${ariaLabel || nothing}\n        aria-pressed=${this.selected}\n        aria-disabled=${this.softDisabled || nothing}\n        ?disabled=${this.disabled && !this.alwaysFocusable}\n        @click=${this.handleClickOnChild}\n        >${content}</button\n      >\n    `;\n  }\n\n  protected override renderLeadingIcon() {\n    if (!this.selected) {\n      return super.renderLeadingIcon();\n    }\n\n    return html`\n      <slot name=\"selected-icon\">\n        <svg class=\"checkmark\" viewBox=\"0 0 18 18\" aria-hidden=\"true\">\n          <path\n            d=\"M6.75012 12.1274L3.62262 8.99988L2.55762 10.0574L6.75012 14.2499L15.7501 5.24988L14.6926 4.19238L6.75012 12.1274Z\" />\n        </svg>\n      </slot>\n    `;\n  }\n\n  protected override renderTrailingAction(focusListener: EventListener) {\n    if (this.removable) {\n      return renderRemoveButton({\n        focusListener,\n        ariaLabel: this.ariaLabelRemove,\n        disabled: this.disabled || this.softDisabled,\n      });\n    }\n\n    return nothing;\n  }\n\n  protected override renderOutline() {\n    if (this.elevated) {\n      return html`<md-elevation part=\"elevation\"></md-elevation>`;\n    }\n\n    return super.renderOutline();\n  }\n\n  private handleClickOnChild(event: MouseEvent) {\n    if (this.disabled || this.softDisabled) {\n      return;\n    }\n\n    // Store prevValue to revert in case `chip.selected` is changed during an\n    // event listener.\n    const prevValue = this.selected;\n    this.selected = !this.selected;\n\n    const preventDefault = !redispatchEvent(this, event);\n    if (preventDefault) {\n      // We should not do `this.selected = !this.selected`, since a client\n      // click listener could change its value. Instead, always revert to the\n      // original value.\n      this.selected = prevValue;\n      return;\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n// Generated stylesheet for ./chips/internal/filter-styles.css.\nimport {css} from 'lit';\nexport const styles = css`:host{--_container-height: var(--md-filter-chip-container-height, 32px);--_disabled-label-text-color: var(--md-filter-chip-disabled-label-text-color, var(--md-sys-color-on-surface, #1d1b20));--_disabled-label-text-opacity: var(--md-filter-chip-disabled-label-text-opacity, 0.38);--_elevated-container-elevation: var(--md-filter-chip-elevated-container-elevation, 1);--_elevated-container-shadow-color: var(--md-filter-chip-elevated-container-shadow-color, var(--md-sys-color-shadow, #000));--_elevated-disabled-container-color: var(--md-filter-chip-elevated-disabled-container-color, var(--md-sys-color-on-surface, #1d1b20));--_elevated-disabled-container-elevation: var(--md-filter-chip-elevated-disabled-container-elevation, 0);--_elevated-disabled-container-opacity: var(--md-filter-chip-elevated-disabled-container-opacity, 0.12);--_elevated-focus-container-elevation: var(--md-filter-chip-elevated-focus-container-elevation, 1);--_elevated-hover-container-elevation: var(--md-filter-chip-elevated-hover-container-elevation, 2);--_elevated-pressed-container-elevation: var(--md-filter-chip-elevated-pressed-container-elevation, 1);--_elevated-selected-container-color: var(--md-filter-chip-elevated-selected-container-color, var(--md-sys-color-secondary-container, #e8def8));--_label-text-font: var(--md-filter-chip-label-text-font, var(--md-sys-typescale-label-large-font, var(--md-ref-typeface-plain, Roboto)));--_label-text-line-height: var(--md-filter-chip-label-text-line-height, var(--md-sys-typescale-label-large-line-height, 1.25rem));--_label-text-size: var(--md-filter-chip-label-text-size, var(--md-sys-typescale-label-large-size, 0.875rem));--_label-text-weight: var(--md-filter-chip-label-text-weight, var(--md-sys-typescale-label-large-weight, var(--md-ref-typeface-weight-medium, 500)));--_selected-focus-label-text-color: var(--md-filter-chip-selected-focus-label-text-color, var(--md-sys-color-on-secondary-container, #1d192b));--_selected-hover-label-text-color: var(--md-filter-chip-selected-hover-label-text-color, var(--md-sys-color-on-secondary-container, #1d192b));--_selected-hover-state-layer-color: var(--md-filter-chip-selected-hover-state-layer-color, var(--md-sys-color-on-secondary-container, #1d192b));--_selected-hover-state-layer-opacity: var(--md-filter-chip-selected-hover-state-layer-opacity, 0.08);--_selected-label-text-color: var(--md-filter-chip-selected-label-text-color, var(--md-sys-color-on-secondary-container, #1d192b));--_selected-pressed-label-text-color: var(--md-filter-chip-selected-pressed-label-text-color, var(--md-sys-color-on-secondary-container, #1d192b));--_selected-pressed-state-layer-color: var(--md-filter-chip-selected-pressed-state-layer-color, var(--md-sys-color-on-surface-variant, #49454f));--_selected-pressed-state-layer-opacity: var(--md-filter-chip-selected-pressed-state-layer-opacity, 0.12);--_elevated-container-color: var(--md-filter-chip-elevated-container-color, var(--md-sys-color-surface-container-low, #f7f2fa));--_disabled-outline-color: var(--md-filter-chip-disabled-outline-color, var(--md-sys-color-on-surface, #1d1b20));--_disabled-outline-opacity: var(--md-filter-chip-disabled-outline-opacity, 0.12);--_disabled-selected-container-color: var(--md-filter-chip-disabled-selected-container-color, var(--md-sys-color-on-surface, #1d1b20));--_disabled-selected-container-opacity: var(--md-filter-chip-disabled-selected-container-opacity, 0.12);--_focus-outline-color: var(--md-filter-chip-focus-outline-color, var(--md-sys-color-on-surface-variant, #49454f));--_outline-color: var(--md-filter-chip-outline-color, var(--md-sys-color-outline, #79747e));--_outline-width: var(--md-filter-chip-outline-width, 1px);--_selected-container-color: var(--md-filter-chip-selected-container-color, var(--md-sys-color-secondary-container, #e8def8));--_selected-outline-width: var(--md-filter-chip-selected-outline-width, 0px);--_focus-label-text-color: var(--md-filter-chip-focus-label-text-color, var(--md-sys-color-on-surface-variant, #49454f));--_hover-label-text-color: var(--md-filter-chip-hover-label-text-color, var(--md-sys-color-on-surface-variant, #49454f));--_hover-state-layer-color: var(--md-filter-chip-hover-state-layer-color, var(--md-sys-color-on-surface-variant, #49454f));--_hover-state-layer-opacity: var(--md-filter-chip-hover-state-layer-opacity, 0.08);--_label-text-color: var(--md-filter-chip-label-text-color, var(--md-sys-color-on-surface-variant, #49454f));--_pressed-label-text-color: var(--md-filter-chip-pressed-label-text-color, var(--md-sys-color-on-surface-variant, #49454f));--_pressed-state-layer-color: var(--md-filter-chip-pressed-state-layer-color, var(--md-sys-color-on-secondary-container, #1d192b));--_pressed-state-layer-opacity: var(--md-filter-chip-pressed-state-layer-opacity, 0.12);--_icon-size: var(--md-filter-chip-icon-size, 18px);--_disabled-leading-icon-color: var(--md-filter-chip-disabled-leading-icon-color, var(--md-sys-color-on-surface, #1d1b20));--_disabled-leading-icon-opacity: var(--md-filter-chip-disabled-leading-icon-opacity, 0.38);--_selected-focus-leading-icon-color: var(--md-filter-chip-selected-focus-leading-icon-color, var(--md-sys-color-on-secondary-container, #1d192b));--_selected-hover-leading-icon-color: var(--md-filter-chip-selected-hover-leading-icon-color, var(--md-sys-color-on-secondary-container, #1d192b));--_selected-leading-icon-color: var(--md-filter-chip-selected-leading-icon-color, var(--md-sys-color-on-secondary-container, #1d192b));--_selected-pressed-leading-icon-color: var(--md-filter-chip-selected-pressed-leading-icon-color, var(--md-sys-color-on-secondary-container, #1d192b));--_focus-leading-icon-color: var(--md-filter-chip-focus-leading-icon-color, var(--md-sys-color-primary, #6750a4));--_hover-leading-icon-color: var(--md-filter-chip-hover-leading-icon-color, var(--md-sys-color-primary, #6750a4));--_leading-icon-color: var(--md-filter-chip-leading-icon-color, var(--md-sys-color-primary, #6750a4));--_pressed-leading-icon-color: var(--md-filter-chip-pressed-leading-icon-color, var(--md-sys-color-primary, #6750a4));--_disabled-trailing-icon-color: var(--md-filter-chip-disabled-trailing-icon-color, var(--md-sys-color-on-surface, #1d1b20));--_disabled-trailing-icon-opacity: var(--md-filter-chip-disabled-trailing-icon-opacity, 0.38);--_selected-focus-trailing-icon-color: var(--md-filter-chip-selected-focus-trailing-icon-color, var(--md-sys-color-on-secondary-container, #1d192b));--_selected-hover-trailing-icon-color: var(--md-filter-chip-selected-hover-trailing-icon-color, var(--md-sys-color-on-secondary-container, #1d192b));--_selected-pressed-trailing-icon-color: var(--md-filter-chip-selected-pressed-trailing-icon-color, var(--md-sys-color-on-secondary-container, #1d192b));--_selected-trailing-icon-color: var(--md-filter-chip-selected-trailing-icon-color, var(--md-sys-color-on-secondary-container, #1d192b));--_focus-trailing-icon-color: var(--md-filter-chip-focus-trailing-icon-color, var(--md-sys-color-on-surface-variant, #49454f));--_hover-trailing-icon-color: var(--md-filter-chip-hover-trailing-icon-color, var(--md-sys-color-on-surface-variant, #49454f));--_pressed-trailing-icon-color: var(--md-filter-chip-pressed-trailing-icon-color, var(--md-sys-color-on-surface-variant, #49454f));--_trailing-icon-color: var(--md-filter-chip-trailing-icon-color, var(--md-sys-color-on-surface-variant, #49454f));--_container-shape-start-start: var(--md-filter-chip-container-shape-start-start, var(--md-filter-chip-container-shape, var(--md-sys-shape-corner-small, 8px)));--_container-shape-start-end: var(--md-filter-chip-container-shape-start-end, var(--md-filter-chip-container-shape, var(--md-sys-shape-corner-small, 8px)));--_container-shape-end-end: var(--md-filter-chip-container-shape-end-end, var(--md-filter-chip-container-shape, var(--md-sys-shape-corner-small, 8px)));--_container-shape-end-start: var(--md-filter-chip-container-shape-end-start, var(--md-filter-chip-container-shape, var(--md-sys-shape-corner-small, 8px)));--_leading-space: var(--md-filter-chip-leading-space, 16px);--_trailing-space: var(--md-filter-chip-trailing-space, 16px);--_icon-label-space: var(--md-filter-chip-icon-label-space, 8px);--_with-leading-icon-leading-space: var(--md-filter-chip-with-leading-icon-leading-space, 8px);--_with-trailing-icon-trailing-space: var(--md-filter-chip-with-trailing-icon-trailing-space, 8px)}.selected.elevated::before{background:var(--_elevated-selected-container-color)}.checkmark{height:var(--_icon-size);width:var(--_icon-size)}.disabled .checkmark{opacity:var(--_disabled-leading-icon-opacity)}@media(forced-colors: active){.disabled .checkmark{opacity:1}}\n`;\n", "/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {styles as elevatedStyles} from './internal/elevated-styles.js';\nimport {FilterChip} from './internal/filter-chip.js';\nimport {styles} from './internal/filter-styles.js';\nimport {styles as selectableStyles} from './internal/selectable-styles.js';\nimport {styles as sharedStyles} from './internal/shared-styles.js';\nimport {styles as trailingIconStyles} from './internal/trailing-icon-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-filter-chip': MdFilterChip;\n  }\n}\n\n/**\n * TODO(b/243982145): add docs\n *\n * @final\n * @suppress {visibility}\n */\n@customElement('md-filter-chip')\nexport class MdFilterChip extends FilterChip {\n  static override styles: CSSResultOrNative[] = [\n    sharedStyles,\n    elevatedStyles,\n    trailingIconStyles,\n    selectableStyles,\n    styles,\n  ];\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsBM,IAAO,aAAP,cAA0B,gBAAe;EAA/C,cAAA;;AAC6B,SAAA,WAAW;AACX,SAAA,YAAY;AACG,SAAA,WAAW;AASrD,SAAA,kBAAkB;EA2FpB;EAzFE,IAAc,YAAS;AACrB,WAAO;EACT;EAOmB,sBAAmB;AACpC,WAAO;MACL,GAAG,MAAM,oBAAmB;MAC5B,UAAU,KAAK;MACf,UAAU,KAAK;MACf,gBAAgB,KAAK;MACrB,YAAY,KAAK,WAAW,KAAK;;EAErC;EAEmB,oBAAoB,SAAgB;AACrD,UAAM,EAAC,UAAS,IAAI;AACpB,WAAO;;;;qBAIU,aAAa,OAAO;uBAClB,KAAK,QAAQ;wBACZ,KAAK,gBAAgB,OAAO;oBAChC,KAAK,YAAY,CAAC,KAAK,eAAe;iBACzC,KAAK,kBAAkB;WAC7B,OAAO;;;EAGhB;EAEmB,oBAAiB;AAClC,QAAI,CAAC,KAAK,UAAU;AAClB,aAAO,MAAM,kBAAiB;IAChC;AAEA,WAAO;;;;;;;;EAQT;EAEmB,qBAAqB,eAA4B;AAClE,QAAI,KAAK,WAAW;AAClB,aAAO,mBAAmB;QACxB;QACA,WAAW,KAAK;QAChB,UAAU,KAAK,YAAY,KAAK;OACjC;IACH;AAEA,WAAO;EACT;EAEmB,gBAAa;AAC9B,QAAI,KAAK,UAAU;AACjB,aAAO;IACT;AAEA,WAAO,MAAM,cAAa;EAC5B;EAEQ,mBAAmB,OAAiB;AAC1C,QAAI,KAAK,YAAY,KAAK,cAAc;AACtC;IACF;AAIA,UAAM,YAAY,KAAK;AACvB,SAAK,WAAW,CAAC,KAAK;AAEtB,UAAM,iBAAiB,CAAC,gBAAgB,MAAM,KAAK;AACnD,QAAI,gBAAgB;AAIlB,WAAK,WAAW;AAChB;IACF;EACF;;AArG2B,WAAA;EAA1B,SAAS,EAAC,MAAM,QAAO,CAAC;;AACE,WAAA;EAA1B,SAAS,EAAC,MAAM,QAAO,CAAC;;AACiB,WAAA;EAAzC,SAAS,EAAC,MAAM,SAAS,SAAS,KAAI,CAAC;;AASxC,WAAA;EADC,SAAS,EAAC,MAAM,SAAS,SAAS,MAAM,WAAW,oBAAmB,CAAC;;AAQrD,WAAA;EADlB,MAAM,iBAAiB;;AAGL,WAAA;EADlB,MAAM,kBAAkB;;;;ACnCpB,IAAMA,UAAS;;;;ACsBf,IAAM,eAAN,MAAMC,sBAAqB,WAAU;;AAC1B,aAAA,SAA8B;EAC5CC;EACA;EACAA;EACAA;EACAA;;AANS,eAAY,WAAA;EADxB,cAAc,gBAAgB;GAClB,YAAY;", "names": ["styles", "MdFilterChip", "styles"]}