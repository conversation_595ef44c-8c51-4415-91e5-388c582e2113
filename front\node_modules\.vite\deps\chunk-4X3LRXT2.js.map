{"version": 3, "sources": ["../../@material/web/internal/events/redispatch-event.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\n/**\n * Re-dispatches an event from the provided element.\n *\n * This function is useful for forwarding non-composed events, such as `change`\n * events.\n *\n * @example\n * class MyInput extends LitElement {\n *   render() {\n *     return html`<input @change=${this.redispatchEvent}>`;\n *   }\n *\n *   protected redispatchEvent(event: Event) {\n *     redispatchEvent(this, event);\n *   }\n * }\n *\n * @param element The element to dispatch the event from.\n * @param event The event to re-dispatch.\n * @return Whether or not the event was dispatched (if cancelable).\n */\nexport function redispatchEvent(element: Element, event: Event) {\n  // For bubbling events in SSR light DOM (or composed), stop their propagation\n  // and dispatch the copy.\n  if (event.bubbles && (!element.shadowRoot || event.composed)) {\n    event.stopPropagation();\n  }\n\n  const copy = Reflect.construct(event.constructor, [event.type, event]);\n  const dispatched = element.dispatchEvent(copy);\n  if (!dispatched) {\n    event.preventDefault();\n  }\n\n  return dispatched;\n}\n"], "mappings": ";AA2BM,SAAU,gBAAgB,SAAkB,OAAY;AAG5D,MAAI,MAAM,YAAY,CAAC,QAAQ,cAAc,MAAM,WAAW;AAC5D,UAAM,gBAAe;EACvB;AAEA,QAAM,OAAO,QAAQ,UAAU,MAAM,aAAa,CAAC,MAAM,MAAM,KAAK,CAAC;AACrE,QAAM,aAAa,QAAQ,cAAc,IAAI;AAC7C,MAAI,CAAC,YAAY;AACf,UAAM,eAAc;EACtB;AAEA,SAAO;AACT;", "names": []}