{"version": 3, "sources": ["../../@material/web/fab/internal/shared.ts", "../../@material/web/fab/internal/fab.ts", "../../@material/web/fab/internal/forced-colors-styles.ts", "../../@material/web/fab/internal/shared-styles.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport '../../elevation/elevation.js';\nimport '../../focus/md-focus-ring.js';\nimport '../../ripple/ripple.js';\n\nimport {html, LitElement, nothing} from 'lit';\nimport {property} from 'lit/decorators.js';\nimport {classMap} from 'lit/directives/class-map.js';\n\nimport {ARIAMixinStrict} from '../../internal/aria/aria.js';\nimport {mixinDelegatesAria} from '../../internal/aria/delegate.js';\n\n/**\n * Sizes variants available to non-extended FABs.\n */\nexport type FabSize = 'medium' | 'small' | 'large';\n\n// Separate variable needed for closure.\nconst fabBaseClass = mixinDelegatesAria(LitElement);\n\n// tslint:disable-next-line:enforce-comments-on-exported-symbols\nexport abstract class SharedFab extends fabBaseClass {\n  /** @nocollapse */\n  static override shadowRootOptions: ShadowRootInit = {\n    mode: 'open' as const,\n    delegatesFocus: true,\n  };\n\n  /**\n   * The size of the FAB.\n   *\n   * NOTE: Branded FABs cannot be sized to `small`, and Extended FABs do not\n   * have different sizes.\n   */\n  @property({reflect: true}) size: FabSize = 'medium';\n\n  /**\n   * The text to display on the FAB.\n   */\n  @property() label = '';\n\n  /**\n   * Lowers the FAB's elevation.\n   */\n  @property({type: Boolean}) lowered = false;\n\n  protected override render() {\n    // Needed for closure conformance\n    const {ariaLabel} = this as ARIAMixinStrict;\n    return html`\n      <button\n        class=\"fab ${classMap(this.getRenderClasses())}\"\n        aria-label=${ariaLabel || nothing}>\n        <md-elevation part=\"elevation\"></md-elevation>\n        <md-focus-ring part=\"focus-ring\"></md-focus-ring>\n        <md-ripple class=\"ripple\"></md-ripple>\n        ${this.renderTouchTarget()} ${this.renderIcon()} ${this.renderLabel()}\n      </button>\n    `;\n  }\n\n  protected getRenderClasses() {\n    const isExtended = !!this.label;\n    return {\n      'lowered': this.lowered,\n      'small': this.size === 'small' && !isExtended,\n      'large': this.size === 'large' && !isExtended,\n      'extended': isExtended,\n    };\n  }\n\n  private renderTouchTarget() {\n    return html`<div class=\"touch-target\"></div>`;\n  }\n\n  private renderLabel() {\n    return this.label ? html`<span class=\"label\">${this.label}</span>` : '';\n  }\n\n  private renderIcon() {\n    const {ariaLabel} = this as ARIAMixinStrict;\n    return html`<span class=\"icon\">\n      <slot\n        name=\"icon\"\n        aria-hidden=${ariaLabel || this.label\n          ? 'true'\n          : (nothing as unknown as 'false')}>\n        <span></span>\n      </slot>\n    </span>`;\n  }\n}\n", "/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {property} from 'lit/decorators.js';\n\nimport {SharedFab} from './shared.js';\n\n/**\n * The variants available to non-branded FABs.\n */\nexport type FabVariant = 'surface' | 'primary' | 'secondary' | 'tertiary';\n\n// tslint:disable-next-line:enforce-comments-on-exported-symbols\nexport class Fab extends SharedFab {\n  /**\n   * The FAB color variant to render.\n   */\n  @property() variant: FabVariant = 'surface';\n\n  protected override getRenderClasses() {\n    return {\n      ...super.getRenderClasses(),\n      'primary': this.variant === 'primary',\n      'secondary': this.variant === 'secondary',\n      'tertiary': this.variant === 'tertiary',\n    };\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n// Generated stylesheet for ./fab/internal/forced-colors-styles.css.\nimport {css} from 'lit';\nexport const styles = css`@media(forced-colors: active){.fab{border:1px solid ButtonText}.fab.extended{padding-inline-start:15px;padding-inline-end:19px}md-focus-ring{--md-focus-ring-outward-offset: 3px}}\n`;\n", "/**\n * @license\n * Copyright 2024 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n// Generated stylesheet for ./fab/internal/shared-styles.css.\nimport {css} from 'lit';\nexport const styles = css`:host{--md-ripple-hover-opacity: var(--_hover-state-layer-opacity);--md-ripple-pressed-opacity: var(--_pressed-state-layer-opacity);display:inline-flex;-webkit-tap-highlight-color:rgba(0,0,0,0)}:host([size=medium][touch-target=wrapper]){margin:max(0px,48px - var(--_container-height))}:host([size=large][touch-target=wrapper]){margin:max(0px,48px - var(--_large-container-height))}.fab,.icon,.icon ::slotted(*){display:flex}.fab{align-items:center;justify-content:center;vertical-align:middle;padding:0;position:relative;height:var(--_container-height);transition-property:background-color;border-width:0px;outline:none;z-index:0;text-transform:inherit;--md-elevation-level: var(--_container-elevation);--md-elevation-shadow-color: var(--_container-shadow-color);background-color:var(--_container-color);--md-ripple-hover-color: var(--_hover-state-layer-color);--md-ripple-pressed-color: var(--_pressed-state-layer-color)}.fab.extended{width:inherit;box-sizing:border-box;padding-inline-start:16px;padding-inline-end:20px}.fab:not(.extended){width:var(--_container-width)}.fab.large{width:var(--_large-container-width);height:var(--_large-container-height)}.fab.large .icon ::slotted(*){width:var(--_large-icon-size);height:var(--_large-icon-size);font-size:var(--_large-icon-size)}.fab.large,.fab.large .ripple{border-start-start-radius:var(--_large-container-shape-start-start);border-start-end-radius:var(--_large-container-shape-start-end);border-end-start-radius:var(--_large-container-shape-end-start);border-end-end-radius:var(--_large-container-shape-end-end)}.fab.large md-focus-ring{--md-focus-ring-shape-start-start: var(--_large-container-shape-start-start);--md-focus-ring-shape-start-end: var(--_large-container-shape-start-end);--md-focus-ring-shape-end-end: var(--_large-container-shape-end-end);--md-focus-ring-shape-end-start: var(--_large-container-shape-end-start)}.fab:focus{--md-elevation-level: var(--_focus-container-elevation)}.fab:hover{--md-elevation-level: var(--_hover-container-elevation)}.fab:active{--md-elevation-level: var(--_pressed-container-elevation)}.fab.lowered{background-color:var(--_lowered-container-color);--md-elevation-level: var(--_lowered-container-elevation)}.fab.lowered:focus{--md-elevation-level: var(--_lowered-focus-container-elevation)}.fab.lowered:hover{--md-elevation-level: var(--_lowered-hover-container-elevation)}.fab.lowered:active{--md-elevation-level: var(--_lowered-pressed-container-elevation)}.fab .label{color:var(--_label-text-color)}.fab:hover .fab .label{color:var(--_hover-label-text-color)}.fab:focus .fab .label{color:var(--_focus-label-text-color)}.fab:active .fab .label{color:var(--_pressed-label-text-color)}.label{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;font-family:var(--_label-text-font);font-size:var(--_label-text-size);line-height:var(--_label-text-line-height);font-weight:var(--_label-text-weight)}.fab.extended .icon ::slotted(*){margin-inline-end:12px}.ripple{overflow:hidden}.ripple,md-elevation{z-index:-1}.touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%)}:host([touch-target=none]) .touch-target{display:none}md-elevation,.fab{transition-duration:280ms;transition-timing-function:cubic-bezier(0.2, 0, 0, 1)}.fab,.ripple{border-start-start-radius:var(--_container-shape-start-start);border-start-end-radius:var(--_container-shape-start-end);border-end-start-radius:var(--_container-shape-end-start);border-end-end-radius:var(--_container-shape-end-end)}md-focus-ring{--md-focus-ring-shape-start-start: var(--_container-shape-start-start);--md-focus-ring-shape-start-end: var(--_container-shape-start-end);--md-focus-ring-shape-end-end: var(--_container-shape-end-end);--md-focus-ring-shape-end-start: var(--_container-shape-end-start)}.icon ::slotted(*){width:var(--_icon-size);height:var(--_icon-size);font-size:var(--_icon-size)}\n`;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAuBA,IAAM,eAAe,mBAAmB,UAAU;AAG5C,IAAgB,YAAhB,cAAkC,aAAY;EAApD,cAAA;;AAa6B,SAAA,OAAgB;AAK/B,SAAA,QAAQ;AAKO,SAAA,UAAU;EA+CvC;EA7CqB,SAAM;AAEvB,UAAM,EAAC,UAAS,IAAI;AACpB,WAAO;;qBAEU,SAAS,KAAK,iBAAgB,CAAE,CAAC;qBACjC,aAAa,OAAO;;;;UAI/B,KAAK,kBAAiB,CAAE,IAAI,KAAK,WAAU,CAAE,IAAI,KAAK,YAAW,CAAE;;;EAG3E;EAEU,mBAAgB;AACxB,UAAM,aAAa,CAAC,CAAC,KAAK;AAC1B,WAAO;MACL,WAAW,KAAK;MAChB,SAAS,KAAK,SAAS,WAAW,CAAC;MACnC,SAAS,KAAK,SAAS,WAAW,CAAC;MACnC,YAAY;;EAEhB;EAEQ,oBAAiB;AACvB,WAAO;EACT;EAEQ,cAAW;AACjB,WAAO,KAAK,QAAQ,2BAA2B,KAAK,KAAK,YAAY;EACvE;EAEQ,aAAU;AAChB,UAAM,EAAC,UAAS,IAAI;AACpB,WAAO;;;sBAGW,aAAa,KAAK,QAC5B,SACC,OAA8B;;;;EAIzC;;AAnEgB,UAAA,oBAAoC;EAClD,MAAM;EACN,gBAAgB;;AASS,WAAA;EAA1B,SAAS,EAAC,SAAS,KAAI,CAAC;;AAKb,WAAA;EAAX,SAAQ;;AAKkB,WAAA;EAA1B,SAAS,EAAC,MAAM,QAAO,CAAC;;;;ACjCrB,IAAO,MAAP,cAAmB,UAAS;EAAlC,cAAA;;AAIc,SAAA,UAAsB;EAUpC;EARqB,mBAAgB;AACjC,WAAO;MACL,GAAG,MAAM,iBAAgB;MACzB,WAAW,KAAK,YAAY;MAC5B,aAAa,KAAK,YAAY;MAC9B,YAAY,KAAK,YAAY;;EAEjC;;AATY,WAAA;EAAX,SAAQ;;;;ACbJ,IAAM,SAAS;;;;ACAf,IAAMA,UAAS;;", "names": ["styles"]}