{"version": 3, "sources": ["../../@material/web/labs/card/internal/elevated-styles.ts", "../../@material/web/labs/card/elevated-card.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2024 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n// Generated stylesheet for ./labs/card/internal/elevated-styles.css.\nimport {css} from 'lit';\nexport const styles = css`:host{--_container-color: var(--md-elevated-card-container-color, var(--md-sys-color-surface-container-low, #f7f2fa));--_container-elevation: var(--md-elevated-card-container-elevation, 1);--_container-shadow-color: var(--md-elevated-card-container-shadow-color, var(--md-sys-color-shadow, #000));--_container-shape: var(--md-elevated-card-container-shape, var(--md-sys-shape-corner-medium, 12px))}\n`;\n", "/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {Card} from './internal/card.js';\nimport {styles as elevatedStyles} from './internal/elevated-styles.js';\nimport {styles as sharedStyles} from './internal/shared-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-elevated-card': MdElevatedCard;\n  }\n}\n\n/**\n * @final\n * @suppress {visibility}\n */\n@customElement('md-elevated-card')\nexport class MdElevatedCard extends Card {\n  static override styles: CSSResultOrNative[] = [sharedStyles, elevatedStyles];\n}\n"], "mappings": ";;;;;;;;;;;;;;;AAOO,IAAMA,UAAS;;;;ACiBf,IAAM,iBAAN,MAAMC,wBAAuB,KAAI;;AACtB,eAAA,SAA8B,CAAC,QAAcC,OAAc;AADhE,iBAAc,WAAA;EAD1B,cAAc,kBAAkB;GACpB,cAAc;", "names": ["styles", "MdElevatedCard", "styles"]}