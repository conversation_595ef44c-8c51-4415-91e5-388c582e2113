import {
  Corner
} from "./chunk-T4JRGFZZ.js";
import {
  createDeactivateItemsEvent,
  createRequestActivationEvent,
  deactivateActiveItem,
  getFirstActivatableItem
} from "./chunk-SHO7BEJJ.js";
import {
  CloseReason,
  KeydownClose<PERSON>ey,
  <PERSON>vi<PERSON><PERSON>ey,
  <PERSON><PERSON><PERSON>,
  createActivateTypeaheadEvent,
  createDeactivateTypeaheadEvent
} from "./chunk-DMRCI6IZ.js";
import {
  __decorate,
  customElement,
  property,
  queryAssignedElements
} from "./chunk-PZNDE6JX.js";
import {
  LitElement,
  css,
  html,
  isServer
} from "./chunk-4GZ3EDRH.js";

// node_modules/@material/web/menu/internal/submenu/sub-menu.js
var SubMenu = class extends LitElement {
  get item() {
    return this.items[0] ?? null;
  }
  get menu() {
    return this.menus[0] ?? null;
  }
  constructor() {
    super();
    this.anchorCorner = Corner.START_END;
    this.menuCorner = Corner.START_START;
    this.hoverOpenDelay = 400;
    this.hoverCloseDelay = 400;
    this.isSubMenu = true;
    this.previousOpenTimeout = 0;
    this.previousCloseTimeout = 0;
    this.onMouseenter = () => {
      clearTimeout(this.previousOpenTimeout);
      clearTimeout(this.previousCloseTimeout);
      if (this.menu?.open)
        return;
      if (!this.hoverOpenDelay) {
        this.show();
      } else {
        this.previousOpenTimeout = setTimeout(() => {
          this.show();
        }, this.hoverOpenDelay);
      }
    };
    this.onMouseleave = () => {
      clearTimeout(this.previousCloseTimeout);
      clearTimeout(this.previousOpenTimeout);
      if (!this.hoverCloseDelay) {
        this.close();
      } else {
        this.previousCloseTimeout = setTimeout(() => {
          this.close();
        }, this.hoverCloseDelay);
      }
    };
    if (!isServer) {
      this.addEventListener("mouseenter", this.onMouseenter);
      this.addEventListener("mouseleave", this.onMouseleave);
    }
  }
  render() {
    return html`
      <slot
        name="item"
        @click=${this.onClick}
        @keydown=${this.onKeydown}
        @slotchange=${this.onSlotchange}>
      </slot>
      <slot
        name="menu"
        @keydown=${this.onSubMenuKeydown}
        @close-menu=${this.onCloseSubmenu}
        @slotchange=${this.onSlotchange}>
      </slot>
    `;
  }
  firstUpdated() {
    this.onSlotchange();
  }
  /**
   * Shows the submenu.
   */
  async show() {
    const menu = this.menu;
    if (!menu || menu.open)
      return;
    menu.addEventListener("closed", () => {
      this.item.ariaExpanded = "false";
      this.dispatchEvent(createActivateTypeaheadEvent());
      this.dispatchEvent(createDeactivateItemsEvent());
      menu.ariaHidden = "true";
    }, { once: true });
    if (menu.positioning === "document") {
      menu.positioning = "absolute";
    }
    menu.quick = true;
    menu.hasOverflow = true;
    menu.anchorCorner = this.anchorCorner;
    menu.menuCorner = this.menuCorner;
    menu.anchorElement = this.item;
    menu.defaultFocus = "first-item";
    menu.removeAttribute("aria-hidden");
    menu.skipRestoreFocus = false;
    const menuAlreadyOpen = menu.open;
    menu.show();
    this.item.ariaExpanded = "true";
    this.item.ariaHasPopup = "menu";
    if (menu.id) {
      this.item.setAttribute("aria-controls", menu.id);
    }
    this.dispatchEvent(createDeactivateItemsEvent());
    this.dispatchEvent(createDeactivateTypeaheadEvent());
    this.item.selected = true;
    if (!menuAlreadyOpen) {
      let open = (value) => {
      };
      const opened = new Promise((resolve) => {
        open = resolve;
      });
      menu.addEventListener("opened", open, { once: true });
      await opened;
    }
  }
  /**
   * Closes the submenu.
   */
  async close() {
    const menu = this.menu;
    if (!menu || !menu.open)
      return;
    this.dispatchEvent(createActivateTypeaheadEvent());
    menu.quick = true;
    menu.close();
    this.dispatchEvent(createDeactivateItemsEvent());
    let close = (value) => {
    };
    const closed = new Promise((resolve) => {
      close = resolve;
    });
    menu.addEventListener("closed", close, { once: true });
    await closed;
  }
  onSlotchange() {
    if (!this.item) {
      return;
    }
    this.item.ariaExpanded = "false";
    this.item.ariaHasPopup = "menu";
    if (this.menu?.id) {
      this.item.setAttribute("aria-controls", this.menu.id);
    }
    this.item.keepOpen = true;
    const menu = this.menu;
    if (!menu)
      return;
    menu.isSubmenu = true;
    menu.ariaHidden = "true";
  }
  onClick() {
    this.show();
  }
  /**
   * On item keydown handles opening the submenu.
   */
  async onKeydown(event) {
    const shouldOpenSubmenu = this.isSubmenuOpenKey(event.code);
    if (event.defaultPrevented)
      return;
    const openedWithLR = shouldOpenSubmenu && (NavigableKey.LEFT === event.code || NavigableKey.RIGHT === event.code);
    if (event.code === SelectionKey.SPACE || openedWithLR) {
      event.preventDefault();
      if (openedWithLR) {
        event.stopPropagation();
      }
    }
    if (!shouldOpenSubmenu) {
      return;
    }
    const submenu = this.menu;
    if (!submenu)
      return;
    const submenuItems = submenu.items;
    const firstActivatableItem = getFirstActivatableItem(submenuItems);
    if (firstActivatableItem) {
      await this.show();
      firstActivatableItem.tabIndex = 0;
      firstActivatableItem.focus();
      return;
    }
  }
  onCloseSubmenu(event) {
    const { itemPath, reason } = event.detail;
    itemPath.push(this.item);
    this.dispatchEvent(createActivateTypeaheadEvent());
    if (reason.kind === CloseReason.KEYDOWN && reason.key === KeydownCloseKey.ESCAPE) {
      event.stopPropagation();
      this.item.dispatchEvent(createRequestActivationEvent());
      return;
    }
    this.dispatchEvent(createDeactivateItemsEvent());
  }
  async onSubMenuKeydown(event) {
    if (event.defaultPrevented)
      return;
    const { close: shouldClose, keyCode } = this.isSubmenuCloseKey(event.code);
    if (!shouldClose)
      return;
    event.preventDefault();
    if (keyCode === NavigableKey.LEFT || keyCode === NavigableKey.RIGHT) {
      event.stopPropagation();
    }
    await this.close();
    deactivateActiveItem(this.menu.items);
    this.item?.focus();
    this.item.tabIndex = 0;
    this.item.focus();
  }
  /**
   * Determines whether the given KeyboardEvent code is one that should open
   * the submenu. This is RTL-aware. By default, left, right, space, or enter.
   *
   * @param code The native KeyboardEvent code.
   * @return Whether or not the key code should open the submenu.
   */
  isSubmenuOpenKey(code) {
    const isRtl = getComputedStyle(this).direction === "rtl";
    const arrowEnterKey = isRtl ? NavigableKey.LEFT : NavigableKey.RIGHT;
    switch (code) {
      case arrowEnterKey:
      case SelectionKey.SPACE:
      case SelectionKey.ENTER:
        return true;
      default:
        return false;
    }
  }
  /**
   * Determines whether the given KeyboardEvent code is one that should close
   * the submenu. This is RTL-aware. By default right, left, or escape.
   *
   * @param code The native KeyboardEvent code.
   * @return Whether or not the key code should close the submenu.
   */
  isSubmenuCloseKey(code) {
    const isRtl = getComputedStyle(this).direction === "rtl";
    const arrowEnterKey = isRtl ? NavigableKey.RIGHT : NavigableKey.LEFT;
    switch (code) {
      case arrowEnterKey:
      case KeydownCloseKey.ESCAPE:
        return { close: true, keyCode: code };
      default:
        return { close: false };
    }
  }
};
__decorate([
  property({ attribute: "anchor-corner" })
], SubMenu.prototype, "anchorCorner", void 0);
__decorate([
  property({ attribute: "menu-corner" })
], SubMenu.prototype, "menuCorner", void 0);
__decorate([
  property({ type: Number, attribute: "hover-open-delay" })
], SubMenu.prototype, "hoverOpenDelay", void 0);
__decorate([
  property({ type: Number, attribute: "hover-close-delay" })
], SubMenu.prototype, "hoverCloseDelay", void 0);
__decorate([
  property({ type: Boolean, reflect: true, attribute: "md-sub-menu" })
], SubMenu.prototype, "isSubMenu", void 0);
__decorate([
  queryAssignedElements({ slot: "item", flatten: true })
], SubMenu.prototype, "items", void 0);
__decorate([
  queryAssignedElements({ slot: "menu", flatten: true })
], SubMenu.prototype, "menus", void 0);

// node_modules/@material/web/menu/internal/submenu/sub-menu-styles.js
var styles = css`:host{position:relative;display:flex;flex-direction:column}
`;

// node_modules/@material/web/menu/sub-menu.js
var MdSubMenu = class MdSubMenu2 extends SubMenu {
};
MdSubMenu.styles = [styles];
MdSubMenu = __decorate([
  customElement("md-sub-menu")
], MdSubMenu);

export {
  MdSubMenu
};
/*! Bundled license information:

@material/web/menu/internal/submenu/sub-menu.js:
@material/web/menu/sub-menu.js:
  (**
   * @license
   * Copyright 2023 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   *)

@material/web/menu/internal/submenu/sub-menu-styles.js:
  (**
   * @license
   * Copyright 2024 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   *)
*/
//# sourceMappingURL=chunk-FHSRQKDO.js.map
