{"version": 3, "sources": ["../../@material/web/labs/navigationtab/internal/navigation-tab.ts", "../../@material/web/labs/navigationtab/internal/navigation-tab-styles.ts", "../../@material/web/labs/navigationtab/navigation-tab.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2022 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport '../../../focus/md-focus-ring.js';\nimport '../../../ripple/ripple.js';\nimport '../../badge/badge.js';\n\nimport {html, LitElement, nothing, PropertyValues} from 'lit';\nimport {property, query} from 'lit/decorators.js';\nimport {classMap} from 'lit/directives/class-map.js';\n\nimport {ARIAMixinStrict} from '../../../internal/aria/aria.js';\nimport {mixinDelegatesAria} from '../../../internal/aria/delegate.js';\n\nimport {NavigationTabState} from './state.js';\n\n// Separate variable needed for closure.\nconst navigationTabBaseClass = mixinDelegatesAria(LitElement);\n\n/**\n * b/265346501 - add docs\n *\n * @fires navigation-tab-rendered {Event} Dispatched when the navigation tab's\n * DOM has rendered and custom element definition has loaded. --bubbles\n * --composed\n * @fires navigation-tab-interaction {CustomEvent<{state: MdNavigationTab}>}\n * Dispatched when the navigation tab has been clicked. --bubbles --composed\n */\nexport class NavigationTab\n  extends navigationTabBaseClass\n  implements NavigationTabState\n{\n  @property({type: Boolean}) disabled = false;\n  @property({type: Boolean, reflect: true}) active = false;\n  @property({type: Boolean, attribute: 'hide-inactive-label'})\n  hideInactiveLabel = false;\n  @property() label?: string;\n  @property({attribute: 'badge-value'}) badgeValue = '';\n  @property({type: Boolean, attribute: 'show-badge'}) showBadge = false;\n\n  @query('button') buttonElement!: HTMLElement | null;\n\n  protected override render() {\n    // Needed for closure conformance\n    const {ariaLabel} = this as ARIAMixinStrict;\n    return html` <button\n      class=\"md3-navigation-tab ${classMap(this.getRenderClasses())}\"\n      role=\"tab\"\n      aria-selected=\"${this.active}\"\n      aria-label=${ariaLabel || nothing}\n      tabindex=\"${this.active ? 0 : -1}\"\n      @click=\"${this.handleClick}\">\n      <md-focus-ring part=\"focus-ring\" inward></md-focus-ring>\n      <md-ripple\n        ?disabled=\"${this.disabled}\"\n        class=\"md3-navigation-tab__ripple\"></md-ripple>\n      <span aria-hidden=\"true\" class=\"md3-navigation-tab__icon-content\"\n        ><span class=\"md3-navigation-tab__active-indicator\"></span\n        ><span class=\"md3-navigation-tab__icon\"\n          ><slot name=\"inactive-icon\"></slot\n        ></span>\n        <span class=\"md3-navigation-tab__icon md3-navigation-tab__icon--active\"\n          ><slot name=\"active-icon\"></slot></span\n        >${this.renderBadge()}</span\n      >${this.renderLabel()}\n    </button>`;\n  }\n\n  private getRenderClasses() {\n    return {\n      'md3-navigation-tab--hide-inactive-label': this.hideInactiveLabel,\n      'md3-navigation-tab--active': this.active,\n    };\n  }\n\n  private renderBadge() {\n    return this.showBadge\n      ? html`<md-badge .value=\"${this.badgeValue}\"></md-badge>`\n      : nothing;\n  }\n\n  private renderLabel() {\n    // Needed for closure conformance\n    const {ariaLabel} = this as ARIAMixinStrict;\n    const ariaHidden = ariaLabel ? 'true' : 'false';\n    return !this.label\n      ? nothing\n      : html` <span\n          aria-hidden=\"${ariaHidden}\"\n          class=\"md3-navigation-tab__label-text\"\n          >${this.label}</span\n        >`;\n  }\n\n  override firstUpdated(changedProperties: PropertyValues) {\n    super.firstUpdated(changedProperties);\n    const event = new Event('navigation-tab-rendered', {\n      bubbles: true,\n      composed: true,\n    });\n    this.dispatchEvent(event);\n  }\n\n  override focus() {\n    const buttonElement = this.buttonElement;\n    if (buttonElement) {\n      buttonElement.focus();\n    }\n  }\n\n  override blur() {\n    const buttonElement = this.buttonElement;\n    if (buttonElement) {\n      buttonElement.blur();\n    }\n  }\n\n  handleClick() {\n    // b/269772145 - connect to ripple\n    this.dispatchEvent(\n      new CustomEvent('navigation-tab-interaction', {\n        detail: {state: this},\n        bubbles: true,\n        composed: true,\n      }),\n    );\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n// Generated stylesheet for ./labs/navigationtab/internal/navigation-tab-styles.css.\nimport {css} from 'lit';\nexport const styles = css`:host{--_active-indicator-color: var(--md-navigation-bar-active-indicator-color, var(--md-sys-color-secondary-container, #e8def8));--_active-indicator-height: var(--md-navigation-bar-active-indicator-height, 32px);--_active-indicator-shape: var(--md-navigation-bar-active-indicator-shape, var(--md-sys-shape-corner-full, 9999px));--_active-indicator-width: var(--md-navigation-bar-active-indicator-width, 64px);--_active-focus-icon-color: var(--md-navigation-bar-active-focus-icon-color, var(--md-sys-color-on-secondary-container, #1d192b));--_active-focus-label-text-color: var(--md-navigation-bar-active-focus-label-text-color, var(--md-sys-color-on-surface, #1d1b20));--_active-focus-state-layer-color: var(--md-navigation-bar-active-focus-state-layer-color, var(--md-sys-color-on-surface, #1d1b20));--_active-hover-icon-color: var(--md-navigation-bar-active-hover-icon-color, var(--md-sys-color-on-secondary-container, #1d192b));--_active-hover-label-text-color: var(--md-navigation-bar-active-hover-label-text-color, var(--md-sys-color-on-surface, #1d1b20));--_active-hover-state-layer-color: var(--md-navigation-bar-active-hover-state-layer-color, var(--md-sys-color-on-surface, #1d1b20));--_active-icon-color: var(--md-navigation-bar-active-icon-color, var(--md-sys-color-on-secondary-container, #1d192b));--_active-label-text-color: var(--md-navigation-bar-active-label-text-color, var(--md-sys-color-on-surface, #1d1b20));--_active-label-text-weight: var(--md-navigation-bar-active-label-text-weight, var(--md-sys-typescale-label-medium-weight-prominent, var(--md-ref-typeface-weight-bold, 700)));--_active-pressed-icon-color: var(--md-navigation-bar-active-pressed-icon-color, var(--md-sys-color-on-secondary-container, #1d192b));--_active-pressed-label-text-color: var(--md-navigation-bar-active-pressed-label-text-color, var(--md-sys-color-on-surface, #1d1b20));--_active-pressed-state-layer-color: var(--md-navigation-bar-active-pressed-state-layer-color, var(--md-sys-color-on-surface, #1d1b20));--_container-color: var(--md-navigation-bar-container-color, var(--md-sys-color-surface-container, #f3edf7));--_container-elevation: var(--md-navigation-bar-container-elevation, 2);--_container-height: var(--md-navigation-bar-container-height, 80px);--_container-shape: var(--md-navigation-bar-container-shape, var(--md-sys-shape-corner-none, 0px));--_focus-state-layer-opacity: var(--md-navigation-bar-focus-state-layer-opacity, 0.12);--_hover-state-layer-opacity: var(--md-navigation-bar-hover-state-layer-opacity, 0.08);--_icon-size: var(--md-navigation-bar-icon-size, 24px);--_inactive-focus-icon-color: var(--md-navigation-bar-inactive-focus-icon-color, var(--md-sys-color-on-surface, #1d1b20));--_inactive-focus-label-text-color: var(--md-navigation-bar-inactive-focus-label-text-color, var(--md-sys-color-on-surface, #1d1b20));--_inactive-focus-state-layer-color: var(--md-navigation-bar-inactive-focus-state-layer-color, var(--md-sys-color-on-surface, #1d1b20));--_inactive-hover-icon-color: var(--md-navigation-bar-inactive-hover-icon-color, var(--md-sys-color-on-surface, #1d1b20));--_inactive-hover-label-text-color: var(--md-navigation-bar-inactive-hover-label-text-color, var(--md-sys-color-on-surface, #1d1b20));--_inactive-hover-state-layer-color: var(--md-navigation-bar-inactive-hover-state-layer-color, var(--md-sys-color-on-surface, #1d1b20));--_inactive-icon-color: var(--md-navigation-bar-inactive-icon-color, var(--md-sys-color-on-surface-variant, #49454f));--_inactive-label-text-color: var(--md-navigation-bar-inactive-label-text-color, var(--md-sys-color-on-surface-variant, #49454f));--_inactive-pressed-icon-color: var(--md-navigation-bar-inactive-pressed-icon-color, var(--md-sys-color-on-surface, #1d1b20));--_inactive-pressed-label-text-color: var(--md-navigation-bar-inactive-pressed-label-text-color, var(--md-sys-color-on-surface, #1d1b20));--_inactive-pressed-state-layer-color: var(--md-navigation-bar-inactive-pressed-state-layer-color, var(--md-sys-color-on-surface, #1d1b20));--_label-text-font: var(--md-navigation-bar-label-text-font, var(--md-sys-typescale-label-medium-font, var(--md-ref-typeface-plain, Roboto)));--_label-text-line-height: var(--md-navigation-bar-label-text-line-height, var(--md-sys-typescale-label-medium-line-height, 1rem));--_label-text-size: var(--md-navigation-bar-label-text-size, var(--md-sys-typescale-label-medium-size, 0.75rem));--_label-text-tracking: var(--md-navigation-bar-label-text-tracking, );--_label-text-type: var(--md-navigation-bar-label-text-type, var(--md-sys-typescale-label-medium-weight, var(--md-ref-typeface-weight-medium, 500)) var(--md-sys-typescale-label-medium-size, 0.75rem) / var(--md-sys-typescale-label-medium-line-height, 1rem) var(--md-sys-typescale-label-medium-font, var(--md-ref-typeface-plain, Roboto)));--_label-text-weight: var(--md-navigation-bar-label-text-weight, var(--md-sys-typescale-label-medium-weight, var(--md-ref-typeface-weight-medium, 500)));--_pressed-state-layer-opacity: var(--md-navigation-bar-pressed-state-layer-opacity, 0.12);display:flex;flex-grow:1}md-focus-ring{--md-focus-ring-shape: var(--md-sys-shape-corner-small, 8px);--md-focus-ring-inward-offset: -1px}.md3-navigation-tab{align-items:center;appearance:none;background:none;border:none;box-sizing:border-box;cursor:pointer;display:flex;flex-direction:column;height:100%;justify-content:center;min-height:48px;min-width:48px;outline:none;padding:8px 0px 12px;position:relative;text-align:center;width:100%;font-family:var(--_label-text-font);font-size:var(--_label-text-size);line-height:var(--_label-text-line-height);font-weight:var(--_label-text-weight);text-transform:inherit}.md3-navigation-tab::-moz-focus-inner{border:0;padding:0}.md3-navigation-tab__icon-content{align-items:center;box-sizing:border-box;display:flex;justify-content:center;position:relative;z-index:1}.md3-navigation-tab__label-text{height:16px;margin-top:4px;opacity:1;transition:opacity 100ms cubic-bezier(0.4, 0, 0.2, 1),height 100ms cubic-bezier(0.4, 0, 0.2, 1);z-index:1}.md3-navigation-tab--hide-inactive-label:not(.md3-navigation-tab--active) .md3-navigation-tab__label-text{height:0;opacity:0}.md3-navigation-tab__active-indicator{display:flex;justify-content:center;opacity:0;position:absolute;transition:width 100ms cubic-bezier(0.4, 0, 0.2, 1),opacity 100ms cubic-bezier(0.4, 0, 0.2, 1);width:32px;background-color:var(--_active-indicator-color);border-radius:var(--_active-indicator-shape)}.md3-navigation-tab--active .md3-navigation-tab__active-indicator{opacity:1}.md3-navigation-tab__active-indicator,.md3-navigation-tab__icon-content{height:var(--_active-indicator-height)}.md3-navigation-tab--active .md3-navigation-tab__active-indicator,.md3-navigation-tab__icon-content{width:var(--_active-indicator-width)}.md3-navigation-tab__icon{fill:currentColor;align-self:center;display:inline-block;position:relative;width:var(--_icon-size);height:var(--_icon-size);font-size:var(--_icon-size)}.md3-navigation-tab__icon.md3-navigation-tab__icon--active{display:none}.md3-navigation-tab--active .md3-navigation-tab__icon{display:none}.md3-navigation-tab--active .md3-navigation-tab__icon.md3-navigation-tab__icon--active{display:inline-block}.md3-navigation-tab__ripple{z-index:0}.md3-navigation-tab--active{--md-ripple-hover-color: var(--_active-hover-state-layer-color);--md-ripple-pressed-color: var(--_active-pressed-state-layer-color);--md-ripple-hover-opacity: var(--_hover-state-layer-opacity);--md-ripple-pressed-opacity: var(--_pressed-state-layer-opacity)}.md3-navigation-tab--active .md3-navigation-tab__icon{color:var(--_active-icon-color)}.md3-navigation-tab--active .md3-navigation-tab__label-text{color:var(--_active-label-text-color)}.md3-navigation-tab--active:hover .md3-navigation-tab__icon{color:var(--_active-hover-icon-color)}.md3-navigation-tab--active:hover .md3-navigation-tab__label-text{color:var(--_active-hover-label-text-color)}.md3-navigation-tab--active:focus .md3-navigation-tab__icon{color:var(--_active-focus-icon-color)}.md3-navigation-tab--active:focus .md3-navigation-tab__label-text{color:var(--_active-focus-label-text-color)}.md3-navigation-tab--active:active .md3-navigation-tab__icon{color:var(--_active-pressed-icon-color)}.md3-navigation-tab--active:active .md3-navigation-tab__label-text{color:var(--_active-pressed-label-text-color)}.md3-navigation-tab:not(.md3-navigation-tab--active){--md-ripple-hover-color: var(--_inactive-hover-state-layer-color);--md-ripple-pressed-color: var(--_inactive-pressed-state-layer-color);--md-ripple-hover-opacity: var(--_hover-state-layer-opacity);--md-ripple-pressed-opacity: var(--_pressed-state-layer-opacity)}.md3-navigation-tab:not(.md3-navigation-tab--active) .md3-navigation-tab__icon{color:var(--_inactive-icon-color)}.md3-navigation-tab:not(.md3-navigation-tab--active) .md3-navigation-tab__label-text{color:var(--_inactive-label-text-color)}.md3-navigation-tab:not(.md3-navigation-tab--active):hover .md3-navigation-tab__icon{color:var(--_inactive-hover-icon-color)}.md3-navigation-tab:not(.md3-navigation-tab--active):hover .md3-navigation-tab__label-text{color:var(--_inactive-hover-label-text-color)}.md3-navigation-tab:not(.md3-navigation-tab--active):focus .md3-navigation-tab__icon{color:var(--_inactive-focus-icon-color)}.md3-navigation-tab:not(.md3-navigation-tab--active):focus .md3-navigation-tab__label-text{color:var(--_inactive-focus-label-text-color)}.md3-navigation-tab:not(.md3-navigation-tab--active):active .md3-navigation-tab__icon{color:var(--_inactive-pressed-icon-color)}.md3-navigation-tab:not(.md3-navigation-tab--active):active .md3-navigation-tab__label-text{color:var(--_inactive-pressed-label-text-color)}\n`;\n", "/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {NavigationTab} from './internal/navigation-tab.js';\nimport {styles} from './internal/navigation-tab-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-navigation-tab': MdNavigationTab;\n  }\n}\n\n/**\n * @final\n * @suppress {visibility}\n */\n@customElement('md-navigation-tab')\nexport class MdNavigationTab extends NavigationTab {\n  static override styles: CSSResultOrNative[] = [styles];\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAoBA,IAAM,yBAAyB,mBAAmB,UAAU;AAWtD,IAAO,gBAAP,cACI,uBAAsB;EADhC,cAAA;;AAI6B,SAAA,WAAW;AACI,SAAA,SAAS;AAEnD,SAAA,oBAAoB;AAEkB,SAAA,aAAa;AACC,SAAA,YAAY;EAyFlE;EArFqB,SAAM;AAEvB,UAAM,EAAC,UAAS,IAAI;AACpB,WAAO;kCACuB,SAAS,KAAK,iBAAgB,CAAE,CAAC;;uBAE5C,KAAK,MAAM;mBACf,aAAa,OAAO;kBACrB,KAAK,SAAS,IAAI,EAAE;gBACtB,KAAK,WAAW;;;qBAGX,KAAK,QAAQ;;;;;;;;;WASvB,KAAK,YAAW,CAAE;SACpB,KAAK,YAAW,CAAE;;EAEzB;EAEQ,mBAAgB;AACtB,WAAO;MACL,2CAA2C,KAAK;MAChD,8BAA8B,KAAK;;EAEvC;EAEQ,cAAW;AACjB,WAAO,KAAK,YACR,yBAAyB,KAAK,UAAU,kBACxC;EACN;EAEQ,cAAW;AAEjB,UAAM,EAAC,UAAS,IAAI;AACpB,UAAM,aAAa,YAAY,SAAS;AACxC,WAAO,CAAC,KAAK,QACT,UACA;yBACiB,UAAU;;aAEtB,KAAK,KAAK;;EAErB;EAES,aAAa,mBAAiC;AACrD,UAAM,aAAa,iBAAiB;AACpC,UAAM,QAAQ,IAAI,MAAM,2BAA2B;MACjD,SAAS;MACT,UAAU;KACX;AACD,SAAK,cAAc,KAAK;EAC1B;EAES,QAAK;AACZ,UAAM,gBAAgB,KAAK;AAC3B,QAAI,eAAe;AACjB,oBAAc,MAAK;IACrB;EACF;EAES,OAAI;AACX,UAAM,gBAAgB,KAAK;AAC3B,QAAI,eAAe;AACjB,oBAAc,KAAI;IACpB;EACF;EAEA,cAAW;AAET,SAAK,cACH,IAAI,YAAY,8BAA8B;MAC5C,QAAQ,EAAC,OAAO,KAAI;MACpB,SAAS;MACT,UAAU;KACX,CAAC;EAEN;;AA9F2B,WAAA;EAA1B,SAAS,EAAC,MAAM,QAAO,CAAC;;AACiB,WAAA;EAAzC,SAAS,EAAC,MAAM,SAAS,SAAS,KAAI,CAAC;;AAExC,WAAA;EADC,SAAS,EAAC,MAAM,SAAS,WAAW,sBAAqB,CAAC;;AAE/C,WAAA;EAAX,SAAQ;;AAC6B,WAAA;EAArC,SAAS,EAAC,WAAW,cAAa,CAAC;;AACgB,WAAA;EAAnD,SAAS,EAAC,MAAM,SAAS,WAAW,aAAY,CAAC;;AAEjC,WAAA;EAAhB,MAAM,QAAQ;;;;ACpCV,IAAM,SAAS;;;;ACgBf,IAAM,kBAAN,MAAMA,yBAAwB,cAAa;;AAChC,gBAAA,SAA8B,CAAC,MAAM;AAD1C,kBAAe,WAAA;EAD3B,cAAc,mBAAmB;GACrB,eAAe;", "names": ["MdNavigationTab"]}