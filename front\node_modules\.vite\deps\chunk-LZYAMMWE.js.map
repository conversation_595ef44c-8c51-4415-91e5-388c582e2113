{"version": 3, "sources": ["../../@material/web/divider/internal/divider.ts", "../../@material/web/divider/internal/divider-styles.ts", "../../@material/web/divider/divider.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {LitElement} from 'lit';\nimport {property} from 'lit/decorators.js';\n\n/**\n * A divider component.\n */\nexport class Divider extends LitElement {\n  /**\n   * Indents the divider with equal padding on both sides.\n   */\n  @property({type: Boolean, reflect: true}) inset = false;\n\n  /**\n   * Indents the divider with padding on the leading side.\n   */\n  @property({type: Boolean, reflect: true, attribute: 'inset-start'})\n  insetStart = false;\n\n  /**\n   * Indents the divider with padding on the trailing side.\n   */\n  @property({type: Boolean, reflect: true, attribute: 'inset-end'})\n  insetEnd = false;\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n// Generated stylesheet for ./divider/internal/divider-styles.css.\nimport {css} from 'lit';\nexport const styles = css`:host{box-sizing:border-box;color:var(--md-divider-color, var(--md-sys-color-outline-variant, #cac4d0));display:flex;height:var(--md-divider-thickness, 1px);width:100%}:host([inset]),:host([inset-start]){padding-inline-start:16px}:host([inset]),:host([inset-end]){padding-inline-end:16px}:host::before{background:currentColor;content:\"\";height:100%;width:100%}@media(forced-colors: active){:host::before{background:CanvasText}}\n`;\n", "/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {Divider} from './internal/divider.js';\nimport {styles} from './internal/divider-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-divider': MdDivider;\n  }\n}\n\n/**\n * @summary A divider is a thin line that groups content in lists and\n * containers.\n *\n * @description Dividers can reinforce tapability, such as when used to separate\n * list items or define tappable regions in an accordion.\n *\n * @final\n * @suppress {visibility}\n */\n@customElement('md-divider')\nexport class MdDivider extends Divider {\n  static override styles: CSSResultOrNative[] = [styles];\n}\n"], "mappings": ";;;;;;;;;;;;;AAYM,IAAO,UAAP,cAAuB,WAAU;EAAvC,cAAA;;AAI4C,SAAA,QAAQ;AAMlD,SAAA,aAAa;AAMb,SAAA,WAAW;EACb;;AAb4C,WAAA;EAAzC,SAAS,EAAC,MAAM,SAAS,SAAS,KAAI,CAAC;;AAMxC,WAAA;EADC,SAAS,EAAC,MAAM,SAAS,SAAS,MAAM,WAAW,cAAa,CAAC;;AAOlE,WAAA;EADC,SAAS,EAAC,MAAM,SAAS,SAAS,MAAM,WAAW,YAAW,CAAC;;;;ACpB3D,IAAM,SAAS;;;;ACsBf,IAAM,YAAN,MAAMA,mBAAkB,QAAO;;AACpB,UAAA,SAA8B,CAAC,MAAM;AAD1C,YAAS,WAAA;EADrB,cAAc,YAAY;GACd,SAAS;", "names": ["Md<PERSON>iv<PERSON>"]}