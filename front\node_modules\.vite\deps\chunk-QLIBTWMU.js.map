{"version": 3, "sources": ["../../@material/web/labs/behaviors/constraint-validation.ts", "../../@material/web/labs/behaviors/validators/validator.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {isServer, LitElement, PropertyDeclaration, PropertyValues} from 'lit';\n\nimport {internals, WithElementInternals} from './element-internals.js';\nimport {FormAssociated} from './form-associated.js';\nimport {MixinBase, MixinReturn} from './mixin.js';\nimport {Validator} from './validators/validator.js';\n\n/**\n * A form associated element that provides constraint validation APIs.\n *\n * https://developer.mozilla.org/en-US/docs/Web/HTML/Constraint_validation\n */\nexport interface ConstraintValidation extends FormAssociated {\n  /**\n   * Returns a ValidityState object that represents the validity states of the\n   * element.\n   *\n   * https://developer.mozilla.org/en-US/docs/Web/API/ValidityState\n   */\n  readonly validity: ValidityState;\n\n  /**\n   * Returns a validation error message or an empty string if the element is\n   * valid.\n   *\n   * https://developer.mozilla.org/en-US/docs/Web/API/ElementInternals/validationMessage\n   */\n  readonly validationMessage: string;\n\n  /**\n   * Returns whether an element will successfully validate based on forms\n   * validation rules and constraints.\n   *\n   * Disabled and readonly elements will not validate.\n   *\n   * https://developer.mozilla.org/en-US/docs/Web/API/ElementInternals/willValidate\n   */\n  readonly willValidate: boolean;\n\n  /**\n   * Checks the element's constraint validation and returns true if the element\n   * is valid or false if not.\n   *\n   * If invalid, this method will dispatch an `invalid` event.\n   *\n   * https://developer.mozilla.org/en-US/docs/Web/API/ElementInternals/checkValidity\n   *\n   * @return true if the element is valid, or false if not.\n   */\n  checkValidity(): boolean;\n\n  /**\n   * Checks the element's constraint validation and returns true if the element\n   * is valid or false if not.\n   *\n   * If invalid, this method will dispatch a cancelable `invalid` event. If not\n   * canceled, a the current `validationMessage` will be reported to the user.\n   *\n   * https://developer.mozilla.org/en-US/docs/Web/API/ElementInternals/reportValidity\n   *\n   * @return true if the element is valid, or false if not.\n   */\n  reportValidity(): boolean;\n\n  /**\n   * Sets the element's constraint validation error message. When set to a\n   * non-empty string, `validity.customError` will be true and\n   * `validationMessage` will display the provided error.\n   *\n   * Use this method to customize error messages reported.\n   *\n   * https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/setCustomValidity\n   *\n   * @param error The error message to display, or an empty string.\n   */\n  setCustomValidity(error: string): void;\n\n  /**\n   * Creates and returns a `Validator` that is used to compute and cache\n   * validity for the element.\n   *\n   * A validator that caches validity is important since constraint validation\n   * must be computed synchronously and frequently in response to constraint\n   * validation property changes.\n   */\n  [createValidator](): Validator<unknown>;\n\n  /**\n   * Returns shadow DOM child that is used as the anchor for the platform\n   * `reportValidity()` popup. This is often the root element or the inner\n   * focus-delegated element.\n   */\n  [getValidityAnchor](): HTMLElement | null;\n}\n\n/**\n * A symbol property used to create a constraint validation `Validator`.\n * Required for all `mixinConstraintValidation()` elements.\n */\nexport const createValidator = Symbol('createValidator');\n\n/**\n * A symbol property used to return an anchor for constraint validation popups.\n * Required for all `mixinConstraintValidation()` elements.\n */\nexport const getValidityAnchor = Symbol('getValidityAnchor');\n\n// Private symbol members, used to avoid name clashing.\nconst privateValidator = Symbol('privateValidator');\nconst privateSyncValidity = Symbol('privateSyncValidity');\nconst privateCustomValidationMessage = Symbol('privateCustomValidationMessage');\n\n/**\n * Mixes in constraint validation APIs for an element.\n *\n * See https://developer.mozilla.org/en-US/docs/Web/HTML/Constraint_validation\n * for more details.\n *\n * Implementations must provide a validator to cache and compute its validity,\n * along with a shadow root element to anchor validation popups to.\n *\n * @example\n * ```ts\n * const baseClass = mixinConstraintValidation(\n *   mixinFormAssociated(mixinElementInternals(LitElement))\n * );\n *\n * class MyCheckbox extends baseClass {\n *   \\@property({type: Boolean}) checked = false;\n *   \\@property({type: Boolean}) required = false;\n *\n *   [createValidator]() {\n *     return new CheckboxValidator(() => this);\n *   }\n *\n *   [getValidityAnchor]() {\n *     return this.renderRoot.querySelector('.root');\n *   }\n * }\n * ```\n *\n * @param base The class to mix functionality into.\n * @return The provided class with `ConstraintValidation` mixed in.\n */\nexport function mixinConstraintValidation<\n  T extends MixinBase<LitElement & FormAssociated & WithElementInternals>,\n>(base: T): MixinReturn<T, ConstraintValidation> {\n  abstract class ConstraintValidationElement\n    extends base\n    implements ConstraintValidation\n  {\n    get validity() {\n      this[privateSyncValidity]();\n      return this[internals].validity;\n    }\n\n    get validationMessage() {\n      this[privateSyncValidity]();\n      return this[internals].validationMessage;\n    }\n\n    get willValidate() {\n      this[privateSyncValidity]();\n      return this[internals].willValidate;\n    }\n\n    /**\n     * A validator instance created from `[createValidator]()`.\n     */\n    [privateValidator]?: Validator<unknown>;\n\n    /**\n     * Needed for Safari, see https://bugs.webkit.org/show_bug.cgi?id=261432\n     * Replace with this[internals].validity.customError when resolved.\n     */\n    [privateCustomValidationMessage] = '';\n\n    checkValidity() {\n      this[privateSyncValidity]();\n      return this[internals].checkValidity();\n    }\n\n    reportValidity() {\n      this[privateSyncValidity]();\n      return this[internals].reportValidity();\n    }\n\n    setCustomValidity(error: string) {\n      this[privateCustomValidationMessage] = error;\n      this[privateSyncValidity]();\n    }\n\n    override requestUpdate(\n      name?: PropertyKey,\n      oldValue?: unknown,\n      options?: PropertyDeclaration,\n    ) {\n      super.requestUpdate(name, oldValue, options);\n      this[privateSyncValidity]();\n    }\n\n    override firstUpdated(changed: PropertyValues) {\n      super.firstUpdated(changed);\n      // Sync the validity again when the element first renders, since the\n      // validity anchor is now available.\n      //\n      // Elements that `delegatesFocus: true` to an `<input>` will throw an\n      // error in Chrome and Safari when a form tries to submit or call\n      // `form.reportValidity()`:\n      // \"An invalid form control with name='' is not focusable\"\n      //\n      // The validity anchor MUST be provided in `internals.setValidity()` and\n      // MUST be the `<input>` element rendered.\n      //\n      // See https://lit.dev/playground/#gist=6c26e418e0010f7a5aac15005cde8bde\n      // for a reproduction.\n      this[privateSyncValidity]();\n    }\n\n    [privateSyncValidity]() {\n      if (isServer) {\n        return;\n      }\n\n      if (!this[privateValidator]) {\n        this[privateValidator] = this[createValidator]();\n      }\n\n      const {validity, validationMessage: nonCustomValidationMessage} =\n        this[privateValidator].getValidity();\n\n      const customError = !!this[privateCustomValidationMessage];\n      const validationMessage =\n        this[privateCustomValidationMessage] || nonCustomValidationMessage;\n\n      this[internals].setValidity(\n        {...validity, customError},\n        validationMessage,\n        this[getValidityAnchor]() ?? undefined,\n      );\n    }\n\n    [createValidator](): Validator<unknown> {\n      throw new Error('Implement [createValidator]');\n    }\n\n    [getValidityAnchor](): HTMLElement | null {\n      throw new Error('Implement [getValidityAnchor]');\n    }\n  }\n\n  return ConstraintValidationElement;\n}\n", "/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\n/**\n * A class that computes and caches `ValidityStateFlags` for a component with\n * a given `State` interface.\n *\n * Cached performance before computing validity is important since constraint\n * validation must be checked frequently and synchronously when properties\n * change.\n *\n * @template State The expected interface of properties relevant to constraint\n *     validation.\n */\nexport abstract class Validator<State> {\n  /**\n   * The previous state, used to determine if state changed and validation needs\n   * to be re-computed.\n   */\n  private prevState?: State;\n\n  /**\n   * The current validity state and message. This is cached and returns if\n   * constraint validation state does not change.\n   */\n  private currentValidity: ValidityAndMessage = {\n    validity: {},\n    validationMessage: '',\n  };\n\n  /**\n   * Creates a new validator.\n   *\n   * @param getCurrentState A callback that returns the current state of\n   *     constraint validation-related properties.\n   */\n  constructor(private readonly getCurrentState: () => State) {}\n\n  /**\n   * Returns the current `ValidityStateFlags` and validation message for the\n   * validator.\n   *\n   * If the constraint validation state has not changed, this will return a\n   * cached result. This is important since `getValidity()` can be called\n   * frequently in response to synchronous property changes.\n   *\n   * @return The current validity and validation message.\n   */\n  getValidity(): ValidityAndMessage {\n    const state = this.getCurrentState();\n    const hasStateChanged =\n      !this.prevState || !this.equals(this.prevState, state);\n    if (!hasStateChanged) {\n      return this.currentValidity;\n    }\n\n    const {validity, validationMessage} = this.computeValidity(state);\n    this.prevState = this.copy(state);\n    this.currentValidity = {\n      validationMessage,\n      validity: {\n        // Change any `ValidityState` instances into `ValidityStateFlags` since\n        // `ValidityState` cannot be easily `{...spread}`.\n        badInput: validity.badInput,\n        customError: validity.customError,\n        patternMismatch: validity.patternMismatch,\n        rangeOverflow: validity.rangeOverflow,\n        rangeUnderflow: validity.rangeUnderflow,\n        stepMismatch: validity.stepMismatch,\n        tooLong: validity.tooLong,\n        tooShort: validity.tooShort,\n        typeMismatch: validity.typeMismatch,\n        valueMissing: validity.valueMissing,\n      },\n    };\n\n    return this.currentValidity;\n  }\n\n  /**\n   * Computes the `ValidityStateFlags` and validation message for a given set\n   * of constraint validation properties.\n   *\n   * Implementations can use platform elements like `<input>` and `<select>` to\n   * sync state and compute validation along with i18n'd messages. This function\n   * may be expensive, and is only called when state changes.\n   *\n   * @param state The new state of constraint validation properties.\n   * @return An object containing a `validity` property with\n   *     `ValidityStateFlags` and a `validationMessage` property.\n   */\n  protected abstract computeValidity(state: State): ValidityAndMessage;\n\n  /**\n   * Checks if two states are equal. This is used to check against cached state\n   * to see if validity needs to be re-computed.\n   *\n   * @param prev The previous state.\n   * @param next The next state.\n   * @return True if the states are equal, or false if not.\n   */\n  protected abstract equals(prev: State, next: State): boolean;\n\n  /**\n   * Creates a copy of a state. This is used to cache state and check if it\n   * changes.\n   *\n   * Note: do NOT spread the {...state} to copy it. The actual state object is\n   * a web component, and trying to spread its getter/setter properties won't\n   * work.\n   *\n   * @param state The state to copy.\n   * @return A copy of the state.\n   */\n  protected abstract copy(state: State): State;\n}\n\n/**\n * An object containing `ValidityStateFlags` and a corresponding validation\n * message.\n */\nexport interface ValidityAndMessage {\n  /**\n   * Validity flags.\n   */\n  validity: ValidityStateFlags;\n\n  /**\n   * The validation message for the associated flags. It may not be an empty\n   * string if any of the validity flags are `true`.\n   */\n  validationMessage: string;\n}\n"], "mappings": ";;;;;;;;AAyGO,IAAM,kBAAkB,OAAO,iBAAiB;AAMhD,IAAM,oBAAoB,OAAO,mBAAmB;AAG3D,IAAM,mBAAmB,OAAO,kBAAkB;AAClD,IAAM,sBAAsB,OAAO,qBAAqB;AACxD,IAAM,iCAAiC,OAAO,gCAAgC;AAkCxE,SAAU,0BAEd,MAAO;;EACP,MAAe,oCACL,KAAI;IADd,cAAA;;AA4BE,WAAA,EAAA,IAAmC;IA0ErC;IAlGE,IAAI,WAAQ;AACV,WAAK,mBAAmB,EAAC;AACzB,aAAO,KAAK,SAAS,EAAE;IACzB;IAEA,IAAI,oBAAiB;AACnB,WAAK,mBAAmB,EAAC;AACzB,aAAO,KAAK,SAAS,EAAE;IACzB;IAEA,IAAI,eAAY;AACd,WAAK,mBAAmB,EAAC;AACzB,aAAO,KAAK,SAAS,EAAE;IACzB;IAaA,gBAAa;AACX,WAAK,mBAAmB,EAAC;AACzB,aAAO,KAAK,SAAS,EAAE,cAAa;IACtC;IAEA,iBAAc;AACZ,WAAK,mBAAmB,EAAC;AACzB,aAAO,KAAK,SAAS,EAAE,eAAc;IACvC;IAEA,kBAAkB,OAAa;AAC7B,WAAK,8BAA8B,IAAI;AACvC,WAAK,mBAAmB,EAAC;IAC3B;IAES,cACP,MACA,UACA,SAA6B;AAE7B,YAAM,cAAc,MAAM,UAAU,OAAO;AAC3C,WAAK,mBAAmB,EAAC;IAC3B;IAES,aAAa,SAAuB;AAC3C,YAAM,aAAa,OAAO;AAc1B,WAAK,mBAAmB,EAAC;IAC3B;IAEA,EAAA,KA5CC,gCA4CA,oBAAmB,IAAC;AACnB,UAAI,UAAU;AACZ;MACF;AAEA,UAAI,CAAC,KAAK,gBAAgB,GAAG;AAC3B,aAAK,gBAAgB,IAAI,KAAK,eAAe,EAAC;MAChD;AAEA,YAAM,EAAC,UAAU,mBAAmB,2BAA0B,IAC5D,KAAK,gBAAgB,EAAE,YAAW;AAEpC,YAAM,cAAc,CAAC,CAAC,KAAK,8BAA8B;AACzD,YAAM,oBACJ,KAAK,8BAA8B,KAAK;AAE1C,WAAK,SAAS,EAAE,YACd,EAAC,GAAG,UAAU,YAAW,GACzB,mBACA,KAAK,iBAAiB,EAAC,KAAM,MAAS;IAE1C;IAEA,CAAC,eAAe,IAAC;AACf,YAAM,IAAI,MAAM,6BAA6B;IAC/C;IAEA,CAAC,iBAAiB,IAAC;AACjB,YAAM,IAAI,MAAM,+BAA+B;IACjD;;AAGF,SAAO;AACT;;;ACjPM,IAAgB,YAAhB,MAAyB;;;;;;;EAsB7B,YAA6B,iBAA4B;AAA5B,SAAA,kBAAA;AAXrB,SAAA,kBAAsC;MAC5C,UAAU,CAAA;MACV,mBAAmB;;EASuC;;;;;;;;;;;EAY5D,cAAW;AACT,UAAM,QAAQ,KAAK,gBAAe;AAClC,UAAM,kBACJ,CAAC,KAAK,aAAa,CAAC,KAAK,OAAO,KAAK,WAAW,KAAK;AACvD,QAAI,CAAC,iBAAiB;AACpB,aAAO,KAAK;IACd;AAEA,UAAM,EAAC,UAAU,kBAAiB,IAAI,KAAK,gBAAgB,KAAK;AAChE,SAAK,YAAY,KAAK,KAAK,KAAK;AAChC,SAAK,kBAAkB;MACrB;MACA,UAAU;;;QAGR,UAAU,SAAS;QACnB,aAAa,SAAS;QACtB,iBAAiB,SAAS;QAC1B,eAAe,SAAS;QACxB,gBAAgB,SAAS;QACzB,cAAc,SAAS;QACvB,SAAS,SAAS;QAClB,UAAU,SAAS;QACnB,cAAc,SAAS;QACvB,cAAc,SAAS;;;AAI3B,WAAO,KAAK;EACd;;", "names": []}