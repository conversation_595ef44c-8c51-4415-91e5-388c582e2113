{"version": 3, "sources": ["../../@material/web/labs/card/internal/filled-styles.ts", "../../@material/web/labs/card/filled-card.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2024 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n// Generated stylesheet for ./labs/card/internal/filled-styles.css.\nimport {css} from 'lit';\nexport const styles = css`:host{--_container-color: var(--md-filled-card-container-color, var(--md-sys-color-surface-container-highest, #e6e0e9));--_container-elevation: var(--md-filled-card-container-elevation, 0);--_container-shadow-color: var(--md-filled-card-container-shadow-color, var(--md-sys-color-shadow, #000));--_container-shape: var(--md-filled-card-container-shape, var(--md-sys-shape-corner-medium, 12px))}\n`;\n", "/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {Card} from './internal/card.js';\nimport {styles as filledStyles} from './internal/filled-styles.js';\nimport {styles as sharedStyles} from './internal/shared-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-filled-card': MdFilledCard;\n  }\n}\n\n/**\n * @final\n * @suppress {visibility}\n */\n@customElement('md-filled-card')\nexport class MdFilledCard extends Card {\n  static override styles: CSSResultOrNative[] = [sharedStyles, filledStyles];\n}\n"], "mappings": ";;;;;;;;;;;;;;;AAOO,IAAMA,UAAS;;;;ACiBf,IAAM,eAAN,MAAMC,sBAAqB,KAAI;;AACpB,aAAA,SAA8B,CAAC,QAAcC,OAAY;AAD9D,eAAY,WAAA;EADxB,cAAc,gBAAgB;GAClB,YAAY;", "names": ["styles", "MdFilledCard", "styles"]}