{"version": 3, "sources": ["../../@material/web/progress/internal/progress.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {html, LitElement, nothing, TemplateResult} from 'lit';\nimport {property} from 'lit/decorators.js';\nimport {classMap} from 'lit/directives/class-map.js';\n\nimport {ARIAMixinStrict} from '../../internal/aria/aria.js';\nimport {mixinDelegatesAria} from '../../internal/aria/delegate.js';\n\n// Separate variable needed for closure.\nconst progressBaseClass = mixinDelegatesAria(LitElement);\n\n/**\n * A progress component.\n */\nexport abstract class Progress extends progressBaseClass {\n  /**\n   * Progress to display, a fraction between 0 and `max`.\n   */\n  @property({type: Number}) value = 0;\n\n  /**\n   * Maximum progress to display, defaults to 1.\n   */\n  @property({type: Number}) max = 1;\n\n  /**\n   * Whether or not to display indeterminate progress, which gives no indication\n   * to how long an activity will take.\n   */\n  @property({type: Boolean}) indeterminate = false;\n\n  /**\n   * Whether or not to render indeterminate mode using 4 colors instead of one.\n   */\n  @property({type: Boolean, attribute: 'four-color'}) fourColor = false;\n\n  protected override render() {\n    // Needed for closure conformance\n    const {ariaLabel} = this as ARIAMixinStrict;\n    return html`\n      <div\n        class=\"progress ${classMap(this.getRenderClasses())}\"\n        role=\"progressbar\"\n        aria-label=\"${ariaLabel || nothing}\"\n        aria-valuemin=\"0\"\n        aria-valuemax=${this.max}\n        aria-valuenow=${this.indeterminate ? nothing : this.value}\n        >${this.renderIndicator()}</div\n      >\n    `;\n  }\n\n  protected getRenderClasses() {\n    return {\n      'indeterminate': this.indeterminate,\n      'four-color': this.fourColor,\n    };\n  }\n\n  protected abstract renderIndicator(): TemplateResult;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;AAcA,IAAM,oBAAoB,mBAAmB,UAAU;AAKjD,IAAgB,WAAhB,cAAiC,kBAAiB;EAAxD,cAAA;;AAI4B,SAAA,QAAQ;AAKR,SAAA,MAAM;AAML,SAAA,gBAAgB;AAKS,SAAA,YAAY;EA0BlE;EAxBqB,SAAM;AAEvB,UAAM,EAAC,UAAS,IAAI;AACpB,WAAO;;0BAEe,SAAS,KAAK,iBAAgB,CAAE,CAAC;;sBAErC,aAAa,OAAO;;wBAElB,KAAK,GAAG;wBACR,KAAK,gBAAgB,UAAU,KAAK,KAAK;WACtD,KAAK,gBAAe,CAAE;;;EAG/B;EAEU,mBAAgB;AACxB,WAAO;MACL,iBAAiB,KAAK;MACtB,cAAc,KAAK;;EAEvB;;AAvC0B,WAAA;EAAzB,SAAS,EAAC,MAAM,OAAM,CAAC;;AAKE,WAAA;EAAzB,SAAS,EAAC,MAAM,OAAM,CAAC;;AAMG,WAAA;EAA1B,SAAS,EAAC,MAAM,QAAO,CAAC;;AAK2B,WAAA;EAAnD,SAAS,EAAC,MAAM,SAAS,WAAW,aAAY,CAAC;;", "names": []}