{"version": 3, "sources": ["../../@material/web/menu/internal/controllers/surfacePositionController.ts", "../../@material/web/menu/internal/controllers/typeaheadController.ts", "../../@material/web/menu/internal/menu.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {ReactiveController, ReactiveControllerHost} from 'lit';\nimport {StyleInfo} from 'lit/directives/style-map.js';\n\n/**\n * Declare popoverAPI functions and properties. See\n * https://developer.mozilla.org/en-US/docs/Web/API/Popover_API\n * Without this, closure will rename these functions. Can remove once these\n * functions make it into the typescript lib.\n */\ndeclare global {\n  interface HTMLElement {\n    showPopover(): void;\n    hidePopover(): void;\n    togglePopover(force: boolean): void;\n    popover: string | null;\n  }\n}\n\n/**\n * An enum of supported Menu corners\n */\n// tslint:disable-next-line:enforce-name-casing We are mimicking enum style\nexport const Corner = {\n  END_START: 'end-start',\n  END_END: 'end-end',\n  START_START: 'start-start',\n  START_END: 'start-end',\n} as const;\n\n/**\n * A corner of a box in the standard logical property style of <block>_<inline>\n */\nexport type Corner = (typeof Corner)[keyof typeof Corner];\n\n/**\n * An interface that provides a method to customize the rect from which to\n * calculate the anchor positioning. Useful for when you want a surface to\n * anchor to an element in your shadow DOM rather than the host element.\n */\nexport interface SurfacePositionTarget extends HTMLElement {\n  getSurfacePositionClientRect?: () => DOMRect;\n}\n\n/**\n * The configurable options for the surface position controller.\n */\nexport interface SurfacePositionControllerProperties {\n  /**\n   * Disable the `flip` behavior on the block axis of the surface's corner\n   */\n  disableBlockFlip: boolean;\n  /**\n   * Disable the `flip` behavior on the inline axis of the surface's corner\n   */\n  disableInlineFlip: boolean;\n  /**\n   * The corner of the anchor to align the surface's position.\n   */\n  anchorCorner: Corner;\n  /**\n   * The corner of the surface to align to the given anchor corner.\n   */\n  surfaceCorner: Corner;\n  /**\n   * The HTMLElement reference of the surface to be positioned.\n   */\n  surfaceEl: SurfacePositionTarget | null;\n  /**\n   * The HTMLElement reference of the anchor to align to.\n   */\n  anchorEl: SurfacePositionTarget | null;\n  /**\n   * Whether the positioning algorithim should calculate relative to the parent\n   * of the anchor element (absolute) or relative to the window (fixed).\n   *\n   * Examples for `position = 'fixed'`:\n   *\n   * - If there is no `position:relative` in the given parent tree and the\n   *   surface is `position:absolute`\n   * - If the surface is `position:fixed`\n   * - If the surface is in the \"top layer\"\n   * - The anchor and the surface do not share a common `position:relative`\n   *   ancestor\n   */\n  positioning: 'absolute' | 'fixed' | 'document';\n  /**\n   * Whether or not the surface should be \"open\" and visible\n   */\n  isOpen: boolean;\n  /**\n   * The number of pixels in which to offset from the inline axis relative to\n   * logical property.\n   *\n   * Positive is right in LTR and left in RTL.\n   */\n  xOffset: number;\n  /**\n   * The number of pixes in which to offset the block axis.\n   *\n   * Positive is down and negative is up.\n   */\n  yOffset: number;\n  /**\n   * The strategy to follow when repositioning the menu to stay inside the\n   * viewport. \"move\" will simply move the surface to stay in the viewport.\n   * \"resize\" will attempt to resize the surface.\n   *\n   * Both strategies will still attempt to flip the anchor and surface corners.\n   */\n  repositionStrategy: 'move' | 'resize';\n  /**\n   * A function to call after the surface has been positioned.\n   */\n  onOpen: () => void;\n  /**\n   * A function to call before the surface should be closed. (A good time to\n   * perform animations while the surface is still visible)\n   */\n  beforeClose: () => Promise<void>;\n  /**\n   * A function to call after the surface has been closed.\n   */\n  onClose: () => void;\n}\n\n/**\n * Given a surface, an anchor, corners, and some options, this surface will\n * calculate the position of a surface to align the two given corners and keep\n * the surface inside the window viewport. It also provides a StyleInfo map that\n * can be applied to the surface to handle visiblility and position.\n */\nexport class SurfacePositionController implements ReactiveController {\n  // The current styles to apply to the surface.\n  private surfaceStylesInternal: StyleInfo = {\n    'display': 'none',\n  };\n  // Previous values stored for change detection. Open change detection is\n  // calculated separately so initialize it here.\n  private lastValues: SurfacePositionControllerProperties = {\n    isOpen: false,\n  } as SurfacePositionControllerProperties;\n\n  /**\n   * @param host The host to connect the controller to.\n   * @param getProperties A function that returns the properties for the\n   * controller.\n   */\n  constructor(\n    private readonly host: ReactiveControllerHost,\n    private readonly getProperties: () => SurfacePositionControllerProperties,\n  ) {\n    this.host.addController(this);\n  }\n\n  /**\n   * The StyleInfo map to apply to the surface via Lit's stylemap\n   */\n  get surfaceStyles() {\n    return this.surfaceStylesInternal;\n  }\n\n  /**\n   * Calculates the surface's new position required so that the surface's\n   * `surfaceCorner` aligns to the anchor's `anchorCorner` while keeping the\n   * surface inside the window viewport. This positioning also respects RTL by\n   * checking `getComputedStyle()` on the surface element.\n   */\n  async position() {\n    const {\n      surfaceEl,\n      anchorEl,\n      anchorCorner: anchorCornerRaw,\n      surfaceCorner: surfaceCornerRaw,\n      positioning,\n      xOffset,\n      yOffset,\n      disableBlockFlip,\n      disableInlineFlip,\n      repositionStrategy,\n    } = this.getProperties();\n    const anchorCorner = anchorCornerRaw.toLowerCase().trim();\n    const surfaceCorner = surfaceCornerRaw.toLowerCase().trim();\n\n    if (!surfaceEl || !anchorEl) {\n      return;\n    }\n\n    // Store these before we potentially resize the window with the next set of\n    // lines\n    const windowInnerWidth = window.innerWidth;\n    const windowInnerHeight = window.innerHeight;\n\n    const div = document.createElement('div');\n    div.style.opacity = '0';\n    div.style.position = 'fixed';\n    div.style.display = 'block';\n    div.style.inset = '0';\n    document.body.appendChild(div);\n    const scrollbarTestRect = div.getBoundingClientRect();\n    div.remove();\n\n    // Calculate the widths of the scrollbars in the inline and block directions\n    // to account for window-relative calculations.\n    const blockScrollbarHeight = window.innerHeight - scrollbarTestRect.bottom;\n    const inlineScrollbarWidth = window.innerWidth - scrollbarTestRect.right;\n\n    // Paint the surface transparently so that we can get the position and the\n    // rect info of the surface.\n    this.surfaceStylesInternal = {\n      'display': 'block',\n      'opacity': '0',\n    };\n\n    // Wait for it to be visible.\n    this.host.requestUpdate();\n    await this.host.updateComplete;\n\n    // Safari has a bug that makes popovers render incorrectly if the node is\n    // made visible + Animation Frame before calling showPopover().\n    // https://bugs.webkit.org/show_bug.cgi?id=264069\n    // also the cast is required due to differing TS types in Google and OSS.\n    if (\n      (surfaceEl as unknown as {popover: string}).popover &&\n      surfaceEl.isConnected\n    ) {\n      (surfaceEl as unknown as {showPopover: () => void}).showPopover();\n    }\n\n    const surfaceRect = surfaceEl.getSurfacePositionClientRect\n      ? surfaceEl.getSurfacePositionClientRect()\n      : surfaceEl.getBoundingClientRect();\n    const anchorRect = anchorEl.getSurfacePositionClientRect\n      ? anchorEl.getSurfacePositionClientRect()\n      : anchorEl.getBoundingClientRect();\n    const [surfaceBlock, surfaceInline] = surfaceCorner.split('-') as Array<\n      'start' | 'end'\n    >;\n    const [anchorBlock, anchorInline] = anchorCorner.split('-') as Array<\n      'start' | 'end'\n    >;\n\n    // LTR depends on the direction of the SURFACE not the anchor.\n    const isLTR =\n      getComputedStyle(surfaceEl as HTMLElement).direction === 'ltr';\n\n    /*\n     * For more on inline and block dimensions, see MDN article:\n     * https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_logical_properties_and_values\n     *\n     * ┌───── inline/blockDocumentOffset  inlineScrollbarWidth\n     * │       │                                    │\n     * │     ┌─▼─────┐                              │Document\n     * │    ┌┼───────┴──────────────────────────────┼────────┐\n     * │    ││                                      │        │\n     * └──► ││ ┌───── inline/blockWindowOffset      │        │\n     *      ││ │       │                            ▼        │\n     *      ││ │     ┌─▼───┐                 Window┌┐        │\n     *      └┤ │    ┌┼─────┴───────────────────────┼│        │\n     *       │ │    ││                             ││        │\n     *       │ └──► ││  ┌──inline/blockAnchorOffset││        │\n     *       │      ││  │     │                    ││        │\n     *       │      └┤  │  ┌──▼───┐                ││        │\n     *       │       │  │ ┌┼──────┤                ││        │\n     *       │       │  └─►│Anchor│                ││        │\n     *       │       │    └┴──────┘                ││        │\n     *       │       │                             ││        │\n     *       │       │     ┌───────────────────────┼┼────┐   │\n     *       │       │     │ Surface               ││    │   │\n     *       │       │     │                       ││    │   │\n     *       │       │     │                       ││    │   │\n     *       │       │     │                       ││    │   │\n     *       │       │     │                       ││    │   │\n     *       │      ┌┼─────┼───────────────────────┼│    │   │\n     *       │   ┌─►┴──────┼────────────────────────┘    ├┐  │\n     *       │   │         │ inline/blockOOBCorrection   ││  │\n     *       │   │         │                         │   ││  │\n     *       │   │         │                         ├──►├│  │\n     *       │   │         │                         │   ││  │\n     *       │   │         └────────────────────────┐▼───┼┘  │\n     *       │  blockScrollbarHeight                └────┘   │\n     *       │                                               │\n     *       └───────────────────────────────────────────────┘\n     */\n\n    // Calculate the block positioning properties\n    let {blockInset, blockOutOfBoundsCorrection, surfaceBlockProperty} =\n      this.calculateBlock({\n        surfaceRect,\n        anchorRect,\n        anchorBlock,\n        surfaceBlock,\n        yOffset,\n        positioning,\n        windowInnerHeight,\n        blockScrollbarHeight,\n      });\n\n    // If the surface should be out of bounds in the block direction, flip the\n    // surface and anchor corner block values and recalculate\n    if (blockOutOfBoundsCorrection && !disableBlockFlip) {\n      const flippedSurfaceBlock = surfaceBlock === 'start' ? 'end' : 'start';\n      const flippedAnchorBlock = anchorBlock === 'start' ? 'end' : 'start';\n\n      const flippedBlock = this.calculateBlock({\n        surfaceRect,\n        anchorRect,\n        anchorBlock: flippedAnchorBlock,\n        surfaceBlock: flippedSurfaceBlock,\n        yOffset,\n        positioning,\n        windowInnerHeight,\n        blockScrollbarHeight,\n      });\n\n      // In the case that the flipped verion would require less out of bounds\n      // correcting, use the flipped corner block values\n      if (\n        blockOutOfBoundsCorrection > flippedBlock.blockOutOfBoundsCorrection\n      ) {\n        blockInset = flippedBlock.blockInset;\n        blockOutOfBoundsCorrection = flippedBlock.blockOutOfBoundsCorrection;\n        surfaceBlockProperty = flippedBlock.surfaceBlockProperty;\n      }\n    }\n\n    // Calculate the inline positioning properties\n    let {inlineInset, inlineOutOfBoundsCorrection, surfaceInlineProperty} =\n      this.calculateInline({\n        surfaceRect,\n        anchorRect,\n        anchorInline,\n        surfaceInline,\n        xOffset,\n        positioning,\n        isLTR,\n        windowInnerWidth,\n        inlineScrollbarWidth,\n      });\n\n    // If the surface should be out of bounds in the inline direction, flip the\n    // surface and anchor corner inline values and recalculate\n    if (inlineOutOfBoundsCorrection && !disableInlineFlip) {\n      const flippedSurfaceInline = surfaceInline === 'start' ? 'end' : 'start';\n      const flippedAnchorInline = anchorInline === 'start' ? 'end' : 'start';\n\n      const flippedInline = this.calculateInline({\n        surfaceRect,\n        anchorRect,\n        anchorInline: flippedAnchorInline,\n        surfaceInline: flippedSurfaceInline,\n        xOffset,\n        positioning,\n        isLTR,\n        windowInnerWidth,\n        inlineScrollbarWidth,\n      });\n\n      // In the case that the flipped verion would require less out of bounds\n      // correcting, use the flipped corner inline values\n      if (\n        Math.abs(inlineOutOfBoundsCorrection) >\n        Math.abs(flippedInline.inlineOutOfBoundsCorrection)\n      ) {\n        inlineInset = flippedInline.inlineInset;\n        inlineOutOfBoundsCorrection = flippedInline.inlineOutOfBoundsCorrection;\n        surfaceInlineProperty = flippedInline.surfaceInlineProperty;\n      }\n    }\n\n    // If we are simply repositioning the surface back inside the viewport,\n    // subtract the out of bounds correction values from the positioning.\n    if (repositionStrategy === 'move') {\n      blockInset = blockInset - blockOutOfBoundsCorrection;\n      inlineInset = inlineInset - inlineOutOfBoundsCorrection;\n    }\n\n    this.surfaceStylesInternal = {\n      'display': 'block',\n      'opacity': '1',\n      [surfaceBlockProperty]: `${blockInset}px`,\n      [surfaceInlineProperty]: `${inlineInset}px`,\n    };\n\n    // In the case that we are resizing the surface to stay inside the viewport\n    // we need to set height and width on the surface.\n    if (repositionStrategy === 'resize') {\n      // Add a height property to the styles if there is block height correction\n      if (blockOutOfBoundsCorrection) {\n        this.surfaceStylesInternal['height'] = `${\n          surfaceRect.height - blockOutOfBoundsCorrection\n        }px`;\n      }\n\n      // Add a width property to the styles if there is block height correction\n      if (inlineOutOfBoundsCorrection) {\n        this.surfaceStylesInternal['width'] = `${\n          surfaceRect.width - inlineOutOfBoundsCorrection\n        }px`;\n      }\n    }\n\n    this.host.requestUpdate();\n  }\n\n  /**\n   * Calculates the css property, the inset, and the out of bounds correction\n   * for the surface in the block direction.\n   */\n  private calculateBlock(config: {\n    surfaceRect: DOMRect;\n    anchorRect: DOMRect;\n    anchorBlock: 'start' | 'end';\n    surfaceBlock: 'start' | 'end';\n    yOffset: number;\n    positioning: 'absolute' | 'fixed' | 'document';\n    windowInnerHeight: number;\n    blockScrollbarHeight: number;\n  }) {\n    const {\n      surfaceRect,\n      anchorRect,\n      anchorBlock,\n      surfaceBlock,\n      yOffset,\n      positioning,\n      windowInnerHeight,\n      blockScrollbarHeight,\n    } = config;\n    // We use number booleans to multiply values rather than `if` / ternary\n    // statements because it _heavily_ cuts down on nesting and readability\n    const relativeToWindow =\n      positioning === 'fixed' || positioning === 'document' ? 1 : 0;\n    const relativeToDocument = positioning === 'document' ? 1 : 0;\n    const isSurfaceBlockStart = surfaceBlock === 'start' ? 1 : 0;\n    const isSurfaceBlockEnd = surfaceBlock === 'end' ? 1 : 0;\n    const isOneBlockEnd = anchorBlock !== surfaceBlock ? 1 : 0;\n\n    // Whether or not to apply the height of the anchor\n    const blockAnchorOffset = isOneBlockEnd * anchorRect.height + yOffset;\n    // The absolute block position of the anchor relative to window\n    const blockTopLayerOffset =\n      isSurfaceBlockStart * anchorRect.top +\n      isSurfaceBlockEnd *\n        (windowInnerHeight - anchorRect.bottom - blockScrollbarHeight);\n    const blockDocumentOffset =\n      isSurfaceBlockStart * window.scrollY - isSurfaceBlockEnd * window.scrollY;\n\n    // If the surface's block would be out of bounds of the window, move it back\n    // in\n    const blockOutOfBoundsCorrection = Math.abs(\n      Math.min(\n        0,\n        windowInnerHeight -\n          blockTopLayerOffset -\n          blockAnchorOffset -\n          surfaceRect.height,\n      ),\n    );\n\n    // The block logical value of the surface\n    const blockInset =\n      relativeToWindow * blockTopLayerOffset +\n      relativeToDocument * blockDocumentOffset +\n      blockAnchorOffset;\n\n    const surfaceBlockProperty =\n      surfaceBlock === 'start' ? 'inset-block-start' : 'inset-block-end';\n\n    return {blockInset, blockOutOfBoundsCorrection, surfaceBlockProperty};\n  }\n\n  /**\n   * Calculates the css property, the inset, and the out of bounds correction\n   * for the surface in the inline direction.\n   */\n  private calculateInline(config: {\n    isLTR: boolean;\n    surfaceInline: 'start' | 'end';\n    anchorInline: 'start' | 'end';\n    anchorRect: DOMRect;\n    surfaceRect: DOMRect;\n    xOffset: number;\n    positioning: 'absolute' | 'fixed' | 'document';\n    windowInnerWidth: number;\n    inlineScrollbarWidth: number;\n  }) {\n    const {\n      isLTR: isLTRBool,\n      surfaceInline,\n      anchorInline,\n      anchorRect,\n      surfaceRect,\n      xOffset,\n      positioning,\n      windowInnerWidth,\n      inlineScrollbarWidth,\n    } = config;\n    // We use number booleans to multiply values rather than `if` / ternary\n    // statements because it _heavily_ cuts down on nesting and readability\n    const relativeToWindow =\n      positioning === 'fixed' || positioning === 'document' ? 1 : 0;\n    const relativeToDocument = positioning === 'document' ? 1 : 0;\n    const isLTR = isLTRBool ? 1 : 0;\n    const isRTL = isLTRBool ? 0 : 1;\n    const isSurfaceInlineStart = surfaceInline === 'start' ? 1 : 0;\n    const isSurfaceInlineEnd = surfaceInline === 'end' ? 1 : 0;\n    const isOneInlineEnd = anchorInline !== surfaceInline ? 1 : 0;\n\n    // Whether or not to apply the width of the anchor\n    const inlineAnchorOffset = isOneInlineEnd * anchorRect.width + xOffset;\n    // The inline position of the anchor relative to window in LTR\n    const inlineTopLayerOffsetLTR =\n      isSurfaceInlineStart * anchorRect.left +\n      isSurfaceInlineEnd *\n        (windowInnerWidth - anchorRect.right - inlineScrollbarWidth);\n    // The inline position of the anchor relative to window in RTL\n    const inlineTopLayerOffsetRTL =\n      isSurfaceInlineStart *\n        (windowInnerWidth - anchorRect.right - inlineScrollbarWidth) +\n      isSurfaceInlineEnd * anchorRect.left;\n    // The inline position of the anchor relative to window\n    const inlineTopLayerOffset =\n      isLTR * inlineTopLayerOffsetLTR + isRTL * inlineTopLayerOffsetRTL;\n\n    // The inline position of the anchor relative to window in LTR\n    const inlineDocumentOffsetLTR =\n      isSurfaceInlineStart * window.scrollX -\n      isSurfaceInlineEnd * window.scrollX;\n    // The inline position of the anchor relative to window in RTL\n    const inlineDocumentOffsetRTL =\n      isSurfaceInlineEnd * window.scrollX -\n      isSurfaceInlineStart * window.scrollX;\n    // The inline position of the anchor relative to window\n    const inlineDocumentOffset =\n      isLTR * inlineDocumentOffsetLTR + isRTL * inlineDocumentOffsetRTL;\n\n    // If the surface's inline would be out of bounds of the window, move it\n    // back in\n    const inlineOutOfBoundsCorrection = Math.abs(\n      Math.min(\n        0,\n        windowInnerWidth -\n          inlineTopLayerOffset -\n          inlineAnchorOffset -\n          surfaceRect.width,\n      ),\n    );\n\n    // The inline logical value of the surface\n    const inlineInset =\n      relativeToWindow * inlineTopLayerOffset +\n      inlineAnchorOffset +\n      relativeToDocument * inlineDocumentOffset;\n\n    let surfaceInlineProperty =\n      surfaceInline === 'start' ? 'inset-inline-start' : 'inset-inline-end';\n\n    // There are cases where the element is RTL but the root of the page is not.\n    // In these cases we want to not use logical properties.\n    if (positioning === 'document' || positioning === 'fixed') {\n      if (\n        (surfaceInline === 'start' && isLTRBool) ||\n        (surfaceInline === 'end' && !isLTRBool)\n      ) {\n        surfaceInlineProperty = 'left';\n      } else {\n        surfaceInlineProperty = 'right';\n      }\n    }\n\n    return {\n      inlineInset,\n      inlineOutOfBoundsCorrection,\n      surfaceInlineProperty,\n    };\n  }\n\n  hostUpdate() {\n    this.onUpdate();\n  }\n\n  hostUpdated() {\n    this.onUpdate();\n  }\n\n  /**\n   * Checks whether the properties passed into the controller have changed since\n   * the last positioning. If so, it will reposition if the surface is open or\n   * close it if the surface should close.\n   */\n  private async onUpdate() {\n    const props = this.getProperties();\n    let hasChanged = false;\n    for (const [key, value] of Object.entries(props)) {\n      // tslint:disable-next-line\n      hasChanged = hasChanged || value !== (this.lastValues as any)[key];\n      if (hasChanged) break;\n    }\n\n    const openChanged = this.lastValues.isOpen !== props.isOpen;\n    const hasAnchor = !!props.anchorEl;\n    const hasSurface = !!props.surfaceEl;\n\n    if (hasChanged && hasAnchor && hasSurface) {\n      // Only update isOpen, because if it's closed, we do not want to waste\n      // time on a useless reposition calculation. So save the other \"dirty\"\n      // values until next time it opens.\n      this.lastValues.isOpen = props.isOpen;\n\n      if (props.isOpen) {\n        // We are going to do a reposition, so save the prop values for future\n        // dirty checking.\n        this.lastValues = props;\n\n        await this.position();\n        props.onOpen();\n      } else if (openChanged) {\n        await props.beforeClose();\n        this.close();\n        props.onClose();\n      }\n    }\n  }\n\n  /**\n   * Hides the surface.\n   */\n  private close() {\n    this.surfaceStylesInternal = {\n      'display': 'none',\n    };\n    this.host.requestUpdate();\n    const surfaceEl = this.getProperties().surfaceEl;\n\n    // The following type casts are required due to differing TS types in Google\n    // and open source.\n    if (\n      (surfaceEl as unknown as {popover?: string})?.popover &&\n      surfaceEl?.isConnected\n    ) {\n      (surfaceEl as unknown as {hidePopover: () => void}).hidePopover();\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {MenuItem} from './menuItemController.js';\n\n/**\n * The options that are passed to the typeahead controller.\n */\nexport interface TypeaheadControllerProperties {\n  /**\n   * A function that returns an array of menu items to be searched.\n   * @return An array of menu items to be searched by typing.\n   */\n  getItems: () => MenuItem[];\n  /**\n   * The maximum time between each keystroke to keep the current type buffer\n   * alive.\n   */\n  typeaheadBufferTime: number;\n  /**\n   * Whether or not the typeahead should listen for keystrokes or not.\n   */\n  active: boolean;\n}\n\n/**\n * Data structure tuple that helps with indexing.\n *\n * [index, item, normalized header text]\n */\ntype TypeaheadRecord = [number, MenuItem, string];\n/**\n * Indicies to access the TypeaheadRecord tuple type.\n */\nexport const TYPEAHEAD_RECORD = {\n  INDEX: 0,\n  ITEM: 1,\n  TEXT: 2,\n} as const;\n\n/**\n * This controller listens to `keydown` events and searches the header text of\n * an array of `MenuItem`s with the corresponding entered keys within the buffer\n * time and activates the item.\n *\n * @example\n * ```ts\n * const typeaheadController = new TypeaheadController(() => ({\n *   typeaheadBufferTime: 50,\n *   getItems: () => Array.from(document.querySelectorAll('md-menu-item'))\n * }));\n * html`\n *   <div\n *       @keydown=${typeaheadController.onKeydown}\n *       tabindex=\"0\"\n *       class=\"activeItemText\">\n *     <!-- focusable element that will receive keydown events -->\n *     Apple\n *   </div>\n *   <div>\n *     <md-menu-item active header=\"Apple\"></md-menu-item>\n *     <md-menu-item header=\"Apricot\"></md-menu-item>\n *     <md-menu-item header=\"Banana\"></md-menu-item>\n *     <md-menu-item header=\"Olive\"></md-menu-item>\n *     <md-menu-item header=\"Orange\"></md-menu-item>\n *   </div>\n * `;\n * ```\n */\nexport class TypeaheadController {\n  /**\n   * Array of tuples that helps with indexing.\n   */\n  private typeaheadRecords: TypeaheadRecord[] = [];\n  /**\n   * Currently-typed text since last buffer timeout\n   */\n  private typaheadBuffer = '';\n  /**\n   * The timeout id from the current buffer's setTimeout\n   */\n  private cancelTypeaheadTimeout = 0;\n  /**\n   * If we are currently \"typing\"\n   */\n  isTypingAhead = false;\n  /**\n   * The record of the last active item.\n   */\n  lastActiveRecord: TypeaheadRecord | null = null;\n\n  /**\n   * @param getProperties A function that returns the options of the typeahead\n   * controller:\n   *\n   * {\n   *   getItems: A function that returns an array of menu items to be searched.\n   *   typeaheadBufferTime: The maximum time between each keystroke to keep the\n   *       current type buffer alive.\n   * }\n   */\n  constructor(\n    private readonly getProperties: () => TypeaheadControllerProperties,\n  ) {}\n\n  private get items() {\n    return this.getProperties().getItems();\n  }\n\n  private get active() {\n    return this.getProperties().active;\n  }\n\n  /**\n   * Apply this listener to the element that will receive `keydown` events that\n   * should trigger this controller.\n   *\n   * @param event The native browser `KeyboardEvent` from the `keydown` event.\n   */\n  readonly onKeydown = (event: KeyboardEvent) => {\n    if (this.isTypingAhead) {\n      this.typeahead(event);\n    } else {\n      this.beginTypeahead(event);\n    }\n  };\n\n  /**\n   * Sets up typingahead\n   */\n  private beginTypeahead(event: KeyboardEvent) {\n    if (!this.active) {\n      return;\n    }\n\n    // We don't want to typeahead if the _beginning_ of the typeahead is a menu\n    // navigation, or a selection. We will handle \"Space\" only if it's in the\n    // middle of a typeahead\n    if (\n      event.code === 'Space' ||\n      event.code === 'Enter' ||\n      event.code.startsWith('Arrow') ||\n      event.code === 'Escape'\n    ) {\n      return;\n    }\n\n    this.isTypingAhead = true;\n    // Generates the record array data structure which is the index, the element\n    // and a normalized header.\n    this.typeaheadRecords = this.items.map((el, index) => [\n      index,\n      el,\n      el.typeaheadText.trim().toLowerCase(),\n    ]);\n    this.lastActiveRecord =\n      this.typeaheadRecords.find(\n        (record) => record[TYPEAHEAD_RECORD.ITEM].tabIndex === 0,\n      ) ?? null;\n    if (this.lastActiveRecord) {\n      this.lastActiveRecord[TYPEAHEAD_RECORD.ITEM].tabIndex = -1;\n    }\n    this.typeahead(event);\n  }\n\n  /**\n   * Performs the typeahead. Based on the normalized items and the current text\n   * buffer, finds the _next_ item with matching text and activates it.\n   *\n   * @example\n   *\n   * items: Apple, Banana, Olive, Orange, Cucumber\n   * buffer: ''\n   * user types: o\n   *\n   * activates Olive\n   *\n   * @example\n   *\n   * items: Apple, Banana, Olive (active), Orange, Cucumber\n   * buffer: 'o'\n   * user types: l\n   *\n   * activates Olive\n   *\n   * @example\n   *\n   * items: Apple, Banana, Olive (active), Orange, Cucumber\n   * buffer: ''\n   * user types: o\n   *\n   * activates Orange\n   *\n   * @example\n   *\n   * items: Apple, Banana, Olive, Orange (active), Cucumber\n   * buffer: ''\n   * user types: o\n   *\n   * activates Olive\n   */\n  private typeahead(event: KeyboardEvent) {\n    if (event.defaultPrevented) return;\n    clearTimeout(this.cancelTypeaheadTimeout);\n    // Stop typingahead if one of the navigation or selection keys (except for\n    // Space) are pressed\n    if (\n      event.code === 'Enter' ||\n      event.code.startsWith('Arrow') ||\n      event.code === 'Escape'\n    ) {\n      this.endTypeahead();\n      if (this.lastActiveRecord) {\n        this.lastActiveRecord[TYPEAHEAD_RECORD.ITEM].tabIndex = -1;\n      }\n      return;\n    }\n\n    // If Space is pressed, prevent it from selecting and closing the menu\n    if (event.code === 'Space') {\n      event.preventDefault();\n    }\n\n    // Start up a new keystroke buffer timeout\n    this.cancelTypeaheadTimeout = setTimeout(\n      this.endTypeahead,\n      this.getProperties().typeaheadBufferTime,\n    );\n\n    this.typaheadBuffer += event.key.toLowerCase();\n\n    const lastActiveIndex = this.lastActiveRecord\n      ? this.lastActiveRecord[TYPEAHEAD_RECORD.INDEX]\n      : -1;\n    const numRecords = this.typeaheadRecords.length;\n\n    /**\n     * Sorting function that will resort the items starting with the given index\n     *\n     * @example\n     *\n     * this.typeaheadRecords =\n     * 0: [0, <reference>, 'apple']\n     * 1: [1, <reference>, 'apricot']\n     * 2: [2, <reference>, 'banana']\n     * 3: [3, <reference>, 'olive'] <-- lastActiveIndex\n     * 4: [4, <reference>, 'orange']\n     * 5: [5, <reference>, 'strawberry']\n     *\n     * this.typeaheadRecords.sort((a,b) => rebaseIndexOnActive(a)\n     *                                       - rebaseIndexOnActive(b)) ===\n     * 0: [3, <reference>, 'olive'] <-- lastActiveIndex\n     * 1: [4, <reference>, 'orange']\n     * 2: [5, <reference>, 'strawberry']\n     * 3: [0, <reference>, 'apple']\n     * 4: [1, <reference>, 'apricot']\n     * 5: [2, <reference>, 'banana']\n     */\n    const rebaseIndexOnActive = (record: TypeaheadRecord) => {\n      return (\n        (record[TYPEAHEAD_RECORD.INDEX] + numRecords - lastActiveIndex) %\n        numRecords\n      );\n    };\n\n    // records filtered and sorted / rebased around the last active index\n    const matchingRecords = this.typeaheadRecords\n      .filter(\n        (record) =>\n          !record[TYPEAHEAD_RECORD.ITEM].disabled &&\n          record[TYPEAHEAD_RECORD.TEXT].startsWith(this.typaheadBuffer),\n      )\n      .sort((a, b) => rebaseIndexOnActive(a) - rebaseIndexOnActive(b));\n\n    // Just leave if there's nothing that matches. Native select will just\n    // choose the first thing that starts with the next letter in the alphabet\n    // but that's out of scope and hard to localize\n    if (matchingRecords.length === 0) {\n      clearTimeout(this.cancelTypeaheadTimeout);\n      if (this.lastActiveRecord) {\n        this.lastActiveRecord[TYPEAHEAD_RECORD.ITEM].tabIndex = -1;\n      }\n      this.endTypeahead();\n      return;\n    }\n\n    const isNewQuery = this.typaheadBuffer.length === 1;\n    let nextRecord: TypeaheadRecord;\n\n    // This is likely the case that someone is trying to \"tab\" through different\n    // entries that start with the same letter\n    if (this.lastActiveRecord === matchingRecords[0] && isNewQuery) {\n      nextRecord = matchingRecords[1] ?? matchingRecords[0];\n    } else {\n      nextRecord = matchingRecords[0];\n    }\n\n    if (this.lastActiveRecord) {\n      this.lastActiveRecord[TYPEAHEAD_RECORD.ITEM].tabIndex = -1;\n    }\n\n    this.lastActiveRecord = nextRecord;\n    nextRecord[TYPEAHEAD_RECORD.ITEM].tabIndex = 0;\n    nextRecord[TYPEAHEAD_RECORD.ITEM].focus();\n    return;\n  }\n\n  /**\n   * Ends the current typeahead and clears the buffer.\n   */\n  private readonly endTypeahead = () => {\n    this.isTypingAhead = false;\n    this.typaheadBuffer = '';\n    this.typeaheadRecords = [];\n  };\n}\n", "/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport '../../elevation/elevation.js';\nimport '../../focus/md-focus-ring.js';\n\nimport {LitElement, PropertyValues, html, isServer, nothing} from 'lit';\nimport {property, query, queryAssignedElements, state} from 'lit/decorators.js';\nimport {ClassInfo, classMap} from 'lit/directives/class-map.js';\nimport {styleMap} from 'lit/directives/style-map.js';\n\nimport {EASING, createAnimationSignal} from '../../internal/motion/animation.js';\nimport {\n  ListController,\n  NavigableKeys,\n} from '../../list/internal/list-controller.js';\nimport {\n  getActiveItem,\n  getFirstActivatableItem,\n  getLastActivatableItem,\n} from '../../list/internal/list-navigation-helpers.js';\n\nimport {MenuItem} from './controllers/menuItemController.js';\nimport {\n  ActivateTypeaheadEvent,\n  DeactivateTypeaheadEvent,\n  FocusState,\n  isClosableKey,\n  isElementInSubtree,\n} from './controllers/shared.js';\nimport {\n  Corner,\n  SurfacePositionController,\n  SurfacePositionTarget,\n} from './controllers/surfacePositionController.js';\nimport {TypeaheadController} from './controllers/typeaheadController.js';\n\nexport {Corner} from './controllers/surfacePositionController.js';\n\n/**\n * The default value for the typeahead buffer time in Milliseconds.\n */\nexport const DEFAULT_TYPEAHEAD_BUFFER_TIME = 200;\n\nconst submenuNavKeys = new Set<string>([\n  NavigableKeys.ArrowDown,\n  NavigableKeys.ArrowUp,\n  NavigableKeys.Home,\n  NavigableKeys.End,\n]);\n\nconst menuNavKeys = new Set<string>([\n  NavigableKeys.ArrowLeft,\n  NavigableKeys.ArrowRight,\n  ...submenuNavKeys,\n]);\n\n/**\n * Gets the currently focused element on the page.\n *\n * @param activeDoc The document or shadowroot from which to start the search.\n *    Defaults to `window.document`\n * @return Returns the currently deeply focused element or `null` if none.\n */\nfunction getFocusedElement(\n  activeDoc: Document | ShadowRoot = document,\n): HTMLElement | null {\n  let activeEl = activeDoc.activeElement as HTMLElement | null;\n\n  // Check for activeElement in the case that an element with a shadow root host\n  // is currently focused.\n  while (activeEl && activeEl?.shadowRoot?.activeElement) {\n    activeEl = activeEl.shadowRoot.activeElement as HTMLElement | null;\n  }\n\n  return activeEl;\n}\n\n/**\n * @fires opening {Event} Fired before the opening animation begins\n * @fires opened {Event} Fired once the menu is open, after any animations\n * @fires closing {Event} Fired before the closing animation begins\n * @fires closed {Event} Fired once the menu is closed, after any animations\n */\nexport abstract class Menu extends LitElement {\n  @query('.menu') private readonly surfaceEl!: HTMLElement | null;\n  @query('slot') private readonly slotEl!: HTMLSlotElement | null;\n\n  /**\n   * The ID of the element in the same root node in which the menu should align\n   * to. Overrides setting `anchorElement = elementReference`.\n   *\n   * __NOTE__: anchor or anchorElement must either be an HTMLElement or resolve\n   * to an HTMLElement in order for menu to open.\n   */\n  @property() anchor = '';\n  /**\n   * Whether the positioning algorithm should calculate relative to the parent\n   * of the anchor element (`absolute`), relative to the window (`fixed`), or\n   * relative to the document (`document`). `popover` will use the popover API\n   * to render the menu in the top-layer. If your browser does not support the\n   * popover API, it will fall back to `fixed`.\n   *\n   * __Examples for `position = 'fixed'`:__\n   *\n   * - If there is no `position:relative` in the given parent tree and the\n   *   surface is `position:absolute`\n   * - If the surface is `position:fixed`\n   * - If the surface is in the \"top layer\"\n   * - The anchor and the surface do not share a common `position:relative`\n   *   ancestor\n   *\n   * When using `positioning=fixed`, in most cases, the menu should position\n   * itself above most other `position:absolute` or `position:fixed` elements\n   * when placed inside of them. e.g. using a menu inside of an `md-dialog`.\n   *\n   * __NOTE__: Fixed menus will not scroll with the page and will be fixed to\n   * the window instead.\n   *\n   * __Examples for `position = 'document'`:__\n   *\n   * - There is no parent that creates a relative positioning context e.g.\n   *   `position: relative`, `position: absolute`, `transform: translate(x, y)`,\n   *   etc.\n   * - You put the effort into hoisting the menu to the top of the DOM like the\n   *   end of the `<body>` to render over everything or in a top-layer.\n   * - You are reusing a single `md-menu` element that dynamically renders\n   *   content.\n   *\n   * __Examples for `position = 'popover'`:__\n   *\n   * - Your browser supports `popover`.\n   * - Most cases. Once popover is in browsers, this will become the default.\n   */\n  @property() positioning: 'absolute' | 'fixed' | 'document' | 'popover' =\n    'absolute';\n  /**\n   * Skips the opening and closing animations.\n   */\n  @property({type: Boolean}) quick = false;\n  /**\n   * Displays overflow content like a submenu. Not required in most cases when\n   * using `positioning=\"popover\"`.\n   *\n   * __NOTE__: This may cause adverse effects if you set\n   * `md-menu {max-height:...}`\n   * and have items overflowing items in the \"y\" direction.\n   */\n  @property({type: Boolean, attribute: 'has-overflow'}) hasOverflow = false;\n  /**\n   * Opens the menu and makes it visible. Alternative to the `.show()` and\n   * `.close()` methods\n   */\n  @property({type: Boolean, reflect: true}) open = false;\n  /**\n   * Offsets the menu's inline alignment from the anchor by the given number in\n   * pixels. This value is direction aware and will follow the LTR / RTL\n   * direction.\n   *\n   * e.g. LTR: positive -> right, negative -> left\n   *      RTL: positive -> left, negative -> right\n   */\n  @property({type: Number, attribute: 'x-offset'}) xOffset = 0;\n  /**\n   * Offsets the menu's block alignment from the anchor by the given number in\n   * pixels.\n   *\n   * e.g. positive -> down, negative -> up\n   */\n  @property({type: Number, attribute: 'y-offset'}) yOffset = 0;\n  /**\n   * Disable the `flip` behavior that usually happens on the horizontal axis\n   * when the surface would render outside the viewport.\n   */\n  @property({type: Boolean, attribute: 'no-horizontal-flip'}) noHorizontalFlip =\n    false;\n  /**\n   * Disable the `flip` behavior that usually happens on the vertical axis when\n   * the surface would render outside the viewport.\n   */\n  @property({type: Boolean, attribute: 'no-vertical-flip'}) noVerticalFlip =\n    false;\n  /**\n   * The max time between the keystrokes of the typeahead menu behavior before\n   * it clears the typeahead buffer.\n   */\n  @property({type: Number, attribute: 'typeahead-delay'})\n  typeaheadDelay = DEFAULT_TYPEAHEAD_BUFFER_TIME;\n  /**\n   * The corner of the anchor which to align the menu in the standard logical\n   * property style of <block>-<inline> e.g. `'end-start'`.\n   *\n   * NOTE: This value may not be respected by the menu positioning algorithm\n   * if the menu would render outisde the viewport.\n   * Use `no-horizontal-flip` or `no-vertical-flip` to force the usage of the value\n   */\n  @property({attribute: 'anchor-corner'})\n  anchorCorner: Corner = Corner.END_START;\n  /**\n   * The corner of the menu which to align the anchor in the standard logical\n   * property style of <block>-<inline> e.g. `'start-start'`.\n   *\n   * NOTE: This value may not be respected by the menu positioning algorithm\n   * if the menu would render outisde the viewport.\n   * Use `no-horizontal-flip` or `no-vertical-flip` to force the usage of the value\n   */\n  @property({attribute: 'menu-corner'}) menuCorner: Corner = Corner.START_START;\n  /**\n   * Keeps the user clicks outside the menu.\n   *\n   * NOTE: clicking outside may still cause focusout to close the menu so see\n   * `stayOpenOnFocusout`.\n   */\n  @property({type: Boolean, attribute: 'stay-open-on-outside-click'})\n  stayOpenOnOutsideClick = false;\n  /**\n   * Keeps the menu open when focus leaves the menu's composed subtree.\n   *\n   * NOTE: Focusout behavior will stop propagation of the focusout event. Set\n   * this property to true to opt-out of menu's focusout handling altogether.\n   */\n  @property({type: Boolean, attribute: 'stay-open-on-focusout'})\n  stayOpenOnFocusout = false;\n  /**\n   * After closing, does not restore focus to the last focused element before\n   * the menu was opened.\n   */\n  @property({type: Boolean, attribute: 'skip-restore-focus'})\n  skipRestoreFocus = false;\n  /**\n   * The element that should be focused by default once opened.\n   *\n   * NOTE: When setting default focus to 'LIST_ROOT', remember to change\n   * `tabindex` to `0` and change md-menu's display to something other than\n   * `display: contents` when necessary.\n   */\n  @property({attribute: 'default-focus'})\n  defaultFocus: FocusState = FocusState.FIRST_ITEM;\n\n  /**\n   * Turns off navigation wrapping. By default, navigating past the end of the\n   * menu items will wrap focus back to the beginning and vice versa. Use this\n   * for ARIA patterns that do not wrap focus, like combobox.\n   */\n  @property({type: Boolean, attribute: 'no-navigation-wrap'})\n  noNavigationWrap = false;\n\n  @queryAssignedElements({flatten: true}) protected slotItems!: HTMLElement[];\n  @state() private typeaheadActive = true;\n\n  /**\n   * Whether or not the current menu is a submenu and should not handle specific\n   * navigation keys.\n   *\n   * @export\n   */\n  isSubmenu = false;\n\n  /**\n   * The event path of the last window pointerdown event.\n   */\n  private pointerPath: EventTarget[] = [];\n\n  /**\n   * Whether or not the menu is repositoining due to window / document resize\n   */\n  private isRepositioning = false;\n  private readonly openCloseAnimationSignal = createAnimationSignal();\n\n  private readonly listController = new ListController<MenuItem>({\n    isItem: (maybeItem: HTMLElement): maybeItem is MenuItem => {\n      return maybeItem.hasAttribute('md-menu-item');\n    },\n    getPossibleItems: () => this.slotItems,\n    isRtl: () => getComputedStyle(this).direction === 'rtl',\n    deactivateItem: (item: MenuItem) => {\n      item.selected = false;\n      item.tabIndex = -1;\n    },\n    activateItem: (item: MenuItem) => {\n      item.selected = true;\n      item.tabIndex = 0;\n    },\n    isNavigableKey: (key: string) => {\n      if (!this.isSubmenu) {\n        return menuNavKeys.has(key);\n      }\n\n      const isRtl = getComputedStyle(this).direction === 'rtl';\n      // we want md-submenu to handle the submenu's left/right arrow exit\n      // key so it can close the menu instead of navigate the list.\n      // Therefore we need to include all keys but left/right arrow close\n      // key\n      const arrowOpen = isRtl\n        ? NavigableKeys.ArrowLeft\n        : NavigableKeys.ArrowRight;\n\n      if (key === arrowOpen) {\n        return true;\n      }\n\n      return submenuNavKeys.has(key);\n    },\n    wrapNavigation: () => !this.noNavigationWrap,\n  });\n\n  /**\n   * Whether the menu is animating upwards or downwards when opening. This is\n   * helpful for calculating some animation calculations.\n   */\n  private get openDirection(): 'UP' | 'DOWN' {\n    const menuCornerBlock = this.menuCorner.split('-')[0];\n    return menuCornerBlock === 'start' ? 'DOWN' : 'UP';\n  }\n\n  /**\n   * The element that was focused before the menu opened.\n   */\n  private lastFocusedElement: HTMLElement | null = null;\n\n  /**\n   * Handles typeahead navigation through the menu.\n   */\n  typeaheadController = new TypeaheadController(() => {\n    return {\n      getItems: () => this.items,\n      typeaheadBufferTime: this.typeaheadDelay,\n      active: this.typeaheadActive,\n    };\n  });\n\n  private currentAnchorElement: HTMLElement | null = null;\n\n  /**\n   * The element which the menu should align to. If `anchor` is set to a\n   * non-empty idref string, then `anchorEl` will resolve to the element with\n   * the given id in the same root node. Otherwise, `null`.\n   */\n  get anchorElement(): (HTMLElement & Partial<SurfacePositionTarget>) | null {\n    if (this.anchor) {\n      return (this.getRootNode() as Document | ShadowRoot).querySelector(\n        `#${this.anchor}`,\n      );\n    }\n\n    return this.currentAnchorElement;\n  }\n\n  set anchorElement(\n    element: (HTMLElement & Partial<SurfacePositionTarget>) | null,\n  ) {\n    this.currentAnchorElement = element;\n    this.requestUpdate('anchorElement');\n  }\n\n  private readonly internals =\n    // Cast needed for closure\n    (this as HTMLElement).attachInternals();\n\n  constructor() {\n    super();\n    if (!isServer) {\n      this.internals.role = 'menu';\n      this.addEventListener('keydown', this.handleKeydown);\n      // Capture so that we can grab the event before it reaches the menu item\n      // istelf. Specifically useful for the case where typeahead encounters a\n      // space and we don't want the menu item to close the menu.\n      this.addEventListener('keydown', this.captureKeydown, {capture: true});\n      this.addEventListener('focusout', this.handleFocusout);\n    }\n  }\n\n  /**\n   * Handles positioning the surface and aligning it to the anchor as well as\n   * keeping it in the viewport.\n   */\n  private readonly menuPositionController = new SurfacePositionController(\n    this,\n    () => {\n      return {\n        anchorCorner: this.anchorCorner,\n        surfaceCorner: this.menuCorner,\n        surfaceEl: this.surfaceEl,\n        anchorEl: this.anchorElement,\n        positioning:\n          this.positioning === 'popover' ? 'document' : this.positioning,\n        isOpen: this.open,\n        xOffset: this.xOffset,\n        yOffset: this.yOffset,\n        disableBlockFlip: this.noVerticalFlip,\n        disableInlineFlip: this.noHorizontalFlip,\n        onOpen: this.onOpened,\n        beforeClose: this.beforeClose,\n        onClose: this.onClosed,\n        // We can't resize components that have overflow like menus with\n        // submenus because the overflow-y will show menu items / content\n        // outside the bounds of the menu. Popover API fixes this because each\n        // submenu is hoisted to the top-layer and are not considered overflow\n        // content.\n        repositionStrategy:\n          this.hasOverflow && this.positioning !== 'popover'\n            ? 'move'\n            : 'resize',\n      };\n    },\n  );\n\n  /**\n   * The menu items associated with this menu. The items must be `MenuItem`s and\n   * have both the `md-menu-item` and `md-list-item` attributes.\n   */\n  get items(): MenuItem[] {\n    return this.listController.items;\n  }\n\n  protected override willUpdate(changed: PropertyValues<Menu>) {\n    if (!changed.has('open')) {\n      return;\n    }\n\n    if (this.open) {\n      this.removeAttribute('aria-hidden');\n      return;\n    }\n\n    this.setAttribute('aria-hidden', 'true');\n  }\n\n  override update(changed: PropertyValues<Menu>) {\n    if (changed.has('open')) {\n      if (this.open) {\n        this.setUpGlobalEventListeners();\n      } else {\n        this.cleanUpGlobalEventListeners();\n      }\n    }\n\n    // Firefox does not support popover. Fall-back to using fixed.\n    if (\n      changed.has('positioning') &&\n      this.positioning === 'popover' &&\n      // type required for Google JS conformance\n      !(this as unknown as {showPopover?: () => void}).showPopover\n    ) {\n      this.positioning = 'fixed';\n    }\n\n    super.update(changed);\n  }\n\n  private readonly onWindowResize = () => {\n    if (\n      this.isRepositioning ||\n      (this.positioning !== 'document' &&\n        this.positioning !== 'fixed' &&\n        this.positioning !== 'popover')\n    ) {\n      return;\n    }\n    this.isRepositioning = true;\n    this.reposition();\n    this.isRepositioning = false;\n  };\n\n  override connectedCallback() {\n    super.connectedCallback();\n    if (this.open) {\n      this.setUpGlobalEventListeners();\n    }\n  }\n\n  override disconnectedCallback() {\n    super.disconnectedCallback();\n    this.cleanUpGlobalEventListeners();\n  }\n\n  override getBoundingClientRect() {\n    if (!this.surfaceEl) {\n      return super.getBoundingClientRect();\n    }\n    return this.surfaceEl.getBoundingClientRect();\n  }\n\n  override getClientRects() {\n    if (!this.surfaceEl) {\n      return super.getClientRects();\n    }\n    return this.surfaceEl.getClientRects();\n  }\n\n  protected override render() {\n    return this.renderSurface();\n  }\n\n  /**\n   * Renders the positionable surface element and its contents.\n   */\n  private renderSurface() {\n    return html`\n      <div\n        class=\"menu ${classMap(this.getSurfaceClasses())}\"\n        style=${styleMap(this.menuPositionController.surfaceStyles)}\n        popover=${this.positioning === 'popover' ? 'manual' : nothing}>\n        ${this.renderElevation()}\n        <div class=\"items\">\n          <div class=\"item-padding\"> ${this.renderMenuItems()} </div>\n        </div>\n      </div>\n    `;\n  }\n\n  /**\n   * Renders the menu items' slot\n   */\n  private renderMenuItems() {\n    return html`<slot\n      @close-menu=${this.onCloseMenu}\n      @deactivate-items=${this.onDeactivateItems}\n      @request-activation=${this.onRequestActivation}\n      @deactivate-typeahead=${this.handleDeactivateTypeahead}\n      @activate-typeahead=${this.handleActivateTypeahead}\n      @stay-open-on-focusout=${this.handleStayOpenOnFocusout}\n      @close-on-focusout=${this.handleCloseOnFocusout}\n      @slotchange=${this.listController.onSlotchange}></slot>`;\n  }\n\n  /**\n   * Renders the elevation component.\n   */\n  private renderElevation() {\n    return html`<md-elevation part=\"elevation\"></md-elevation>`;\n  }\n\n  private getSurfaceClasses(): ClassInfo {\n    return {\n      open: this.open,\n      fixed: this.positioning === 'fixed',\n      'has-overflow': this.hasOverflow,\n    };\n  }\n\n  private readonly handleFocusout = async (event: FocusEvent) => {\n    const anchorEl = this.anchorElement!;\n    // Do not close if we focused out by clicking on the anchor element. We\n    // can't assume anchor buttons can be the related target because of iOS does\n    // not focus buttons.\n    if (\n      this.stayOpenOnFocusout ||\n      !this.open ||\n      this.pointerPath.includes(anchorEl)\n    ) {\n      return;\n    }\n\n    if (event.relatedTarget) {\n      // Don't close the menu if we are switching focus between menu,\n      // md-menu-item, and md-list or if the anchor was click focused, but check\n      // if length of pointerPath is 0 because that means something was at least\n      // clicked (shift+tab case).\n      if (\n        isElementInSubtree(event.relatedTarget, this) ||\n        (this.pointerPath.length !== 0 &&\n          isElementInSubtree(event.relatedTarget, anchorEl))\n      ) {\n        return;\n      }\n    } else if (this.pointerPath.includes(this)) {\n      // If menu tabindex == -1 and the user clicks on the menu or a divider, we\n      // want to keep the menu open.\n      return;\n    }\n\n    const oldRestoreFocus = this.skipRestoreFocus;\n    // allow focus to continue to the next focused object rather than returning\n    this.skipRestoreFocus = true;\n    this.close();\n    // await for close\n    await this.updateComplete;\n    // return to previous behavior\n    this.skipRestoreFocus = oldRestoreFocus;\n  };\n\n  private captureKeydown(event: KeyboardEvent) {\n    if (\n      event.target === this &&\n      !event.defaultPrevented &&\n      isClosableKey(event.code)\n    ) {\n      event.preventDefault();\n      this.close();\n    }\n\n    this.typeaheadController.onKeydown(event);\n  }\n\n  /**\n   * Saves the last focused element focuses the new element based on\n   * `defaultFocus`, and animates open.\n   */\n  private readonly onOpened = async () => {\n    this.lastFocusedElement = getFocusedElement();\n\n    const items = this.items;\n    const activeItemRecord = getActiveItem(items);\n\n    if (activeItemRecord && this.defaultFocus !== FocusState.NONE) {\n      activeItemRecord.item.tabIndex = -1;\n    }\n\n    let animationAborted = !this.quick;\n\n    if (this.quick) {\n      this.dispatchEvent(new Event('opening'));\n    } else {\n      animationAborted = !!(await this.animateOpen());\n    }\n\n    // This must come after the opening animation or else it may focus one of\n    // the items before the animation has begun and causes the list to slide\n    // (block-padding-of-the-menu)px at the end of the animation\n    switch (this.defaultFocus) {\n      case FocusState.FIRST_ITEM:\n        const first = getFirstActivatableItem(items);\n        if (first) {\n          first.tabIndex = 0;\n          first.focus();\n          await (first as LitElement & MenuItem).updateComplete;\n        }\n        break;\n      case FocusState.LAST_ITEM:\n        const last = getLastActivatableItem(items);\n        if (last) {\n          last.tabIndex = 0;\n          last.focus();\n          await (last as LitElement & MenuItem).updateComplete;\n        }\n        break;\n      case FocusState.LIST_ROOT:\n        this.focus();\n        break;\n      default:\n      case FocusState.NONE:\n        // Do nothing.\n        break;\n    }\n\n    if (!animationAborted) {\n      this.dispatchEvent(new Event('opened'));\n    }\n  };\n\n  /**\n   * Animates closed.\n   */\n  private readonly beforeClose = async () => {\n    this.open = false;\n\n    if (!this.skipRestoreFocus) {\n      this.lastFocusedElement?.focus?.();\n    }\n\n    if (!this.quick) {\n      await this.animateClose();\n    }\n  };\n\n  /**\n   * Focuses the last focused element.\n   */\n  private readonly onClosed = () => {\n    if (this.quick) {\n      this.dispatchEvent(new Event('closing'));\n      this.dispatchEvent(new Event('closed'));\n    }\n  };\n\n  /**\n   * Performs the opening animation:\n   *\n   * https://direct.googleplex.com/#/spec/295000003+271060003\n   *\n   * @return A promise that resolve to `true` if the animation was aborted,\n   *     `false` if it was not aborted.\n   */\n  private async animateOpen() {\n    const surfaceEl = this.surfaceEl;\n    const slotEl = this.slotEl;\n\n    if (!surfaceEl || !slotEl) return true;\n\n    const openDirection = this.openDirection;\n    this.dispatchEvent(new Event('opening'));\n    // needs to be imperative because we don't want to mix animation and Lit\n    // render timing\n    surfaceEl.classList.toggle('animating', true);\n\n    const signal = this.openCloseAnimationSignal.start();\n    const height = surfaceEl.offsetHeight;\n    const openingUpwards = openDirection === 'UP';\n    const children = this.items;\n    const FULL_DURATION = 500;\n    const SURFACE_OPACITY_DURATION = 50;\n    const ITEM_OPACITY_DURATION = 250;\n    // We want to fit every child fade-in animation within the full duration of\n    // the animation.\n    const DELAY_BETWEEN_ITEMS =\n      (FULL_DURATION - ITEM_OPACITY_DURATION) / children.length;\n\n    const surfaceHeightAnimation = surfaceEl.animate(\n      [{height: '0px'}, {height: `${height}px`}],\n      {\n        duration: FULL_DURATION,\n        easing: EASING.EMPHASIZED,\n      },\n    );\n    // When we are opening upwards, we want to make sure the last item is always\n    // in view, so we need to translate it upwards the opposite direction of the\n    // height animation\n    const upPositionCorrectionAnimation = slotEl.animate(\n      [\n        {transform: openingUpwards ? `translateY(-${height}px)` : ''},\n        {transform: ''},\n      ],\n      {duration: FULL_DURATION, easing: EASING.EMPHASIZED},\n    );\n\n    const surfaceOpacityAnimation = surfaceEl.animate(\n      [{opacity: 0}, {opacity: 1}],\n      SURFACE_OPACITY_DURATION,\n    );\n\n    const childrenAnimations: Array<[HTMLElement, Animation]> = [];\n\n    for (let i = 0; i < children.length; i++) {\n      // If we are animating upwards, then reverse the children list.\n      const directionalIndex = openingUpwards ? children.length - 1 - i : i;\n      const child = children[directionalIndex];\n      const animation = child.animate([{opacity: 0}, {opacity: 1}], {\n        duration: ITEM_OPACITY_DURATION,\n        delay: DELAY_BETWEEN_ITEMS * i,\n      });\n\n      // Make them all initially hidden and then clean up at the end of each\n      // animation.\n      child.classList.toggle('md-menu-hidden', true);\n      animation.addEventListener('finish', () => {\n        child.classList.toggle('md-menu-hidden', false);\n      });\n\n      childrenAnimations.push([child, animation]);\n    }\n\n    let resolveAnimation = (value: boolean) => {};\n    const animationFinished = new Promise<boolean>((resolve) => {\n      resolveAnimation = resolve;\n    });\n\n    signal.addEventListener('abort', () => {\n      surfaceHeightAnimation.cancel();\n      upPositionCorrectionAnimation.cancel();\n      surfaceOpacityAnimation.cancel();\n      childrenAnimations.forEach(([child, animation]) => {\n        child.classList.toggle('md-menu-hidden', false);\n        animation.cancel();\n      });\n\n      resolveAnimation(true);\n    });\n\n    surfaceHeightAnimation.addEventListener('finish', () => {\n      surfaceEl.classList.toggle('animating', false);\n      this.openCloseAnimationSignal.finish();\n      resolveAnimation(false);\n    });\n\n    return await animationFinished;\n  }\n\n  /**\n   * Performs the closing animation:\n   *\n   * https://direct.googleplex.com/#/spec/295000003+271060003\n   */\n  private animateClose() {\n    let resolve!: (value: unknown) => void;\n\n    // This promise blocks the surface position controller from setting\n    // display: none on the surface which will interfere with this animation.\n    const animationEnded = new Promise((res) => {\n      resolve = res;\n    });\n\n    const surfaceEl = this.surfaceEl;\n    const slotEl = this.slotEl;\n\n    if (!surfaceEl || !slotEl) {\n      resolve(false);\n      return animationEnded;\n    }\n\n    const openDirection = this.openDirection;\n    const closingDownwards = openDirection === 'UP';\n    this.dispatchEvent(new Event('closing'));\n    // needs to be imperative because we don't want to mix animation and Lit\n    // render timing\n    surfaceEl.classList.toggle('animating', true);\n    const signal = this.openCloseAnimationSignal.start();\n    const height = surfaceEl.offsetHeight;\n    const children = this.items;\n    const FULL_DURATION = 150;\n    const SURFACE_OPACITY_DURATION = 50;\n    // The surface fades away at the very end\n    const SURFACE_OPACITY_DELAY = FULL_DURATION - SURFACE_OPACITY_DURATION;\n    const ITEM_OPACITY_DURATION = 50;\n    const ITEM_OPACITY_INITIAL_DELAY = 50;\n    const END_HEIGHT_PERCENTAGE = 0.35;\n\n    // We want to fit every child fade-out animation within the full duration of\n    // the animation.\n    const DELAY_BETWEEN_ITEMS =\n      (FULL_DURATION - ITEM_OPACITY_INITIAL_DELAY - ITEM_OPACITY_DURATION) /\n      children.length;\n\n    // The mock has the animation shrink to 35%\n    const surfaceHeightAnimation = surfaceEl.animate(\n      [\n        {height: `${height}px`},\n        {height: `${height * END_HEIGHT_PERCENTAGE}px`},\n      ],\n      {\n        duration: FULL_DURATION,\n        easing: EASING.EMPHASIZED_ACCELERATE,\n      },\n    );\n\n    // When we are closing downwards, we want to make sure the last item is\n    // always in view, so we need to translate it upwards the opposite direction\n    // of the height animation\n    const downPositionCorrectionAnimation = slotEl.animate(\n      [\n        {transform: ''},\n        {\n          transform: closingDownwards\n            ? `translateY(-${height * (1 - END_HEIGHT_PERCENTAGE)}px)`\n            : '',\n        },\n      ],\n      {duration: FULL_DURATION, easing: EASING.EMPHASIZED_ACCELERATE},\n    );\n\n    const surfaceOpacityAnimation = surfaceEl.animate(\n      [{opacity: 1}, {opacity: 0}],\n      {duration: SURFACE_OPACITY_DURATION, delay: SURFACE_OPACITY_DELAY},\n    );\n\n    const childrenAnimations: Array<[HTMLElement, Animation]> = [];\n\n    for (let i = 0; i < children.length; i++) {\n      // If the animation is closing upwards, then reverse the list of\n      // children so that we animate in the opposite direction.\n      const directionalIndex = closingDownwards ? i : children.length - 1 - i;\n      const child = children[directionalIndex];\n      const animation = child.animate([{opacity: 1}, {opacity: 0}], {\n        duration: ITEM_OPACITY_DURATION,\n        delay: ITEM_OPACITY_INITIAL_DELAY + DELAY_BETWEEN_ITEMS * i,\n      });\n\n      // Make sure the items stay hidden at the end of each child animation.\n      // We clean this up at the end of the overall animation.\n      animation.addEventListener('finish', () => {\n        child.classList.toggle('md-menu-hidden', true);\n      });\n      childrenAnimations.push([child, animation]);\n    }\n\n    signal.addEventListener('abort', () => {\n      surfaceHeightAnimation.cancel();\n      downPositionCorrectionAnimation.cancel();\n      surfaceOpacityAnimation.cancel();\n      childrenAnimations.forEach(([child, animation]) => {\n        animation.cancel();\n        child.classList.toggle('md-menu-hidden', false);\n      });\n      resolve(false);\n    });\n\n    surfaceHeightAnimation.addEventListener('finish', () => {\n      surfaceEl.classList.toggle('animating', false);\n      childrenAnimations.forEach(([child]) => {\n        child.classList.toggle('md-menu-hidden', false);\n      });\n      this.openCloseAnimationSignal.finish();\n      this.dispatchEvent(new Event('closed'));\n      resolve(true);\n    });\n\n    return animationEnded;\n  }\n\n  private handleKeydown(event: KeyboardEvent) {\n    // At any key event, the pointer interaction is done so we need to clear our\n    // cached pointerpath. This handles the case where the user clicks on the\n    // anchor, and then hits shift+tab\n    this.pointerPath = [];\n    this.listController.handleKeydown(event);\n  }\n\n  private setUpGlobalEventListeners() {\n    document.addEventListener('click', this.onDocumentClick, {capture: true});\n    window.addEventListener('pointerdown', this.onWindowPointerdown);\n    document.addEventListener('resize', this.onWindowResize, {passive: true});\n    window.addEventListener('resize', this.onWindowResize, {passive: true});\n  }\n\n  private cleanUpGlobalEventListeners() {\n    document.removeEventListener('click', this.onDocumentClick, {\n      capture: true,\n    });\n    window.removeEventListener('pointerdown', this.onWindowPointerdown);\n    document.removeEventListener('resize', this.onWindowResize);\n    window.removeEventListener('resize', this.onWindowResize);\n  }\n\n  private readonly onWindowPointerdown = (event: PointerEvent) => {\n    this.pointerPath = event.composedPath();\n  };\n\n  /**\n   * We cannot listen to window click because Safari on iOS will not bubble a\n   * click event on window if the item clicked is not a \"clickable\" item such as\n   * <body>\n   */\n  private readonly onDocumentClick = (event: Event) => {\n    if (!this.open) {\n      return;\n    }\n\n    const path = event.composedPath();\n\n    if (\n      !this.stayOpenOnOutsideClick &&\n      !path.includes(this) &&\n      !path.includes(this.anchorElement!)\n    ) {\n      this.open = false;\n    }\n  };\n\n  private onCloseMenu() {\n    this.close();\n  }\n\n  private onDeactivateItems(event: Event) {\n    event.stopPropagation();\n    this.listController.onDeactivateItems();\n  }\n\n  private onRequestActivation(event: Event) {\n    event.stopPropagation();\n    this.listController.onRequestActivation(event);\n  }\n\n  private handleDeactivateTypeahead(event: DeactivateTypeaheadEvent) {\n    // stopPropagation so that this does not deactivate any typeaheads in menus\n    // nested above it e.g. md-sub-menu\n    event.stopPropagation();\n    this.typeaheadActive = false;\n  }\n\n  private handleActivateTypeahead(event: ActivateTypeaheadEvent) {\n    // stopPropagation so that this does not activate any typeaheads in menus\n    // nested above it e.g. md-sub-menu\n    event.stopPropagation();\n    this.typeaheadActive = true;\n  }\n\n  private handleStayOpenOnFocusout(event: Event) {\n    event.stopPropagation();\n    this.stayOpenOnFocusout = true;\n  }\n\n  private handleCloseOnFocusout(event: Event) {\n    event.stopPropagation();\n    this.stayOpenOnFocusout = false;\n  }\n\n  close() {\n    this.open = false;\n    const maybeSubmenu = this.slotItems as Array<\n      HTMLElement & {close?: () => void}\n    >;\n    maybeSubmenu.forEach((item) => {\n      item.close?.();\n    });\n  }\n\n  show() {\n    this.open = true;\n  }\n\n  /**\n   * Activates the next item in the menu. If at the end of the menu, the first\n   * item will be activated.\n   *\n   * @return The activated menu item or `null` if there are no items.\n   */\n  activateNextItem() {\n    return this.listController.activateNextItem() ?? null;\n  }\n\n  /**\n   * Activates the previous item in the menu. If at the start of the menu, the\n   * last item will be activated.\n   *\n   * @return The activated menu item or `null` if there are no items.\n   */\n  activatePreviousItem() {\n    return this.listController.activatePreviousItem() ?? null;\n  }\n\n  /**\n   * Repositions the menu if it is open.\n   *\n   * Useful for the case where document or window-positioned menus have their\n   * anchors moved while open.\n   */\n  reposition() {\n    if (this.open) {\n      this.menuPositionController.position();\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BO,IAAM,SAAS;EACpB,WAAW;EACX,SAAS;EACT,aAAa;EACb,WAAW;;AAyGP,IAAO,4BAAP,MAAgC;;;;;;EAgBpC,YACmB,MACA,eAAwD;AADxD,SAAA,OAAA;AACA,SAAA,gBAAA;AAhBX,SAAA,wBAAmC;MACzC,WAAW;;AAIL,SAAA,aAAkD;MACxD,QAAQ;;AAYR,SAAK,KAAK,cAAc,IAAI;EAC9B;;;;EAKA,IAAI,gBAAa;AACf,WAAO,KAAK;EACd;;;;;;;EAQA,MAAM,WAAQ;AACZ,UAAM,EACJ,WACA,UACA,cAAc,iBACd,eAAe,kBACf,aACA,SACA,SACA,kBACA,mBACA,mBAAkB,IAChB,KAAK,cAAa;AACtB,UAAM,eAAe,gBAAgB,YAAW,EAAG,KAAI;AACvD,UAAM,gBAAgB,iBAAiB,YAAW,EAAG,KAAI;AAEzD,QAAI,CAAC,aAAa,CAAC,UAAU;AAC3B;IACF;AAIA,UAAM,mBAAmB,OAAO;AAChC,UAAM,oBAAoB,OAAO;AAEjC,UAAM,MAAM,SAAS,cAAc,KAAK;AACxC,QAAI,MAAM,UAAU;AACpB,QAAI,MAAM,WAAW;AACrB,QAAI,MAAM,UAAU;AACpB,QAAI,MAAM,QAAQ;AAClB,aAAS,KAAK,YAAY,GAAG;AAC7B,UAAM,oBAAoB,IAAI,sBAAqB;AACnD,QAAI,OAAM;AAIV,UAAM,uBAAuB,OAAO,cAAc,kBAAkB;AACpE,UAAM,uBAAuB,OAAO,aAAa,kBAAkB;AAInE,SAAK,wBAAwB;MAC3B,WAAW;MACX,WAAW;;AAIb,SAAK,KAAK,cAAa;AACvB,UAAM,KAAK,KAAK;AAMhB,QACG,UAA2C,WAC5C,UAAU,aACV;AACC,gBAAmD,YAAW;IACjE;AAEA,UAAM,cAAc,UAAU,+BAC1B,UAAU,6BAA4B,IACtC,UAAU,sBAAqB;AACnC,UAAM,aAAa,SAAS,+BACxB,SAAS,6BAA4B,IACrC,SAAS,sBAAqB;AAClC,UAAM,CAAC,cAAc,aAAa,IAAI,cAAc,MAAM,GAAG;AAG7D,UAAM,CAAC,aAAa,YAAY,IAAI,aAAa,MAAM,GAAG;AAK1D,UAAM,QACJ,iBAAiB,SAAwB,EAAE,cAAc;AA0C3D,QAAI,EAAC,YAAY,4BAA4B,qBAAoB,IAC/D,KAAK,eAAe;MAClB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;KACD;AAIH,QAAI,8BAA8B,CAAC,kBAAkB;AACnD,YAAM,sBAAsB,iBAAiB,UAAU,QAAQ;AAC/D,YAAM,qBAAqB,gBAAgB,UAAU,QAAQ;AAE7D,YAAM,eAAe,KAAK,eAAe;QACvC;QACA;QACA,aAAa;QACb,cAAc;QACd;QACA;QACA;QACA;OACD;AAID,UACE,6BAA6B,aAAa,4BAC1C;AACA,qBAAa,aAAa;AAC1B,qCAA6B,aAAa;AAC1C,+BAAuB,aAAa;MACtC;IACF;AAGA,QAAI,EAAC,aAAa,6BAA6B,sBAAqB,IAClE,KAAK,gBAAgB;MACnB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;KACD;AAIH,QAAI,+BAA+B,CAAC,mBAAmB;AACrD,YAAM,uBAAuB,kBAAkB,UAAU,QAAQ;AACjE,YAAM,sBAAsB,iBAAiB,UAAU,QAAQ;AAE/D,YAAM,gBAAgB,KAAK,gBAAgB;QACzC;QACA;QACA,cAAc;QACd,eAAe;QACf;QACA;QACA;QACA;QACA;OACD;AAID,UACE,KAAK,IAAI,2BAA2B,IACpC,KAAK,IAAI,cAAc,2BAA2B,GAClD;AACA,sBAAc,cAAc;AAC5B,sCAA8B,cAAc;AAC5C,gCAAwB,cAAc;MACxC;IACF;AAIA,QAAI,uBAAuB,QAAQ;AACjC,mBAAa,aAAa;AAC1B,oBAAc,cAAc;IAC9B;AAEA,SAAK,wBAAwB;MAC3B,WAAW;MACX,WAAW;MACX,CAAC,oBAAoB,GAAG,GAAG,UAAU;MACrC,CAAC,qBAAqB,GAAG,GAAG,WAAW;;AAKzC,QAAI,uBAAuB,UAAU;AAEnC,UAAI,4BAA4B;AAC9B,aAAK,sBAAsB,QAAQ,IAAI,GACrC,YAAY,SAAS,0BACvB;MACF;AAGA,UAAI,6BAA6B;AAC/B,aAAK,sBAAsB,OAAO,IAAI,GACpC,YAAY,QAAQ,2BACtB;MACF;IACF;AAEA,SAAK,KAAK,cAAa;EACzB;;;;;EAMQ,eAAe,QAStB;AACC,UAAM,EACJ,aACA,YACA,aACA,cACA,SACA,aACA,mBACA,qBAAoB,IAClB;AAGJ,UAAM,mBACJ,gBAAgB,WAAW,gBAAgB,aAAa,IAAI;AAC9D,UAAM,qBAAqB,gBAAgB,aAAa,IAAI;AAC5D,UAAM,sBAAsB,iBAAiB,UAAU,IAAI;AAC3D,UAAM,oBAAoB,iBAAiB,QAAQ,IAAI;AACvD,UAAM,gBAAgB,gBAAgB,eAAe,IAAI;AAGzD,UAAM,oBAAoB,gBAAgB,WAAW,SAAS;AAE9D,UAAM,sBACJ,sBAAsB,WAAW,MACjC,qBACG,oBAAoB,WAAW,SAAS;AAC7C,UAAM,sBACJ,sBAAsB,OAAO,UAAU,oBAAoB,OAAO;AAIpE,UAAM,6BAA6B,KAAK,IACtC,KAAK,IACH,GACA,oBACE,sBACA,oBACA,YAAY,MAAM,CACrB;AAIH,UAAM,aACJ,mBAAmB,sBACnB,qBAAqB,sBACrB;AAEF,UAAM,uBACJ,iBAAiB,UAAU,sBAAsB;AAEnD,WAAO,EAAC,YAAY,4BAA4B,qBAAoB;EACtE;;;;;EAMQ,gBAAgB,QAUvB;AACC,UAAM,EACJ,OAAO,WACP,eACA,cACA,YACA,aACA,SACA,aACA,kBACA,qBAAoB,IAClB;AAGJ,UAAM,mBACJ,gBAAgB,WAAW,gBAAgB,aAAa,IAAI;AAC9D,UAAM,qBAAqB,gBAAgB,aAAa,IAAI;AAC5D,UAAM,QAAQ,YAAY,IAAI;AAC9B,UAAM,QAAQ,YAAY,IAAI;AAC9B,UAAM,uBAAuB,kBAAkB,UAAU,IAAI;AAC7D,UAAM,qBAAqB,kBAAkB,QAAQ,IAAI;AACzD,UAAM,iBAAiB,iBAAiB,gBAAgB,IAAI;AAG5D,UAAM,qBAAqB,iBAAiB,WAAW,QAAQ;AAE/D,UAAM,0BACJ,uBAAuB,WAAW,OAClC,sBACG,mBAAmB,WAAW,QAAQ;AAE3C,UAAM,0BACJ,wBACG,mBAAmB,WAAW,QAAQ,wBACzC,qBAAqB,WAAW;AAElC,UAAM,uBACJ,QAAQ,0BAA0B,QAAQ;AAG5C,UAAM,0BACJ,uBAAuB,OAAO,UAC9B,qBAAqB,OAAO;AAE9B,UAAM,0BACJ,qBAAqB,OAAO,UAC5B,uBAAuB,OAAO;AAEhC,UAAM,uBACJ,QAAQ,0BAA0B,QAAQ;AAI5C,UAAM,8BAA8B,KAAK,IACvC,KAAK,IACH,GACA,mBACE,uBACA,qBACA,YAAY,KAAK,CACpB;AAIH,UAAM,cACJ,mBAAmB,uBACnB,qBACA,qBAAqB;AAEvB,QAAI,wBACF,kBAAkB,UAAU,uBAAuB;AAIrD,QAAI,gBAAgB,cAAc,gBAAgB,SAAS;AACzD,UACG,kBAAkB,WAAW,aAC7B,kBAAkB,SAAS,CAAC,WAC7B;AACA,gCAAwB;MAC1B,OAAO;AACL,gCAAwB;MAC1B;IACF;AAEA,WAAO;MACL;MACA;MACA;;EAEJ;EAEA,aAAU;AACR,SAAK,SAAQ;EACf;EAEA,cAAW;AACT,SAAK,SAAQ;EACf;;;;;;EAOQ,MAAM,WAAQ;AACpB,UAAM,QAAQ,KAAK,cAAa;AAChC,QAAI,aAAa;AACjB,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,KAAK,GAAG;AAEhD,mBAAa,cAAc,UAAW,KAAK,WAAmB,GAAG;AACjE,UAAI;AAAY;IAClB;AAEA,UAAM,cAAc,KAAK,WAAW,WAAW,MAAM;AACrD,UAAM,YAAY,CAAC,CAAC,MAAM;AAC1B,UAAM,aAAa,CAAC,CAAC,MAAM;AAE3B,QAAI,cAAc,aAAa,YAAY;AAIzC,WAAK,WAAW,SAAS,MAAM;AAE/B,UAAI,MAAM,QAAQ;AAGhB,aAAK,aAAa;AAElB,cAAM,KAAK,SAAQ;AACnB,cAAM,OAAM;MACd,WAAW,aAAa;AACtB,cAAM,MAAM,YAAW;AACvB,aAAK,MAAK;AACV,cAAM,QAAO;MACf;IACF;EACF;;;;EAKQ,QAAK;AACX,SAAK,wBAAwB;MAC3B,WAAW;;AAEb,SAAK,KAAK,cAAa;AACvB,UAAM,YAAY,KAAK,cAAa,EAAG;AAIvC,QACG,WAA6C,WAC9C,WAAW,aACX;AACC,gBAAmD,YAAW;IACjE;EACF;;;;ACnmBK,IAAM,mBAAmB;EAC9B,OAAO;EACP,MAAM;EACN,MAAM;;AAgCF,IAAO,sBAAP,MAA0B;;;;;;;;;;;EAgC9B,YACmB,eAAkD;AAAlD,SAAA,gBAAA;AA7BX,SAAA,mBAAsC,CAAA;AAItC,SAAA,iBAAiB;AAIjB,SAAA,yBAAyB;AAIjC,SAAA,gBAAgB;AAIhB,SAAA,mBAA2C;AA8BlC,SAAA,YAAY,CAAC,UAAwB;AAC5C,UAAI,KAAK,eAAe;AACtB,aAAK,UAAU,KAAK;MACtB,OAAO;AACL,aAAK,eAAe,KAAK;MAC3B;IACF;AAyLiB,SAAA,eAAe,MAAK;AACnC,WAAK,gBAAgB;AACrB,WAAK,iBAAiB;AACtB,WAAK,mBAAmB,CAAA;IAC1B;EAnNG;EAEH,IAAY,QAAK;AACf,WAAO,KAAK,cAAa,EAAG,SAAQ;EACtC;EAEA,IAAY,SAAM;AAChB,WAAO,KAAK,cAAa,EAAG;EAC9B;;;;EAmBQ,eAAe,OAAoB;AACzC,QAAI,CAAC,KAAK,QAAQ;AAChB;IACF;AAKA,QACE,MAAM,SAAS,WACf,MAAM,SAAS,WACf,MAAM,KAAK,WAAW,OAAO,KAC7B,MAAM,SAAS,UACf;AACA;IACF;AAEA,SAAK,gBAAgB;AAGrB,SAAK,mBAAmB,KAAK,MAAM,IAAI,CAAC,IAAI,UAAU;MACpD;MACA;MACA,GAAG,cAAc,KAAI,EAAG,YAAW;KACpC;AACD,SAAK,mBACH,KAAK,iBAAiB,KACpB,CAAC,WAAW,OAAO,iBAAiB,IAAI,EAAE,aAAa,CAAC,KACrD;AACP,QAAI,KAAK,kBAAkB;AACzB,WAAK,iBAAiB,iBAAiB,IAAI,EAAE,WAAW;IAC1D;AACA,SAAK,UAAU,KAAK;EACtB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAsCQ,UAAU,OAAoB;AACpC,QAAI,MAAM;AAAkB;AAC5B,iBAAa,KAAK,sBAAsB;AAGxC,QACE,MAAM,SAAS,WACf,MAAM,KAAK,WAAW,OAAO,KAC7B,MAAM,SAAS,UACf;AACA,WAAK,aAAY;AACjB,UAAI,KAAK,kBAAkB;AACzB,aAAK,iBAAiB,iBAAiB,IAAI,EAAE,WAAW;MAC1D;AACA;IACF;AAGA,QAAI,MAAM,SAAS,SAAS;AAC1B,YAAM,eAAc;IACtB;AAGA,SAAK,yBAAyB,WAC5B,KAAK,cACL,KAAK,cAAa,EAAG,mBAAmB;AAG1C,SAAK,kBAAkB,MAAM,IAAI,YAAW;AAE5C,UAAM,kBAAkB,KAAK,mBACzB,KAAK,iBAAiB,iBAAiB,KAAK,IAC5C;AACJ,UAAM,aAAa,KAAK,iBAAiB;AAwBzC,UAAM,sBAAsB,CAAC,WAA2B;AACtD,cACG,OAAO,iBAAiB,KAAK,IAAI,aAAa,mBAC/C;IAEJ;AAGA,UAAM,kBAAkB,KAAK,iBAC1B,OACC,CAAC,WACC,CAAC,OAAO,iBAAiB,IAAI,EAAE,YAC/B,OAAO,iBAAiB,IAAI,EAAE,WAAW,KAAK,cAAc,CAAC,EAEhE,KAAK,CAAC,GAAG,MAAM,oBAAoB,CAAC,IAAI,oBAAoB,CAAC,CAAC;AAKjE,QAAI,gBAAgB,WAAW,GAAG;AAChC,mBAAa,KAAK,sBAAsB;AACxC,UAAI,KAAK,kBAAkB;AACzB,aAAK,iBAAiB,iBAAiB,IAAI,EAAE,WAAW;MAC1D;AACA,WAAK,aAAY;AACjB;IACF;AAEA,UAAM,aAAa,KAAK,eAAe,WAAW;AAClD,QAAI;AAIJ,QAAI,KAAK,qBAAqB,gBAAgB,CAAC,KAAK,YAAY;AAC9D,mBAAa,gBAAgB,CAAC,KAAK,gBAAgB,CAAC;IACtD,OAAO;AACL,mBAAa,gBAAgB,CAAC;IAChC;AAEA,QAAI,KAAK,kBAAkB;AACzB,WAAK,iBAAiB,iBAAiB,IAAI,EAAE,WAAW;IAC1D;AAEA,SAAK,mBAAmB;AACxB,eAAW,iBAAiB,IAAI,EAAE,WAAW;AAC7C,eAAW,iBAAiB,IAAI,EAAE,MAAK;AACvC;EACF;;;;ACvQK,IAAM,gCAAgC;AAE7C,IAAM,iBAAiB,oBAAI,IAAY;EACrC,cAAc;EACd,cAAc;EACd,cAAc;EACd,cAAc;CACf;AAED,IAAM,cAAc,oBAAI,IAAY;EAClC,cAAc;EACd,cAAc;EACd,GAAG;CACJ;AASD,SAAS,kBACP,YAAmC,UAAQ;AAE3C,MAAI,WAAW,UAAU;AAIzB,SAAO,YAAY,UAAU,YAAY,eAAe;AACtD,eAAW,SAAS,WAAW;EACjC;AAEA,SAAO;AACT;AAQM,IAAgB,OAAhB,cAA6B,WAAU;;;;;EAkO3C,IAAY,gBAAa;AACvB,UAAM,kBAAkB,KAAK,WAAW,MAAM,GAAG,EAAE,CAAC;AACpD,WAAO,oBAAoB,UAAU,SAAS;EAChD;;;;;;EAyBA,IAAI,gBAAa;AACf,QAAI,KAAK,QAAQ;AACf,aAAQ,KAAK,YAAW,EAA6B,cACnD,IAAI,KAAK,MAAM,EAAE;IAErB;AAEA,WAAO,KAAK;EACd;EAEA,IAAI,cACF,SAA8D;AAE9D,SAAK,uBAAuB;AAC5B,SAAK,cAAc,eAAe;EACpC;EAMA,cAAA;AACE,UAAK;AAzQK,SAAA,SAAS;AAuCT,SAAA,cACV;AAIyB,SAAA,QAAQ;AASmB,SAAA,cAAc;AAK1B,SAAA,OAAO;AASA,SAAA,UAAU;AAOV,SAAA,UAAU;AAKC,SAAA,mBAC1D;AAKwD,SAAA,iBACxD;AAMF,SAAA,iBAAiB;AAUjB,SAAA,eAAuB,OAAO;AASQ,SAAA,aAAqB,OAAO;AAQlE,SAAA,yBAAyB;AAQzB,SAAA,qBAAqB;AAMrB,SAAA,mBAAmB;AASnB,SAAA,eAA2B,WAAW;AAQtC,SAAA,mBAAmB;AAGF,SAAA,kBAAkB;AAQnC,SAAA,YAAY;AAKJ,SAAA,cAA6B,CAAA;AAK7B,SAAA,kBAAkB;AACT,SAAA,2BAA2B,sBAAqB;AAEhD,SAAA,iBAAiB,IAAI,eAAyB;MAC7D,QAAQ,CAAC,cAAiD;AACxD,eAAO,UAAU,aAAa,cAAc;MAC9C;MACA,kBAAkB,MAAM,KAAK;MAC7B,OAAO,MAAM,iBAAiB,IAAI,EAAE,cAAc;MAClD,gBAAgB,CAAC,SAAkB;AACjC,aAAK,WAAW;AAChB,aAAK,WAAW;MAClB;MACA,cAAc,CAAC,SAAkB;AAC/B,aAAK,WAAW;AAChB,aAAK,WAAW;MAClB;MACA,gBAAgB,CAAC,QAAe;AAC9B,YAAI,CAAC,KAAK,WAAW;AACnB,iBAAO,YAAY,IAAI,GAAG;QAC5B;AAEA,cAAM,QAAQ,iBAAiB,IAAI,EAAE,cAAc;AAKnD,cAAM,YAAY,QACd,cAAc,YACd,cAAc;AAElB,YAAI,QAAQ,WAAW;AACrB,iBAAO;QACT;AAEA,eAAO,eAAe,IAAI,GAAG;MAC/B;MACA,gBAAgB,MAAM,CAAC,KAAK;KAC7B;AAcO,SAAA,qBAAyC;AAKjD,SAAA,sBAAsB,IAAI,oBAAoB,MAAK;AACjD,aAAO;QACL,UAAU,MAAM,KAAK;QACrB,qBAAqB,KAAK;QAC1B,QAAQ,KAAK;;IAEjB,CAAC;AAEO,SAAA,uBAA2C;AAwBlC,SAAA;IAEd,KAAqB,gBAAe;AAmBtB,SAAA,yBAAyB,IAAI,0BAC5C,MACA,MAAK;AACH,aAAO;QACL,cAAc,KAAK;QACnB,eAAe,KAAK;QACpB,WAAW,KAAK;QAChB,UAAU,KAAK;QACf,aACE,KAAK,gBAAgB,YAAY,aAAa,KAAK;QACrD,QAAQ,KAAK;QACb,SAAS,KAAK;QACd,SAAS,KAAK;QACd,kBAAkB,KAAK;QACvB,mBAAmB,KAAK;QACxB,QAAQ,KAAK;QACb,aAAa,KAAK;QAClB,SAAS,KAAK;;;;;;QAMd,oBACE,KAAK,eAAe,KAAK,gBAAgB,YACrC,SACA;;IAEV,CAAC;AA8Cc,SAAA,iBAAiB,MAAK;AACrC,UACE,KAAK,mBACJ,KAAK,gBAAgB,cACpB,KAAK,gBAAgB,WACrB,KAAK,gBAAgB,WACvB;AACA;MACF;AACA,WAAK,kBAAkB;AACvB,WAAK,WAAU;AACf,WAAK,kBAAkB;IACzB;AA+EiB,SAAA,iBAAiB,OAAO,UAAqB;AAC5D,YAAM,WAAW,KAAK;AAItB,UACE,KAAK,sBACL,CAAC,KAAK,QACN,KAAK,YAAY,SAAS,QAAQ,GAClC;AACA;MACF;AAEA,UAAI,MAAM,eAAe;AAKvB,YACE,mBAAmB,MAAM,eAAe,IAAI,KAC3C,KAAK,YAAY,WAAW,KAC3B,mBAAmB,MAAM,eAAe,QAAQ,GAClD;AACA;QACF;MACF,WAAW,KAAK,YAAY,SAAS,IAAI,GAAG;AAG1C;MACF;AAEA,YAAM,kBAAkB,KAAK;AAE7B,WAAK,mBAAmB;AACxB,WAAK,MAAK;AAEV,YAAM,KAAK;AAEX,WAAK,mBAAmB;IAC1B;AAmBiB,SAAA,WAAW,YAAW;AACrC,WAAK,qBAAqB,kBAAiB;AAE3C,YAAM,QAAQ,KAAK;AACnB,YAAM,mBAAmB,cAAc,KAAK;AAE5C,UAAI,oBAAoB,KAAK,iBAAiB,WAAW,MAAM;AAC7D,yBAAiB,KAAK,WAAW;MACnC;AAEA,UAAI,mBAAmB,CAAC,KAAK;AAE7B,UAAI,KAAK,OAAO;AACd,aAAK,cAAc,IAAI,MAAM,SAAS,CAAC;MACzC,OAAO;AACL,2BAAmB,CAAC,CAAE,MAAM,KAAK,YAAW;MAC9C;AAKA,cAAQ,KAAK,cAAc;QACzB,KAAK,WAAW;AACd,gBAAM,QAAQ,wBAAwB,KAAK;AAC3C,cAAI,OAAO;AACT,kBAAM,WAAW;AACjB,kBAAM,MAAK;AACX,kBAAO,MAAgC;UACzC;AACA;QACF,KAAK,WAAW;AACd,gBAAM,OAAO,uBAAuB,KAAK;AACzC,cAAI,MAAM;AACR,iBAAK,WAAW;AAChB,iBAAK,MAAK;AACV,kBAAO,KAA+B;UACxC;AACA;QACF,KAAK,WAAW;AACd,eAAK,MAAK;AACV;QACF;QACA,KAAK,WAAW;AAEd;MACJ;AAEA,UAAI,CAAC,kBAAkB;AACrB,aAAK,cAAc,IAAI,MAAM,QAAQ,CAAC;MACxC;IACF;AAKiB,SAAA,cAAc,YAAW;AACxC,WAAK,OAAO;AAEZ,UAAI,CAAC,KAAK,kBAAkB;AAC1B,aAAK,oBAAoB,QAAO;MAClC;AAEA,UAAI,CAAC,KAAK,OAAO;AACf,cAAM,KAAK,aAAY;MACzB;IACF;AAKiB,SAAA,WAAW,MAAK;AAC/B,UAAI,KAAK,OAAO;AACd,aAAK,cAAc,IAAI,MAAM,SAAS,CAAC;AACvC,aAAK,cAAc,IAAI,MAAM,QAAQ,CAAC;MACxC;IACF;AAyPiB,SAAA,sBAAsB,CAAC,UAAuB;AAC7D,WAAK,cAAc,MAAM,aAAY;IACvC;AAOiB,SAAA,kBAAkB,CAAC,UAAgB;AAClD,UAAI,CAAC,KAAK,MAAM;AACd;MACF;AAEA,YAAM,OAAO,MAAM,aAAY;AAE/B,UACE,CAAC,KAAK,0BACN,CAAC,KAAK,SAAS,IAAI,KACnB,CAAC,KAAK,SAAS,KAAK,aAAc,GAClC;AACA,aAAK,OAAO;MACd;IACF;AAzkBE,QAAI,CAAC,UAAU;AACb,WAAK,UAAU,OAAO;AACtB,WAAK,iBAAiB,WAAW,KAAK,aAAa;AAInD,WAAK,iBAAiB,WAAW,KAAK,gBAAgB,EAAC,SAAS,KAAI,CAAC;AACrE,WAAK,iBAAiB,YAAY,KAAK,cAAc;IACvD;EACF;;;;;EAyCA,IAAI,QAAK;AACP,WAAO,KAAK,eAAe;EAC7B;EAEmB,WAAW,SAA6B;AACzD,QAAI,CAAC,QAAQ,IAAI,MAAM,GAAG;AACxB;IACF;AAEA,QAAI,KAAK,MAAM;AACb,WAAK,gBAAgB,aAAa;AAClC;IACF;AAEA,SAAK,aAAa,eAAe,MAAM;EACzC;EAES,OAAO,SAA6B;AAC3C,QAAI,QAAQ,IAAI,MAAM,GAAG;AACvB,UAAI,KAAK,MAAM;AACb,aAAK,0BAAyB;MAChC,OAAO;AACL,aAAK,4BAA2B;MAClC;IACF;AAGA,QACE,QAAQ,IAAI,aAAa,KACzB,KAAK,gBAAgB;IAErB,CAAE,KAA+C,aACjD;AACA,WAAK,cAAc;IACrB;AAEA,UAAM,OAAO,OAAO;EACtB;EAgBS,oBAAiB;AACxB,UAAM,kBAAiB;AACvB,QAAI,KAAK,MAAM;AACb,WAAK,0BAAyB;IAChC;EACF;EAES,uBAAoB;AAC3B,UAAM,qBAAoB;AAC1B,SAAK,4BAA2B;EAClC;EAES,wBAAqB;AAC5B,QAAI,CAAC,KAAK,WAAW;AACnB,aAAO,MAAM,sBAAqB;IACpC;AACA,WAAO,KAAK,UAAU,sBAAqB;EAC7C;EAES,iBAAc;AACrB,QAAI,CAAC,KAAK,WAAW;AACnB,aAAO,MAAM,eAAc;IAC7B;AACA,WAAO,KAAK,UAAU,eAAc;EACtC;EAEmB,SAAM;AACvB,WAAO,KAAK,cAAa;EAC3B;;;;EAKQ,gBAAa;AACnB,WAAO;;sBAEW,SAAS,KAAK,kBAAiB,CAAE,CAAC;gBACxC,SAAS,KAAK,uBAAuB,aAAa,CAAC;kBACjD,KAAK,gBAAgB,YAAY,WAAW,OAAO;UAC3D,KAAK,gBAAe,CAAE;;uCAEO,KAAK,gBAAe,CAAE;;;;EAI3D;;;;EAKQ,kBAAe;AACrB,WAAO;oBACS,KAAK,WAAW;0BACV,KAAK,iBAAiB;4BACpB,KAAK,mBAAmB;8BACtB,KAAK,yBAAyB;4BAChC,KAAK,uBAAuB;+BACzB,KAAK,wBAAwB;2BACjC,KAAK,qBAAqB;oBACjC,KAAK,eAAe,YAAY;EAClD;;;;EAKQ,kBAAe;AACrB,WAAO;EACT;EAEQ,oBAAiB;AACvB,WAAO;MACL,MAAM,KAAK;MACX,OAAO,KAAK,gBAAgB;MAC5B,gBAAgB,KAAK;;EAEzB;EA2CQ,eAAe,OAAoB;AACzC,QACE,MAAM,WAAW,QACjB,CAAC,MAAM,oBACP,cAAc,MAAM,IAAI,GACxB;AACA,YAAM,eAAc;AACpB,WAAK,MAAK;IACZ;AAEA,SAAK,oBAAoB,UAAU,KAAK;EAC1C;;;;;;;;;EA2FQ,MAAM,cAAW;AACvB,UAAM,YAAY,KAAK;AACvB,UAAM,SAAS,KAAK;AAEpB,QAAI,CAAC,aAAa,CAAC;AAAQ,aAAO;AAElC,UAAM,gBAAgB,KAAK;AAC3B,SAAK,cAAc,IAAI,MAAM,SAAS,CAAC;AAGvC,cAAU,UAAU,OAAO,aAAa,IAAI;AAE5C,UAAM,SAAS,KAAK,yBAAyB,MAAK;AAClD,UAAM,SAAS,UAAU;AACzB,UAAM,iBAAiB,kBAAkB;AACzC,UAAM,WAAW,KAAK;AACtB,UAAM,gBAAgB;AACtB,UAAM,2BAA2B;AACjC,UAAM,wBAAwB;AAG9B,UAAM,uBACH,gBAAgB,yBAAyB,SAAS;AAErD,UAAM,yBAAyB,UAAU,QACvC,CAAC,EAAC,QAAQ,MAAK,GAAG,EAAC,QAAQ,GAAG,MAAM,KAAI,CAAC,GACzC;MACE,UAAU;MACV,QAAQ,OAAO;KAChB;AAKH,UAAM,gCAAgC,OAAO,QAC3C;MACE,EAAC,WAAW,iBAAiB,eAAe,MAAM,QAAQ,GAAE;MAC5D,EAAC,WAAW,GAAE;OAEhB,EAAC,UAAU,eAAe,QAAQ,OAAO,WAAU,CAAC;AAGtD,UAAM,0BAA0B,UAAU,QACxC,CAAC,EAAC,SAAS,EAAC,GAAG,EAAC,SAAS,EAAC,CAAC,GAC3B,wBAAwB;AAG1B,UAAM,qBAAsD,CAAA;AAE5D,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AAExC,YAAM,mBAAmB,iBAAiB,SAAS,SAAS,IAAI,IAAI;AACpE,YAAM,QAAQ,SAAS,gBAAgB;AACvC,YAAM,YAAY,MAAM,QAAQ,CAAC,EAAC,SAAS,EAAC,GAAG,EAAC,SAAS,EAAC,CAAC,GAAG;QAC5D,UAAU;QACV,OAAO,sBAAsB;OAC9B;AAID,YAAM,UAAU,OAAO,kBAAkB,IAAI;AAC7C,gBAAU,iBAAiB,UAAU,MAAK;AACxC,cAAM,UAAU,OAAO,kBAAkB,KAAK;MAChD,CAAC;AAED,yBAAmB,KAAK,CAAC,OAAO,SAAS,CAAC;IAC5C;AAEA,QAAI,mBAAmB,CAAC,UAAkB;IAAE;AAC5C,UAAM,oBAAoB,IAAI,QAAiB,CAAC,YAAW;AACzD,yBAAmB;IACrB,CAAC;AAED,WAAO,iBAAiB,SAAS,MAAK;AACpC,6BAAuB,OAAM;AAC7B,oCAA8B,OAAM;AACpC,8BAAwB,OAAM;AAC9B,yBAAmB,QAAQ,CAAC,CAAC,OAAO,SAAS,MAAK;AAChD,cAAM,UAAU,OAAO,kBAAkB,KAAK;AAC9C,kBAAU,OAAM;MAClB,CAAC;AAED,uBAAiB,IAAI;IACvB,CAAC;AAED,2BAAuB,iBAAiB,UAAU,MAAK;AACrD,gBAAU,UAAU,OAAO,aAAa,KAAK;AAC7C,WAAK,yBAAyB,OAAM;AACpC,uBAAiB,KAAK;IACxB,CAAC;AAED,WAAO,MAAM;EACf;;;;;;EAOQ,eAAY;AAClB,QAAI;AAIJ,UAAM,iBAAiB,IAAI,QAAQ,CAAC,QAAO;AACzC,gBAAU;IACZ,CAAC;AAED,UAAM,YAAY,KAAK;AACvB,UAAM,SAAS,KAAK;AAEpB,QAAI,CAAC,aAAa,CAAC,QAAQ;AACzB,cAAQ,KAAK;AACb,aAAO;IACT;AAEA,UAAM,gBAAgB,KAAK;AAC3B,UAAM,mBAAmB,kBAAkB;AAC3C,SAAK,cAAc,IAAI,MAAM,SAAS,CAAC;AAGvC,cAAU,UAAU,OAAO,aAAa,IAAI;AAC5C,UAAM,SAAS,KAAK,yBAAyB,MAAK;AAClD,UAAM,SAAS,UAAU;AACzB,UAAM,WAAW,KAAK;AACtB,UAAM,gBAAgB;AACtB,UAAM,2BAA2B;AAEjC,UAAM,wBAAwB,gBAAgB;AAC9C,UAAM,wBAAwB;AAC9B,UAAM,6BAA6B;AACnC,UAAM,wBAAwB;AAI9B,UAAM,uBACH,gBAAgB,6BAA6B,yBAC9C,SAAS;AAGX,UAAM,yBAAyB,UAAU,QACvC;MACE,EAAC,QAAQ,GAAG,MAAM,KAAI;MACtB,EAAC,QAAQ,GAAG,SAAS,qBAAqB,KAAI;OAEhD;MACE,UAAU;MACV,QAAQ,OAAO;KAChB;AAMH,UAAM,kCAAkC,OAAO,QAC7C;MACE,EAAC,WAAW,GAAE;MACd;QACE,WAAW,mBACP,eAAe,UAAU,IAAI,sBAAsB,QACnD;;OAGR,EAAC,UAAU,eAAe,QAAQ,OAAO,sBAAqB,CAAC;AAGjE,UAAM,0BAA0B,UAAU,QACxC,CAAC,EAAC,SAAS,EAAC,GAAG,EAAC,SAAS,EAAC,CAAC,GAC3B,EAAC,UAAU,0BAA0B,OAAO,sBAAqB,CAAC;AAGpE,UAAM,qBAAsD,CAAA;AAE5D,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AAGxC,YAAM,mBAAmB,mBAAmB,IAAI,SAAS,SAAS,IAAI;AACtE,YAAM,QAAQ,SAAS,gBAAgB;AACvC,YAAM,YAAY,MAAM,QAAQ,CAAC,EAAC,SAAS,EAAC,GAAG,EAAC,SAAS,EAAC,CAAC,GAAG;QAC5D,UAAU;QACV,OAAO,6BAA6B,sBAAsB;OAC3D;AAID,gBAAU,iBAAiB,UAAU,MAAK;AACxC,cAAM,UAAU,OAAO,kBAAkB,IAAI;MAC/C,CAAC;AACD,yBAAmB,KAAK,CAAC,OAAO,SAAS,CAAC;IAC5C;AAEA,WAAO,iBAAiB,SAAS,MAAK;AACpC,6BAAuB,OAAM;AAC7B,sCAAgC,OAAM;AACtC,8BAAwB,OAAM;AAC9B,yBAAmB,QAAQ,CAAC,CAAC,OAAO,SAAS,MAAK;AAChD,kBAAU,OAAM;AAChB,cAAM,UAAU,OAAO,kBAAkB,KAAK;MAChD,CAAC;AACD,cAAQ,KAAK;IACf,CAAC;AAED,2BAAuB,iBAAiB,UAAU,MAAK;AACrD,gBAAU,UAAU,OAAO,aAAa,KAAK;AAC7C,yBAAmB,QAAQ,CAAC,CAAC,KAAK,MAAK;AACrC,cAAM,UAAU,OAAO,kBAAkB,KAAK;MAChD,CAAC;AACD,WAAK,yBAAyB,OAAM;AACpC,WAAK,cAAc,IAAI,MAAM,QAAQ,CAAC;AACtC,cAAQ,IAAI;IACd,CAAC;AAED,WAAO;EACT;EAEQ,cAAc,OAAoB;AAIxC,SAAK,cAAc,CAAA;AACnB,SAAK,eAAe,cAAc,KAAK;EACzC;EAEQ,4BAAyB;AAC/B,aAAS,iBAAiB,SAAS,KAAK,iBAAiB,EAAC,SAAS,KAAI,CAAC;AACxE,WAAO,iBAAiB,eAAe,KAAK,mBAAmB;AAC/D,aAAS,iBAAiB,UAAU,KAAK,gBAAgB,EAAC,SAAS,KAAI,CAAC;AACxE,WAAO,iBAAiB,UAAU,KAAK,gBAAgB,EAAC,SAAS,KAAI,CAAC;EACxE;EAEQ,8BAA2B;AACjC,aAAS,oBAAoB,SAAS,KAAK,iBAAiB;MAC1D,SAAS;KACV;AACD,WAAO,oBAAoB,eAAe,KAAK,mBAAmB;AAClE,aAAS,oBAAoB,UAAU,KAAK,cAAc;AAC1D,WAAO,oBAAoB,UAAU,KAAK,cAAc;EAC1D;EA2BQ,cAAW;AACjB,SAAK,MAAK;EACZ;EAEQ,kBAAkB,OAAY;AACpC,UAAM,gBAAe;AACrB,SAAK,eAAe,kBAAiB;EACvC;EAEQ,oBAAoB,OAAY;AACtC,UAAM,gBAAe;AACrB,SAAK,eAAe,oBAAoB,KAAK;EAC/C;EAEQ,0BAA0B,OAA+B;AAG/D,UAAM,gBAAe;AACrB,SAAK,kBAAkB;EACzB;EAEQ,wBAAwB,OAA6B;AAG3D,UAAM,gBAAe;AACrB,SAAK,kBAAkB;EACzB;EAEQ,yBAAyB,OAAY;AAC3C,UAAM,gBAAe;AACrB,SAAK,qBAAqB;EAC5B;EAEQ,sBAAsB,OAAY;AACxC,UAAM,gBAAe;AACrB,SAAK,qBAAqB;EAC5B;EAEA,QAAK;AACH,SAAK,OAAO;AACZ,UAAM,eAAe,KAAK;AAG1B,iBAAa,QAAQ,CAAC,SAAQ;AAC5B,WAAK,QAAO;IACd,CAAC;EACH;EAEA,OAAI;AACF,SAAK,OAAO;EACd;;;;;;;EAQA,mBAAgB;AACd,WAAO,KAAK,eAAe,iBAAgB,KAAM;EACnD;;;;;;;EAQA,uBAAoB;AAClB,WAAO,KAAK,eAAe,qBAAoB,KAAM;EACvD;;;;;;;EAQA,aAAU;AACR,QAAI,KAAK,MAAM;AACb,WAAK,uBAAuB,SAAQ;IACtC;EACF;;AAj7BiC,WAAA;EAAhC,MAAM,OAAO;;AACkB,WAAA;EAA/B,MAAM,MAAM;;AASD,WAAA;EAAX,SAAQ;;AAuCG,WAAA;EAAX,SAAQ;;AAKkB,WAAA;EAA1B,SAAS,EAAC,MAAM,QAAO,CAAC;;AAS6B,WAAA;EAArD,SAAS,EAAC,MAAM,SAAS,WAAW,eAAc,CAAC;;AAKV,WAAA;EAAzC,SAAS,EAAC,MAAM,SAAS,SAAS,KAAI,CAAC;;AASS,WAAA;EAAhD,SAAS,EAAC,MAAM,QAAQ,WAAW,WAAU,CAAC;;AAOE,WAAA;EAAhD,SAAS,EAAC,MAAM,QAAQ,WAAW,WAAU,CAAC;;AAKa,WAAA;EAA3D,SAAS,EAAC,MAAM,SAAS,WAAW,qBAAoB,CAAC;;AAMA,WAAA;EAAzD,SAAS,EAAC,MAAM,SAAS,WAAW,mBAAkB,CAAC;;AAOxD,WAAA;EADC,SAAS,EAAC,MAAM,QAAQ,WAAW,kBAAiB,CAAC;;AAWtD,WAAA;EADC,SAAS,EAAC,WAAW,gBAAe,CAAC;;AAUA,WAAA;EAArC,SAAS,EAAC,WAAW,cAAa,CAAC;;AAQpC,WAAA;EADC,SAAS,EAAC,MAAM,SAAS,WAAW,6BAA4B,CAAC;;AASlE,WAAA;EADC,SAAS,EAAC,MAAM,SAAS,WAAW,wBAAuB,CAAC;;AAO7D,WAAA;EADC,SAAS,EAAC,MAAM,SAAS,WAAW,qBAAoB,CAAC;;AAU1D,WAAA;EADC,SAAS,EAAC,WAAW,gBAAe,CAAC;;AAStC,WAAA;EADC,SAAS,EAAC,MAAM,SAAS,WAAW,qBAAoB,CAAC;;AAGR,WAAA;EAAjD,sBAAsB,EAAC,SAAS,KAAI,CAAC;;AACrB,WAAA;EAAhB,MAAK;;", "names": []}