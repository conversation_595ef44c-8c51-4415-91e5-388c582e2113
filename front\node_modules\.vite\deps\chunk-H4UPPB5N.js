import {
  Chip
} from "./chunk-KFVVDR35.js";
import {
  css,
  html,
  isServer,
  nothing
} from "./chunk-4GZ3EDRH.js";

// node_modules/@material/web/chips/internal/selectable-styles.js
var styles = css`.selected{--md-ripple-hover-color: var(--_selected-hover-state-layer-color);--md-ripple-hover-opacity: var(--_selected-hover-state-layer-opacity);--md-ripple-pressed-color: var(--_selected-pressed-state-layer-color);--md-ripple-pressed-opacity: var(--_selected-pressed-state-layer-opacity)}:where(.selected)::before{background:var(--_selected-container-color)}:where(.selected) .outline{border-width:var(--_selected-outline-width)}:where(.selected.disabled)::before{background:var(--_disabled-selected-container-color);opacity:var(--_disabled-selected-container-opacity)}:where(.selected) .label{color:var(--_selected-label-text-color)}:where(.selected:hover) .label{color:var(--_selected-hover-label-text-color)}:where(.selected:focus) .label{color:var(--_selected-focus-label-text-color)}:where(.selected:active) .label{color:var(--_selected-pressed-label-text-color)}:where(.selected) .leading.icon{color:var(--_selected-leading-icon-color)}:where(.selected:hover) .leading.icon{color:var(--_selected-hover-leading-icon-color)}:where(.selected:focus) .leading.icon{color:var(--_selected-focus-leading-icon-color)}:where(.selected:active) .leading.icon{color:var(--_selected-pressed-leading-icon-color)}@media(forced-colors: active){:where(.selected:not(.elevated))::before{border:1px solid CanvasText}:where(.selected) .outline{border-width:1px}}
`;

// node_modules/@material/web/chips/internal/trailing-icon-styles.js
var styles2 = css`.trailing.action{align-items:center;justify-content:center;padding-inline-start:var(--_icon-label-space);padding-inline-end:var(--_with-trailing-icon-trailing-space)}.trailing.action :is(md-ripple,md-focus-ring){border-radius:50%;height:calc(1.3333333333*var(--_icon-size));width:calc(1.3333333333*var(--_icon-size))}.trailing.action md-focus-ring{inset:unset}.has-trailing .primary.action{padding-inline-end:0}.trailing.icon{color:var(--_trailing-icon-color);height:var(--_icon-size);width:var(--_icon-size)}:where(:hover) .trailing.icon{color:var(--_hover-trailing-icon-color)}:where(:focus) .trailing.icon{color:var(--_focus-trailing-icon-color)}:where(:active) .trailing.icon{color:var(--_pressed-trailing-icon-color)}:where(.disabled) .trailing.icon{color:var(--_disabled-trailing-icon-color);opacity:var(--_disabled-trailing-icon-opacity)}:where(.selected) .trailing.icon{color:var(--_selected-trailing-icon-color)}:where(.selected:hover) .trailing.icon{color:var(--_selected-hover-trailing-icon-color)}:where(.selected:focus) .trailing.icon{color:var(--_selected-focus-trailing-icon-color)}:where(.selected:active) .trailing.icon{color:var(--_selected-pressed-trailing-icon-color)}@media(forced-colors: active){.trailing.icon{color:ButtonText}:where(.disabled) .trailing.icon{color:GrayText;opacity:1}}
`;

// node_modules/@material/web/chips/internal/multi-action-chip.js
var ARIA_LABEL_REMOVE = "aria-label-remove";
var MultiActionChip = class extends Chip {
  get ariaLabelRemove() {
    if (this.hasAttribute(ARIA_LABEL_REMOVE)) {
      return this.getAttribute(ARIA_LABEL_REMOVE);
    }
    const { ariaLabel } = this;
    if (ariaLabel || this.label) {
      return `Remove ${ariaLabel || this.label}`;
    }
    return null;
  }
  set ariaLabelRemove(ariaLabel) {
    const prev = this.ariaLabelRemove;
    if (ariaLabel === prev) {
      return;
    }
    if (ariaLabel === null) {
      this.removeAttribute(ARIA_LABEL_REMOVE);
    } else {
      this.setAttribute(ARIA_LABEL_REMOVE, ariaLabel);
    }
    this.requestUpdate();
  }
  constructor() {
    super();
    this.handleTrailingActionFocus = this.handleTrailingActionFocus.bind(this);
    if (!isServer) {
      this.addEventListener("keydown", this.handleKeyDown.bind(this));
    }
  }
  focus(options) {
    const isFocusable = this.alwaysFocusable || !this.disabled;
    if (isFocusable && options?.trailing && this.trailingAction) {
      this.trailingAction.focus(options);
      return;
    }
    super.focus(options);
  }
  renderContainerContent() {
    return html`
      ${super.renderContainerContent()}
      ${this.renderTrailingAction(this.handleTrailingActionFocus)}
    `;
  }
  handleKeyDown(event) {
    const isLeft = event.key === "ArrowLeft";
    const isRight = event.key === "ArrowRight";
    if (!isLeft && !isRight) {
      return;
    }
    if (!this.primaryAction || !this.trailingAction) {
      return;
    }
    const isRtl = getComputedStyle(this).direction === "rtl";
    const forwards = isRtl ? isLeft : isRight;
    const isPrimaryFocused = this.primaryAction?.matches(":focus-within");
    const isTrailingFocused = this.trailingAction?.matches(":focus-within");
    if (forwards && isTrailingFocused || !forwards && isPrimaryFocused) {
      return;
    }
    event.preventDefault();
    event.stopPropagation();
    const actionToFocus = forwards ? this.trailingAction : this.primaryAction;
    actionToFocus.focus();
  }
  handleTrailingActionFocus() {
    const { primaryAction, trailingAction } = this;
    if (!primaryAction || !trailingAction) {
      return;
    }
    primaryAction.tabIndex = -1;
    trailingAction.addEventListener("focusout", () => {
      primaryAction.tabIndex = 0;
    }, { once: true });
  }
};

// node_modules/@material/web/chips/internal/trailing-icons.js
function renderRemoveButton({ ariaLabel, disabled, focusListener, tabbable = false }) {
  return html`
    <span id="remove-label" hidden aria-hidden="true">Remove</span>
    <button
      class="trailing action"
      aria-label=${ariaLabel || nothing}
      aria-labelledby=${!ariaLabel ? "remove-label label" : nothing}
      tabindex=${!tabbable ? -1 : nothing}
      @click=${handleRemoveClick}
      @focus=${focusListener}>
      <md-focus-ring part="trailing-focus-ring"></md-focus-ring>
      <md-ripple ?disabled=${disabled}></md-ripple>
      <span class="trailing icon" aria-hidden="true">
        <slot name="remove-trailing-icon">
          <svg viewBox="0 96 960 960">
            <path
              d="m249 849-42-42 231-231-231-231 42-42 231 231 231-231 42 42-231 231 231 231-42 42-231-231-231 231Z" />
          </svg>
        </slot>
      </span>
      <span class="touch"></span>
    </button>
  `;
}
function handleRemoveClick(event) {
  if (this.disabled || this.softDisabled) {
    return;
  }
  event.stopPropagation();
  const preventDefault = !this.dispatchEvent(new Event("remove", { cancelable: true }));
  if (preventDefault) {
    return;
  }
  this.remove();
}

export {
  MultiActionChip,
  renderRemoveButton,
  styles,
  styles2
};
/*! Bundled license information:

@material/web/chips/internal/selectable-styles.js:
@material/web/chips/internal/trailing-icon-styles.js:
  (**
   * @license
   * Copyright 2024 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   *)

@material/web/chips/internal/multi-action-chip.js:
@material/web/chips/internal/trailing-icons.js:
  (**
   * @license
   * Copyright 2023 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   *)
*/
//# sourceMappingURL=chunk-H4UPPB5N.js.map
