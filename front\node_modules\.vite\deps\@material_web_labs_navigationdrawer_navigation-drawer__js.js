import {
  styles
} from "./chunk-ZP5NZYYW.js";
import "./chunk-NQOB77XV.js";
import {
  mixinDelegatesAria
} from "./chunk-GXE4MBY5.js";
import {
  classMap
} from "./chunk-SZQCPKZF.js";
import {
  __decorate,
  customElement,
  property
} from "./chunk-PZNDE6JX.js";
import {
  LitElement,
  css,
  html,
  nothing
} from "./chunk-4GZ3EDRH.js";
import "./chunk-5WRI5ZAA.js";

// node_modules/@material/web/labs/navigationdrawer/internal/navigation-drawer.js
var navigationDrawerBaseClass = mixinDelegatesAria(LitElement);
var NavigationDrawer = class extends navigationDrawerBaseClass {
  constructor() {
    super(...arguments);
    this.opened = false;
    this.pivot = "end";
  }
  render() {
    const ariaExpanded = this.opened ? "true" : "false";
    const ariaHidden = !this.opened ? "true" : "false";
    const { ariaLabel, ariaModal } = this;
    return html`
      <div
        aria-expanded="${ariaExpanded}"
        aria-hidden="${ariaHidden}"
        aria-label=${ariaLabel || nothing}
        aria-modal="${ariaModal || nothing}"
        class="md3-navigation-drawer ${this.getRenderClasses()}"
        role="dialog">
        <md-elevation part="elevation"></md-elevation>
        <div class="md3-navigation-drawer__slot-content">
          <slot></slot>
        </div>
      </div>
    `;
  }
  getRenderClasses() {
    return classMap({
      "md3-navigation-drawer--opened": this.opened,
      "md3-navigation-drawer--pivot-at-start": this.pivot === "start"
    });
  }
  updated(changedProperties) {
    if (changedProperties.has("opened")) {
      setTimeout(() => {
        this.dispatchEvent(new CustomEvent("navigation-drawer-changed", {
          detail: { opened: this.opened },
          bubbles: true,
          composed: true
        }));
      }, 250);
    }
  }
};
__decorate([
  property({ type: Boolean })
], NavigationDrawer.prototype, "opened", void 0);
__decorate([
  property()
], NavigationDrawer.prototype, "pivot", void 0);

// node_modules/@material/web/labs/navigationdrawer/internal/navigation-drawer-styles.js
var styles2 = css`:host{--_container-color: var(--md-navigation-drawer-container-color, #fff);--_container-height: var(--md-navigation-drawer-container-height, 100%);--_container-shape: var(--md-navigation-drawer-container-shape, 0 16px 16px 0);--_container-width: var(--md-navigation-drawer-container-width, 360px);--_divider-color: var(--md-navigation-drawer-divider-color, #000);--_modal-container-elevation: var(--md-navigation-drawer-modal-container-elevation, 1);--_standard-container-elevation: var(--md-navigation-drawer-standard-container-elevation, 0);--md-elevation-level: var(--_standard-container-elevation);--md-elevation-shadow-color: var(--_divider-color)}:host{display:flex}.md3-navigation-drawer{inline-size:0;box-sizing:border-box;display:flex;justify-content:flex-end;overflow:hidden;overflow-y:auto;visibility:hidden;transition:inline-size .25s cubic-bezier(0.4, 0, 0.2, 1) 0s,visibility 0s cubic-bezier(0.4, 0, 0.2, 1) .25s}md-elevation{z-index:0}.md3-navigation-drawer--opened{visibility:visible;transition:inline-size .25s cubic-bezier(0.4, 0, 0.2, 1) 0s,visibility 0s cubic-bezier(0.4, 0, 0.2, 1) 0s}.md3-navigation-drawer--pivot-at-start{justify-content:flex-start}.md3-navigation-drawer__slot-content{display:flex;flex-direction:column;position:relative}
`;

// node_modules/@material/web/labs/navigationdrawer/navigation-drawer.js
var MdNavigationDrawer = class MdNavigationDrawer2 extends NavigationDrawer {
};
MdNavigationDrawer.styles = [styles, styles2];
MdNavigationDrawer = __decorate([
  customElement("md-navigation-drawer")
], MdNavigationDrawer);
export {
  MdNavigationDrawer
};
/*! Bundled license information:

@material/web/labs/navigationdrawer/internal/navigation-drawer.js:
@material/web/labs/navigationdrawer/navigation-drawer.js:
  (**
   * @license
   * Copyright 2021 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   *)

@material/web/labs/navigationdrawer/internal/navigation-drawer-styles.js:
  (**
   * @license
   * Copyright 2024 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   *)
*/
//# sourceMappingURL=@material_web_labs_navigationdrawer_navigation-drawer__js.js.map
