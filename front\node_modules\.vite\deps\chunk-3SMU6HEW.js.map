{"version": 3, "sources": ["../../@material/web/labs/behaviors/focusable.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {LitElement} from 'lit';\nimport {property} from 'lit/decorators.js';\n\nimport {MixinBase, MixinReturn} from './mixin.js';\n\n/**\n * An element that can enable and disable `tabindex` focusability.\n */\nexport interface Focusable {\n  /**\n   * Whether or not the element can be focused. Defaults to true. Set to false\n   * to disable focusing (unless a user has set a `tabindex`).\n   */\n  [isFocusable]: boolean;\n}\n\n/**\n * A property symbol that indicates whether or not a `Focusable` element can be\n * focused.\n */\nexport const isFocusable = Symbol('isFocusable');\n\nconst privateIsFocusable = Symbol('privateIsFocusable');\nconst externalTabIndex = Symbol('externalTabIndex');\nconst isUpdatingTabIndex = Symbol('isUpdatingTabIndex');\nconst updateTabIndex = Symbol('updateTabIndex');\n\n/**\n * Mixes in focusable functionality for a class.\n *\n * Elements can enable and disable their focusability with the `isFocusable`\n * symbol property. Changing `tabIndex` will trigger a lit render, meaning\n * `this.tabIndex` can be used in template expressions.\n *\n * This mixin will preserve externally-set tabindices. If an element turns off\n * focusability, but a user sets `tabindex=\"0\"`, it will still be focusable.\n *\n * To remove user overrides and restore focusability control to the element,\n * remove the `tabindex` attribute.\n *\n * @param base The class to mix functionality into.\n * @return The provided class with `Focusable` mixed in.\n */\nexport function mixinFocusable<T extends MixinBase<LitElement>>(\n  base: T,\n): MixinReturn<T, Focusable> {\n  abstract class FocusableElement extends base implements Focusable {\n    @property({noAccessor: true})\n    declare tabIndex: number;\n\n    get [isFocusable]() {\n      return this[privateIsFocusable];\n    }\n\n    set [isFocusable](value: boolean) {\n      if (this[isFocusable] === value) {\n        return;\n      }\n\n      this[privateIsFocusable] = value;\n      this[updateTabIndex]();\n    }\n\n    [privateIsFocusable] = true;\n    [externalTabIndex]: number | null = null;\n    [isUpdatingTabIndex] = false;\n\n    override connectedCallback() {\n      super.connectedCallback();\n      this[updateTabIndex]();\n    }\n\n    override attributeChangedCallback(\n      name: string,\n      old: string | null,\n      value: string | null,\n    ) {\n      if (name !== 'tabindex') {\n        super.attributeChangedCallback(name, old, value);\n        return;\n      }\n\n      this.requestUpdate('tabIndex', Number(old ?? -1));\n      if (this[isUpdatingTabIndex]) {\n        // Not an externally-initiated update.\n        return;\n      }\n\n      if (!this.hasAttribute('tabindex')) {\n        // User removed the attribute, can now use internal tabIndex\n        this[externalTabIndex] = null;\n        this[updateTabIndex]();\n        return;\n      }\n\n      this[externalTabIndex] = this.tabIndex;\n    }\n\n    [updateTabIndex]() {\n      const internalTabIndex = this[isFocusable] ? 0 : -1;\n      const computedTabIndex = this[externalTabIndex] ?? internalTabIndex;\n\n      this[isUpdatingTabIndex] = true;\n      this.tabIndex = computedTabIndex;\n      this[isUpdatingTabIndex] = false;\n    }\n  }\n\n  return FocusableElement;\n}\n"], "mappings": ";;;;;;;;AA0BO,IAAM,cAAc,OAAO,aAAa;AAE/C,IAAM,qBAAqB,OAAO,oBAAoB;AACtD,IAAM,mBAAmB,OAAO,kBAAkB;AAClD,IAAM,qBAAqB,OAAO,oBAAoB;AACtD,IAAM,iBAAiB,OAAO,gBAAgB;AAkBxC,SAAU,eACd,MAAO;;EAEP,MAAe,yBAAyB,KAAI;IAA5C,cAAA;;AAiBE,WAAA,EAAA,IAAuB;AACvB,WAAA,EAAA,IAAoC;AACpC,WAAA,EAAA,IAAuB;IAyCzB;IAxDE,KAAK,WAAW,IAAC;AACf,aAAO,KAAK,kBAAkB;IAChC;IAEA,KAAK,WAAW,EAAE,OAAc;AAC9B,UAAI,KAAK,WAAW,MAAM,OAAO;AAC/B;MACF;AAEA,WAAK,kBAAkB,IAAI;AAC3B,WAAK,cAAc,EAAC;IACtB;IAMS,oBAAiB;AACxB,YAAM,kBAAiB;AACvB,WAAK,cAAc,EAAC;IACtB;IAES,yBACP,MACA,KACA,OAAoB;AAEpB,UAAI,SAAS,YAAY;AACvB,cAAM,yBAAyB,MAAM,KAAK,KAAK;AAC/C;MACF;AAEA,WAAK,cAAc,YAAY,OAAO,OAAO,EAAE,CAAC;AAChD,UAAI,KAAK,kBAAkB,GAAG;AAE5B;MACF;AAEA,UAAI,CAAC,KAAK,aAAa,UAAU,GAAG;AAElC,aAAK,gBAAgB,IAAI;AACzB,aAAK,cAAc,EAAC;AACpB;MACF;AAEA,WAAK,gBAAgB,IAAI,KAAK;IAChC;IAEA,EAAA,KAnCC,oBAAkB,KAClB,kBAAgB,KAChB,oBAiCA,eAAc,IAAC;AACd,YAAM,mBAAmB,KAAK,WAAW,IAAI,IAAI;AACjD,YAAM,mBAAmB,KAAK,gBAAgB,KAAK;AAEnD,WAAK,kBAAkB,IAAI;AAC3B,WAAK,WAAW;AAChB,WAAK,kBAAkB,IAAI;IAC7B;;AAzDQ,aAAA;IADP,SAAS,EAAC,YAAY,KAAI,CAAC;;AA6D9B,SAAO;AACT;", "names": []}