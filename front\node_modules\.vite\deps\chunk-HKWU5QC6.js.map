{"version": 3, "sources": ["../../@material/web/checkbox/internal/checkbox.ts", "../../@material/web/checkbox/internal/checkbox-styles.ts", "../../@material/web/checkbox/checkbox.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2019 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport '../../focus/md-focus-ring.js';\nimport '../../ripple/ripple.js';\n\nimport {html, isServer, LitElement, nothing, PropertyValues} from 'lit';\nimport {property, query, state} from 'lit/decorators.js';\nimport {classMap} from 'lit/directives/class-map.js';\n\nimport {ARIAMixinStrict} from '../../internal/aria/aria.js';\nimport {mixinDelegatesAria} from '../../internal/aria/delegate.js';\nimport {\n  dispatchActivationClick,\n  isActivationClick,\n} from '../../internal/events/form-label-activation.js';\nimport {redispatchEvent} from '../../internal/events/redispatch-event.js';\nimport {\n  createValidator,\n  getValidityAnchor,\n  mixinConstraintValidation,\n} from '../../labs/behaviors/constraint-validation.js';\nimport {mixinElementInternals} from '../../labs/behaviors/element-internals.js';\nimport {\n  getFormState,\n  getFormValue,\n  mixinFormAssociated,\n} from '../../labs/behaviors/form-associated.js';\nimport {CheckboxValidator} from '../../labs/behaviors/validators/checkbox-validator.js';\n\n// Separate variable needed for closure.\nconst checkboxBaseClass = mixinDelegatesAria(\n  mixinConstraintValidation(\n    mixinFormAssociated(mixinElementInternals(LitElement)),\n  ),\n);\n\n/**\n * A checkbox component.\n *\n *\n * @fires change {Event} The native `change` event on\n * [`<input>`](https://developer.mozilla.org/en-US/docs/Web/API/HTMLElement/change_event)\n * --bubbles\n * @fires input {InputEvent} The native `input` event on\n * [`<input>`](https://developer.mozilla.org/en-US/docs/Web/API/HTMLElement/input_event)\n * --bubbles --composed\n */\nexport class Checkbox extends checkboxBaseClass {\n  /** @nocollapse */\n  static override shadowRootOptions = {\n    ...LitElement.shadowRootOptions,\n    delegatesFocus: true,\n  };\n\n  /**\n   * Whether or not the checkbox is selected.\n   */\n  @property({type: Boolean}) checked = false;\n\n  /**\n   * Whether or not the checkbox is indeterminate.\n   *\n   * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input/checkbox#indeterminate_state_checkboxes\n   */\n  @property({type: Boolean}) indeterminate = false;\n\n  /**\n   * When true, require the checkbox to be selected when participating in\n   * form submission.\n   *\n   * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input/checkbox#validation\n   */\n  @property({type: Boolean}) required = false;\n\n  /**\n   * The value of the checkbox that is submitted with a form when selected.\n   *\n   * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input/checkbox#value\n   */\n  @property() value = 'on';\n\n  @state() private prevChecked = false;\n  @state() private prevDisabled = false;\n  @state() private prevIndeterminate = false;\n  @query('input') private readonly input!: HTMLInputElement | null;\n\n  constructor() {\n    super();\n    if (!isServer) {\n      this.addEventListener('click', (event: MouseEvent) => {\n        if (!isActivationClick(event) || !this.input) {\n          return;\n        }\n        this.focus();\n        dispatchActivationClick(this.input);\n      });\n    }\n  }\n\n  protected override update(changed: PropertyValues<Checkbox>) {\n    if (\n      changed.has('checked') ||\n      changed.has('disabled') ||\n      changed.has('indeterminate')\n    ) {\n      this.prevChecked = changed.get('checked') ?? this.checked;\n      this.prevDisabled = changed.get('disabled') ?? this.disabled;\n      this.prevIndeterminate =\n        changed.get('indeterminate') ?? this.indeterminate;\n    }\n\n    super.update(changed);\n  }\n\n  protected override render() {\n    const prevNone = !this.prevChecked && !this.prevIndeterminate;\n    const prevChecked = this.prevChecked && !this.prevIndeterminate;\n    const prevIndeterminate = this.prevIndeterminate;\n    const isChecked = this.checked && !this.indeterminate;\n    const isIndeterminate = this.indeterminate;\n\n    const containerClasses = classMap({\n      'disabled': this.disabled,\n      'selected': isChecked || isIndeterminate,\n      'unselected': !isChecked && !isIndeterminate,\n      'checked': isChecked,\n      'indeterminate': isIndeterminate,\n      'prev-unselected': prevNone,\n      'prev-checked': prevChecked,\n      'prev-indeterminate': prevIndeterminate,\n      'prev-disabled': this.prevDisabled,\n    });\n\n    // Needed for closure conformance\n    const {ariaLabel, ariaInvalid} = this as ARIAMixinStrict;\n    // Note: <input> needs to be rendered before the <svg> for\n    // form.reportValidity() to work in Chrome.\n    return html`\n      <div class=\"container ${containerClasses}\">\n        <input\n          type=\"checkbox\"\n          id=\"input\"\n          aria-checked=${isIndeterminate ? 'mixed' : nothing}\n          aria-label=${ariaLabel || nothing}\n          aria-invalid=${ariaInvalid || nothing}\n          ?disabled=${this.disabled}\n          ?required=${this.required}\n          .indeterminate=${this.indeterminate}\n          .checked=${this.checked}\n          @input=${this.handleInput}\n          @change=${this.handleChange} />\n\n        <div class=\"outline\"></div>\n        <div class=\"background\"></div>\n        <md-focus-ring part=\"focus-ring\" for=\"input\"></md-focus-ring>\n        <md-ripple for=\"input\" ?disabled=${this.disabled}></md-ripple>\n        <svg class=\"icon\" viewBox=\"0 0 18 18\" aria-hidden=\"true\">\n          <rect class=\"mark short\" />\n          <rect class=\"mark long\" />\n        </svg>\n      </div>\n    `;\n  }\n\n  private handleInput(event: Event) {\n    const target = event.target as HTMLInputElement;\n    this.checked = target.checked;\n    this.indeterminate = target.indeterminate;\n    // <input> 'input' event bubbles and is composed, don't re-dispatch it.\n  }\n\n  private handleChange(event: Event) {\n    // <input> 'change' event is not composed, re-dispatch it.\n    redispatchEvent(this, event);\n  }\n\n  // Writable mixin properties for lit-html binding, needed for lit-analyzer\n  declare disabled: boolean;\n  declare name: string;\n\n  override [getFormValue]() {\n    if (!this.checked || this.indeterminate) {\n      return null;\n    }\n\n    return this.value;\n  }\n\n  override [getFormState]() {\n    return String(this.checked);\n  }\n\n  override formResetCallback() {\n    // The checked property does not reflect, so the original attribute set by\n    // the user is used to determine the default value.\n    this.checked = this.hasAttribute('checked');\n  }\n\n  override formStateRestoreCallback(state: string) {\n    this.checked = state === 'true';\n  }\n\n  override [createValidator]() {\n    return new CheckboxValidator(() => this);\n  }\n\n  override [getValidityAnchor]() {\n    return this.input;\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n// Generated stylesheet for ./checkbox/internal/checkbox-styles.css.\nimport {css} from 'lit';\nexport const styles = css`:host{border-start-start-radius:var(--md-checkbox-container-shape-start-start, var(--md-checkbox-container-shape, 2px));border-start-end-radius:var(--md-checkbox-container-shape-start-end, var(--md-checkbox-container-shape, 2px));border-end-end-radius:var(--md-checkbox-container-shape-end-end, var(--md-checkbox-container-shape, 2px));border-end-start-radius:var(--md-checkbox-container-shape-end-start, var(--md-checkbox-container-shape, 2px));display:inline-flex;height:var(--md-checkbox-container-size, 18px);position:relative;vertical-align:top;width:var(--md-checkbox-container-size, 18px);-webkit-tap-highlight-color:rgba(0,0,0,0);cursor:pointer}:host([disabled]){cursor:default}:host([touch-target=wrapper]){margin:max(0px,(48px - var(--md-checkbox-container-size, 18px))/2)}md-focus-ring{height:44px;inset:unset;width:44px}input{appearance:none;height:48px;margin:0;opacity:0;outline:none;position:absolute;width:48px;z-index:1;cursor:inherit}:host([touch-target=none]) input{height:100%;width:100%}.container{border-radius:inherit;display:flex;height:100%;place-content:center;place-items:center;position:relative;width:100%}.outline,.background,.icon{inset:0;position:absolute}.outline,.background{border-radius:inherit}.outline{border-color:var(--md-checkbox-outline-color, var(--md-sys-color-on-surface-variant, #49454f));border-style:solid;border-width:var(--md-checkbox-outline-width, 2px);box-sizing:border-box}.background{background-color:var(--md-checkbox-selected-container-color, var(--md-sys-color-primary, #6750a4))}.background,.icon{opacity:0;transition-duration:150ms,50ms;transition-property:transform,opacity;transition-timing-function:cubic-bezier(0.3, 0, 0.8, 0.15),linear;transform:scale(0.6)}:where(.selected) :is(.background,.icon){opacity:1;transition-duration:350ms,50ms;transition-timing-function:cubic-bezier(0.05, 0.7, 0.1, 1),linear;transform:scale(1)}md-ripple{border-radius:var(--md-checkbox-state-layer-shape, var(--md-sys-shape-corner-full, 9999px));height:var(--md-checkbox-state-layer-size, 40px);inset:unset;width:var(--md-checkbox-state-layer-size, 40px);--md-ripple-hover-color: var(--md-checkbox-hover-state-layer-color, var(--md-sys-color-on-surface, #1d1b20));--md-ripple-hover-opacity: var(--md-checkbox-hover-state-layer-opacity, 0.08);--md-ripple-pressed-color: var(--md-checkbox-pressed-state-layer-color, var(--md-sys-color-primary, #6750a4));--md-ripple-pressed-opacity: var(--md-checkbox-pressed-state-layer-opacity, 0.12)}.selected md-ripple{--md-ripple-hover-color: var(--md-checkbox-selected-hover-state-layer-color, var(--md-sys-color-primary, #6750a4));--md-ripple-hover-opacity: var(--md-checkbox-selected-hover-state-layer-opacity, 0.08);--md-ripple-pressed-color: var(--md-checkbox-selected-pressed-state-layer-color, var(--md-sys-color-on-surface, #1d1b20));--md-ripple-pressed-opacity: var(--md-checkbox-selected-pressed-state-layer-opacity, 0.12)}.icon{fill:var(--md-checkbox-selected-icon-color, var(--md-sys-color-on-primary, #fff));height:var(--md-checkbox-icon-size, 18px);width:var(--md-checkbox-icon-size, 18px)}.mark.short{height:2px;transition-property:transform,height;width:2px}.mark.long{height:2px;transition-property:transform,width;width:10px}.mark{animation-duration:150ms;animation-timing-function:cubic-bezier(0.3, 0, 0.8, 0.15);transition-duration:150ms;transition-timing-function:cubic-bezier(0.3, 0, 0.8, 0.15)}.selected .mark{animation-duration:350ms;animation-timing-function:cubic-bezier(0.05, 0.7, 0.1, 1);transition-duration:350ms;transition-timing-function:cubic-bezier(0.05, 0.7, 0.1, 1)}.checked .mark,.prev-checked.unselected .mark{transform:scaleY(-1) translate(7px, -14px) rotate(45deg)}.checked .mark.short,.prev-checked.unselected .mark.short{height:5.6568542495px}.checked .mark.long,.prev-checked.unselected .mark.long{width:11.313708499px}.indeterminate .mark,.prev-indeterminate.unselected .mark{transform:scaleY(-1) translate(4px, -10px) rotate(0deg)}.prev-unselected .mark{transition-property:none}.prev-unselected.checked .mark.long{animation-name:prev-unselected-to-checked}@keyframes prev-unselected-to-checked{from{width:0}}:where(:hover) .outline{border-color:var(--md-checkbox-hover-outline-color, var(--md-sys-color-on-surface, #1d1b20));border-width:var(--md-checkbox-hover-outline-width, 2px)}:where(:hover) .background{background:var(--md-checkbox-selected-hover-container-color, var(--md-sys-color-primary, #6750a4))}:where(:hover) .icon{fill:var(--md-checkbox-selected-hover-icon-color, var(--md-sys-color-on-primary, #fff))}:where(:focus-within) .outline{border-color:var(--md-checkbox-focus-outline-color, var(--md-sys-color-on-surface, #1d1b20));border-width:var(--md-checkbox-focus-outline-width, 2px)}:where(:focus-within) .background{background:var(--md-checkbox-selected-focus-container-color, var(--md-sys-color-primary, #6750a4))}:where(:focus-within) .icon{fill:var(--md-checkbox-selected-focus-icon-color, var(--md-sys-color-on-primary, #fff))}:where(:active) .outline{border-color:var(--md-checkbox-pressed-outline-color, var(--md-sys-color-on-surface, #1d1b20));border-width:var(--md-checkbox-pressed-outline-width, 2px)}:where(:active) .background{background:var(--md-checkbox-selected-pressed-container-color, var(--md-sys-color-primary, #6750a4))}:where(:active) .icon{fill:var(--md-checkbox-selected-pressed-icon-color, var(--md-sys-color-on-primary, #fff))}:where(.disabled,.prev-disabled) :is(.background,.icon,.mark){animation-duration:0s;transition-duration:0s}:where(.disabled) .outline{border-color:var(--md-checkbox-disabled-outline-color, var(--md-sys-color-on-surface, #1d1b20));border-width:var(--md-checkbox-disabled-outline-width, 2px);opacity:var(--md-checkbox-disabled-container-opacity, 0.38)}:where(.selected.disabled) .outline{visibility:hidden}:where(.selected.disabled) .background{background:var(--md-checkbox-selected-disabled-container-color, var(--md-sys-color-on-surface, #1d1b20));opacity:var(--md-checkbox-selected-disabled-container-opacity, 0.38)}:where(.disabled) .icon{fill:var(--md-checkbox-selected-disabled-icon-color, var(--md-sys-color-surface, #fef7ff))}@media(forced-colors: active){.background{background-color:CanvasText}.selected.disabled .background{background-color:GrayText;opacity:1}.outline{border-color:CanvasText}.disabled .outline{border-color:GrayText;opacity:1}.icon{fill:Canvas}}\n`;\n", "/**\n * @license\n * Copyright 2018 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {Checkbox} from './internal/checkbox.js';\nimport {styles} from './internal/checkbox-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-checkbox': MdCheckbox;\n  }\n}\n\n/**\n * @summary Checkboxes allow users to select one or more items from a set.\n * Checkboxes can turn an option on or off.\n *\n * @description\n * Use checkboxes to:\n * - Select one or more options from a list\n * - Present a list containing sub-selections\n * - Turn an item on or off in a desktop environment\n *\n * @final\n * @suppress {visibility}\n */\n@customElement('md-checkbox')\nexport class MdCheckbox extends Checkbox {\n  static override styles: CSSResultOrNative[] = [styles];\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA,IAAM,oBAAoB,mBACxB,0BACE,oBAAoB,sBAAsB,UAAU,CAAC,CAAC,CACvD;AAcG,IAAO,WAAP,cAAwB,kBAAiB;EAuC7C,cAAA;AACE,UAAK;AA9BoB,SAAA,UAAU;AAOV,SAAA,gBAAgB;AAQhB,SAAA,WAAW;AAO1B,SAAA,QAAQ;AAEH,SAAA,cAAc;AACd,SAAA,eAAe;AACf,SAAA,oBAAoB;AAKnC,QAAI,CAAC,UAAU;AACb,WAAK,iBAAiB,SAAS,CAAC,UAAqB;AACnD,YAAI,CAAC,kBAAkB,KAAK,KAAK,CAAC,KAAK,OAAO;AAC5C;QACF;AACA,aAAK,MAAK;AACV,gCAAwB,KAAK,KAAK;MACpC,CAAC;IACH;EACF;EAEmB,OAAO,SAAiC;AACzD,QACE,QAAQ,IAAI,SAAS,KACrB,QAAQ,IAAI,UAAU,KACtB,QAAQ,IAAI,eAAe,GAC3B;AACA,WAAK,cAAc,QAAQ,IAAI,SAAS,KAAK,KAAK;AAClD,WAAK,eAAe,QAAQ,IAAI,UAAU,KAAK,KAAK;AACpD,WAAK,oBACH,QAAQ,IAAI,eAAe,KAAK,KAAK;IACzC;AAEA,UAAM,OAAO,OAAO;EACtB;EAEmB,SAAM;AACvB,UAAM,WAAW,CAAC,KAAK,eAAe,CAAC,KAAK;AAC5C,UAAM,cAAc,KAAK,eAAe,CAAC,KAAK;AAC9C,UAAM,oBAAoB,KAAK;AAC/B,UAAM,YAAY,KAAK,WAAW,CAAC,KAAK;AACxC,UAAM,kBAAkB,KAAK;AAE7B,UAAM,mBAAmB,SAAS;MAChC,YAAY,KAAK;MACjB,YAAY,aAAa;MACzB,cAAc,CAAC,aAAa,CAAC;MAC7B,WAAW;MACX,iBAAiB;MACjB,mBAAmB;MACnB,gBAAgB;MAChB,sBAAsB;MACtB,iBAAiB,KAAK;KACvB;AAGD,UAAM,EAAC,WAAW,YAAW,IAAI;AAGjC,WAAO;8BACmB,gBAAgB;;;;yBAIrB,kBAAkB,UAAU,OAAO;uBACrC,aAAa,OAAO;yBAClB,eAAe,OAAO;sBACzB,KAAK,QAAQ;sBACb,KAAK,QAAQ;2BACR,KAAK,aAAa;qBACxB,KAAK,OAAO;mBACd,KAAK,WAAW;oBACf,KAAK,YAAY;;;;;2CAKM,KAAK,QAAQ;;;;;;;EAOtD;EAEQ,YAAY,OAAY;AAC9B,UAAM,SAAS,MAAM;AACrB,SAAK,UAAU,OAAO;AACtB,SAAK,gBAAgB,OAAO;EAE9B;EAEQ,aAAa,OAAY;AAE/B,oBAAgB,MAAM,KAAK;EAC7B;EAMS,CAAC,YAAY,IAAC;AACrB,QAAI,CAAC,KAAK,WAAW,KAAK,eAAe;AACvC,aAAO;IACT;AAEA,WAAO,KAAK;EACd;EAES,CAAC,YAAY,IAAC;AACrB,WAAO,OAAO,KAAK,OAAO;EAC5B;EAES,oBAAiB;AAGxB,SAAK,UAAU,KAAK,aAAa,SAAS;EAC5C;EAES,yBAAyBA,QAAa;AAC7C,SAAK,UAAUA,WAAU;EAC3B;EAES,CAAC,eAAe,IAAC;AACxB,WAAO,IAAI,kBAAkB,MAAM,IAAI;EACzC;EAES,CAAC,iBAAiB,IAAC;AAC1B,WAAO,KAAK;EACd;;AA/JgB,SAAA,oBAAoB;EAClC,GAAG,WAAW;EACd,gBAAgB;;AAMS,WAAA;EAA1B,SAAS,EAAC,MAAM,QAAO,CAAC;;AAOE,WAAA;EAA1B,SAAS,EAAC,MAAM,QAAO,CAAC;;AAQE,WAAA;EAA1B,SAAS,EAAC,MAAM,QAAO,CAAC;;AAOb,WAAA;EAAX,SAAQ;;AAEQ,WAAA;EAAhB,MAAK;;AACW,WAAA;EAAhB,MAAK;;AACW,WAAA;EAAhB,MAAK;;AAC2B,WAAA;EAAhC,MAAM,OAAO;;;;ACjFT,IAAM,SAAS;;;;ACyBf,IAAM,aAAN,MAAMC,oBAAmB,SAAQ;;AACtB,WAAA,SAA8B,CAAC,MAAM;AAD1C,aAAU,WAAA;EADtB,cAAc,aAAa;GACf,UAAU;", "names": ["state", "MdCheckbox"]}