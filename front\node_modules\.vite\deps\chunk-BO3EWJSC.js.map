{"version": 3, "sources": ["../../@material/web/internal/controller/is-rtl.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2022 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\n/**\n * Returns `true` if the given element is in a right-to-left direction.\n *\n * @param el Element to determine direction from\n * @param shouldCheck Optional. If `false`, return `false` without checking\n *     direction. Determining the direction of `el` is somewhat expensive, so\n *     this parameter can be used as a conditional guard. Defaults to `true`.\n */\nexport function isRtl(el: HTMLElement, shouldCheck = true) {\n  return (\n    shouldCheck &&\n    getComputedStyle(el).getPropertyValue('direction').trim() === 'rtl'\n  );\n}\n"], "mappings": ";AAcM,SAAU,MAAM,IAAiB,cAAc,MAAI;AACvD,SACE,eACA,iBAAiB,EAAE,EAAE,iBAAiB,WAAW,EAAE,KAAI,MAAO;AAElE;", "names": []}