import {
  MenuItemController,
  styles
} from "./chunk-5GK7SOHM.js";
import "./chunk-DMRCI6IZ.js";
import "./chunk-YSXTHARJ.js";
import {
  html as html2,
  literal
} from "./chunk-FNIFQ77A.js";
import "./chunk-NAP7BBWC.js";
import "./chunk-XM73XWEP.js";
import "./chunk-WGW4CKV7.js";
import {
  mixinDelegatesAria
} from "./chunk-GXE4MBY5.js";
import {
  classMap
} from "./chunk-SZQCPKZF.js";
import {
  customElement,
  property,
  query,
  queryAssignedElements,
  queryAssignedNodes
} from "./chunk-T3WMJB5E.js";
import {
  LitElement,
  html,
  nothing
} from "./chunk-4GZ3EDRH.js";
import {
  __decorate
} from "./chunk-HMZZ7KLC.js";
import "./chunk-G3PMV62Z.js";

// node_modules/@material/web/menu/internal/menuitem/menu-item.js
var menuItemBaseClass = mixinDelegatesAria(LitElement);
var MenuItemEl = class extends menuItemBaseClass {
  constructor() {
    super(...arguments);
    this.disabled = false;
    this.type = "menuitem";
    this.href = "";
    this.target = "";
    this.keepOpen = false;
    this.selected = false;
    this.menuItemController = new MenuItemController(this, {
      getHeadlineElements: () => {
        return this.headlineElements;
      },
      getSupportingTextElements: () => {
        return this.supportingTextElements;
      },
      getDefaultElements: () => {
        return this.defaultElements;
      },
      getInteractiveElement: () => this.listItemRoot
    });
  }
  /**
   * The text that is selectable via typeahead. If not set, defaults to the
   * innerText of the item slotted into the `"headline"` slot.
   */
  get typeaheadText() {
    return this.menuItemController.typeaheadText;
  }
  set typeaheadText(text) {
    this.menuItemController.setTypeaheadText(text);
  }
  render() {
    return this.renderListItem(html`
      <md-item>
        <div slot="container">
          ${this.renderRipple()} ${this.renderFocusRing()}
        </div>
        <slot name="start" slot="start"></slot>
        <slot name="end" slot="end"></slot>
        ${this.renderBody()}
      </md-item>
    `);
  }
  /**
   * Renders the root list item.
   *
   * @param content the child content of the list item.
   */
  renderListItem(content) {
    const isAnchor = this.type === "link";
    let tag;
    switch (this.menuItemController.tagName) {
      case "a":
        tag = literal`a`;
        break;
      case "button":
        tag = literal`button`;
        break;
      default:
      case "li":
        tag = literal`li`;
        break;
    }
    const target = isAnchor && !!this.target ? this.target : nothing;
    return html2`
      <${tag}
        id="item"
        tabindex=${this.disabled && !isAnchor ? -1 : 0}
        role=${this.menuItemController.role}
        aria-label=${this.ariaLabel || nothing}
        aria-selected=${this.ariaSelected || nothing}
        aria-checked=${this.ariaChecked || nothing}
        aria-expanded=${this.ariaExpanded || nothing}
        aria-haspopup=${this.ariaHasPopup || nothing}
        class="list-item ${classMap(this.getRenderClasses())}"
        href=${this.href || nothing}
        target=${target}
        @click=${this.menuItemController.onClick}
        @keydown=${this.menuItemController.onKeydown}
      >${content}</${tag}>
    `;
  }
  /**
   * Handles rendering of the ripple element.
   */
  renderRipple() {
    return html` <md-ripple
      part="ripple"
      for="item"
      ?disabled=${this.disabled}></md-ripple>`;
  }
  /**
   * Handles rendering of the focus ring.
   */
  renderFocusRing() {
    return html` <md-focus-ring
      part="focus-ring"
      for="item"
      inward></md-focus-ring>`;
  }
  /**
   * Classes applied to the list item root.
   */
  getRenderClasses() {
    return {
      "disabled": this.disabled,
      "selected": this.selected
    };
  }
  /**
   * Handles rendering the headline and supporting text.
   */
  renderBody() {
    return html`
      <slot></slot>
      <slot name="overline" slot="overline"></slot>
      <slot name="headline" slot="headline"></slot>
      <slot name="supporting-text" slot="supporting-text"></slot>
      <slot
        name="trailing-supporting-text"
        slot="trailing-supporting-text"></slot>
    `;
  }
  focus() {
    this.listItemRoot?.focus();
  }
};
MenuItemEl.shadowRootOptions = {
  ...LitElement.shadowRootOptions,
  delegatesFocus: true
};
__decorate([
  property({ type: Boolean, reflect: true })
], MenuItemEl.prototype, "disabled", void 0);
__decorate([
  property()
], MenuItemEl.prototype, "type", void 0);
__decorate([
  property()
], MenuItemEl.prototype, "href", void 0);
__decorate([
  property()
], MenuItemEl.prototype, "target", void 0);
__decorate([
  property({ type: Boolean, attribute: "keep-open" })
], MenuItemEl.prototype, "keepOpen", void 0);
__decorate([
  property({ type: Boolean })
], MenuItemEl.prototype, "selected", void 0);
__decorate([
  query(".list-item")
], MenuItemEl.prototype, "listItemRoot", void 0);
__decorate([
  queryAssignedElements({ slot: "headline" })
], MenuItemEl.prototype, "headlineElements", void 0);
__decorate([
  queryAssignedElements({ slot: "supporting-text" })
], MenuItemEl.prototype, "supportingTextElements", void 0);
__decorate([
  queryAssignedNodes({ slot: "" })
], MenuItemEl.prototype, "defaultElements", void 0);
__decorate([
  property({ attribute: "typeahead-text" })
], MenuItemEl.prototype, "typeaheadText", null);

// node_modules/@material/web/menu/menu-item.js
var MdMenuItem = class MdMenuItem2 extends MenuItemEl {
};
MdMenuItem.styles = [styles];
MdMenuItem = __decorate([
  customElement("md-menu-item")
], MdMenuItem);
export {
  MdMenuItem
};
/*! Bundled license information:

@material/web/menu/internal/menuitem/menu-item.js:
@material/web/menu/menu-item.js:
  (**
   * @license
   * Copyright 2022 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   *)
*/
//# sourceMappingURL=@material_web_menu_menu-item__js.js.map
