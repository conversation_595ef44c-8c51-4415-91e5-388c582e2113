{"version": 3, "sources": ["../../@material/web/slider/internal/forced-colors-styles.ts", "../../lit-html/src/directives/when.ts", "../../@material/web/slider/internal/slider.ts", "../../@material/web/slider/internal/slider-styles.ts", "../../@material/web/slider/slider.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2024 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n// Generated stylesheet for ./slider/internal/forced-colors-styles.css.\nimport {css} from 'lit';\nexport const styles = css`@media(forced-colors: active){:host{--md-slider-active-track-color: CanvasText;--md-slider-disabled-active-track-color: GrayText;--md-slider-disabled-active-track-opacity: 1;--md-slider-disabled-handle-color: GrayText;--md-slider-disabled-inactive-track-color: GrayText;--md-slider-disabled-inactive-track-opacity: 1;--md-slider-focus-handle-color: CanvasText;--md-slider-handle-color: CanvasText;--md-slider-handle-shadow-color: Canvas;--md-slider-hover-handle-color: CanvasText;--md-slider-hover-state-layer-color: Canvas;--md-slider-hover-state-layer-opacity: 1;--md-slider-inactive-track-color: Canvas;--md-slider-label-container-color: Canvas;--md-slider-label-text-color: CanvasText;--md-slider-pressed-handle-color: CanvasText;--md-slider-pressed-state-layer-color: Canvas;--md-slider-pressed-state-layer-opacity: 1;--md-slider-with-overlap-handle-outline-color: CanvasText}.label,.label::before{border:var(--_with-overlap-handle-outline-color) solid var(--_with-overlap-handle-outline-width)}:host(:not([disabled])) .track::before{border:1px solid var(--_active-track-color)}.tickmarks::before{background-image:url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='CanvasText'%3E%3Ccircle cx='2' cy='2'  r='1'/%3E%3C/svg%3E\")}.tickmarks::after{background-image:url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='Canvas'%3E%3Ccircle cx='2' cy='2' r='1'/%3E%3C/svg%3E\")}:host([disabled]) .tickmarks::before{background-image:url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='Canvas'%3E%3Ccircle cx='2' cy='2'  r='1'/%3E%3C/svg%3E\")}}\n`;\n", "/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\ntype Falsy = null | undefined | false | 0 | -0 | 0n | '';\n\n/**\n * When `condition` is true, returns the result of calling `trueCase()`, else\n * returns the result of calling `falseCase()` if `falseCase` is defined.\n *\n * This is a convenience wrapper around a ternary expression that makes it a\n * little nicer to write an inline conditional without an else.\n *\n * @example\n *\n * ```ts\n * render() {\n *   return html`\n *     ${when(this.user, () => html`User: ${this.user.username}`, () => html`Sign In...`)}\n *   `;\n * }\n * ```\n */\nexport function when<C extends Falsy, T, F = undefined>(\n  condition: C,\n  trueCase: (c: C) => T,\n  falseCase?: (c: C) => F\n): F;\nexport function when<C, T, F>(\n  condition: C extends Falsy ? never : C,\n  trueCase: (c: C) => T,\n  falseCase?: (c: C) => F\n): T;\nexport function when<C, T, F = undefined>(\n  condition: C,\n  trueCase: (c: Exclude<C, Falsy>) => T,\n  falseCase?: (c: Extract<C, Falsy>) => F\n): C extends Falsy ? F : T;\nexport function when(\n  condition: unknown,\n  trueCase: (c: unknown) => unknown,\n  falseCase?: (c: unknown) => unknown\n): unknown {\n  return condition ? trueCase(condition) : falseCase?.(condition);\n}\n", "/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport '../../elevation/elevation.js';\nimport '../../focus/md-focus-ring.js';\nimport '../../ripple/ripple.js';\n\nimport {html, isServer, LitElement, nothing, PropertyValues} from 'lit';\nimport {property, query, queryAsync, state} from 'lit/decorators.js';\nimport {classMap} from 'lit/directives/class-map.js';\nimport {styleMap} from 'lit/directives/style-map.js';\nimport {when} from 'lit/directives/when.js';\n\nimport {ARIAMixinStrict} from '../../internal/aria/aria.js';\nimport {mixinDelegatesAria} from '../../internal/aria/delegate.js';\nimport {\n  dispatchActivationClick,\n  isActivationClick,\n} from '../../internal/events/form-label-activation.js';\nimport {redispatchEvent} from '../../internal/events/redispatch-event.js';\nimport {mixinElementInternals} from '../../labs/behaviors/element-internals.js';\nimport {\n  getFormValue,\n  mixinFormAssociated,\n} from '../../labs/behaviors/form-associated.js';\nimport {MdRipple} from '../../ripple/ripple.js';\n\n// Disable warning for classMap with destructuring\n// tslint:disable:no-implicit-dictionary-conversion\n\n// Separate variable needed for closure.\nconst sliderBaseClass = mixinDelegatesAria(\n  mixinFormAssociated(mixinElementInternals(LitElement)),\n);\n\n/**\n * Slider component.\n *\n *\n * @fires change {Event} The native `change` event on\n * [`<input>`](https://developer.mozilla.org/en-US/docs/Web/API/HTMLElement/change_event)\n * --bubbles\n * @fires input {InputEvent} The native `input` event on\n * [`<input>`](https://developer.mozilla.org/en-US/docs/Web/API/HTMLElement/input_event)\n * --bubbles --composed\n */\nexport class Slider extends sliderBaseClass {\n  /** @nocollapse */\n  static override shadowRootOptions: ShadowRootInit = {\n    ...LitElement.shadowRootOptions,\n    delegatesFocus: true,\n  };\n\n  /**\n   * The slider minimum value\n   */\n  @property({type: Number}) min = 0;\n\n  /**\n   * The slider maximum value\n   */\n  @property({type: Number}) max = 100;\n\n  /**\n   * The slider value displayed when range is false.\n   */\n  @property({type: Number}) value?: number;\n\n  /**\n   * The slider start value displayed when range is true.\n   */\n  @property({type: Number, attribute: 'value-start'}) valueStart?: number;\n\n  /**\n   * The slider end value displayed when range is true.\n   */\n  @property({type: Number, attribute: 'value-end'}) valueEnd?: number;\n\n  /**\n   * An optional label for the slider's value displayed when range is\n   * false; if not set, the label is the value itself.\n   */\n  @property({attribute: 'value-label'}) valueLabel = '';\n\n  /**\n   * An optional label for the slider's start value displayed when\n   * range is true; if not set, the label is the valueStart itself.\n   */\n  @property({attribute: 'value-label-start'}) valueLabelStart = '';\n\n  /**\n   * An optional label for the slider's end value displayed when\n   * range is true; if not set, the label is the valueEnd itself.\n   */\n  @property({attribute: 'value-label-end'}) valueLabelEnd = '';\n\n  /**\n   * Aria label for the slider's start handle displayed when\n   * range is true.\n   */\n  @property({attribute: 'aria-label-start'}) ariaLabelStart = '';\n\n  /**\n   * Aria value text for the slider's start value displayed when\n   * range is true.\n   */\n  @property({attribute: 'aria-valuetext-start'}) ariaValueTextStart = '';\n\n  /**\n   * Aria label for the slider's end handle displayed when\n   * range is true.\n   */\n  @property({attribute: 'aria-label-end'}) ariaLabelEnd = '';\n\n  /**\n   * Aria value text for the slider's end value displayed when\n   * range is true.\n   */\n  @property({attribute: 'aria-valuetext-end'}) ariaValueTextEnd = '';\n\n  /**\n   * The step between values.\n   */\n  @property({type: Number}) step = 1;\n\n  /**\n   * Whether or not to show tick marks.\n   */\n  @property({type: Boolean}) ticks = false;\n\n  /**\n   * Whether or not to show a value label when activated.\n   */\n  @property({type: Boolean}) labeled = false;\n\n  /**\n   * Whether or not to show a value range. When false, the slider displays\n   * a slideable handle for the value property; when true, it displays\n   * slideable handles for the valueStart and valueEnd properties.\n   */\n  @property({type: Boolean}) range = false;\n\n  /**\n   * The HTML name to use in form submission for a range slider's starting\n   * value. Use `name` instead if both the start and end values should use the\n   * same name.\n   */\n  get nameStart() {\n    return this.getAttribute('name-start') ?? this.name;\n  }\n  set nameStart(name: string) {\n    this.setAttribute('name-start', name);\n  }\n\n  /**\n   * The HTML name to use in form submission for a range slider's ending value.\n   * Use `name` instead if both the start and end values should use the same\n   * name.\n   */\n  get nameEnd() {\n    return this.getAttribute('name-end') ?? this.nameStart;\n  }\n  set nameEnd(name: string) {\n    this.setAttribute('name-end', name);\n  }\n\n  @query('input.start') private readonly inputStart!: HTMLInputElement | null;\n  @query('.handle.start') private readonly handleStart!: HTMLDivElement | null;\n  @queryAsync('md-ripple.start')\n  private readonly rippleStart!: Promise<MdRipple | null>;\n\n  @query('input.end') private readonly inputEnd!: HTMLInputElement | null;\n  @query('.handle.end') private readonly handleEnd!: HTMLDivElement | null;\n  @queryAsync('md-ripple.end')\n  private readonly rippleEnd!: Promise<MdRipple | null>;\n\n  // handle hover/pressed states are set manually since the handle\n  // does not receive pointer events so that the native inputs are\n  // interaction targets.\n  @state() private handleStartHover = false;\n  @state() private handleEndHover = false;\n\n  @state() private startOnTop = false;\n  @state() private handlesOverlapping = false;\n\n  @state() private renderValueStart?: number;\n  @state() private renderValueEnd?: number;\n\n  // Note: start aria-* properties are only applied when range=true, which is\n  // why they do not need to handle both cases.\n  private get renderAriaLabelStart() {\n    // Needed for closure conformance\n    const {ariaLabel} = this as ARIAMixinStrict;\n    return (\n      this.ariaLabelStart ||\n      (ariaLabel && `${ariaLabel} start`) ||\n      this.valueLabelStart ||\n      String(this.valueStart)\n    );\n  }\n\n  private get renderAriaValueTextStart() {\n    return (\n      this.ariaValueTextStart || this.valueLabelStart || String(this.valueStart)\n    );\n  }\n\n  // Note: end aria-* properties are applied for single and range sliders, which\n  // is why it needs to handle `this.range` (while start aria-* properties do\n  // not).\n  private get renderAriaLabelEnd() {\n    // Needed for closure conformance\n    const {ariaLabel} = this as ARIAMixinStrict;\n    if (this.range) {\n      return (\n        this.ariaLabelEnd ||\n        (ariaLabel && `${ariaLabel} end`) ||\n        this.valueLabelEnd ||\n        String(this.valueEnd)\n      );\n    }\n\n    return ariaLabel || this.valueLabel || String(this.value);\n  }\n\n  private get renderAriaValueTextEnd() {\n    if (this.range) {\n      return (\n        this.ariaValueTextEnd || this.valueLabelEnd || String(this.valueEnd)\n      );\n    }\n\n    // Needed for conformance\n    const {ariaValueText} = this as ARIAMixinStrict;\n    return ariaValueText || this.valueLabel || String(this.value);\n  }\n\n  // used in synthetic events generated to control ripple hover state.\n  private ripplePointerId = 1;\n\n  // flag to prevent processing of re-dispatched input event.\n  private isRedispatchingEvent = false;\n\n  private action?: Action;\n\n  constructor() {\n    super();\n    if (!isServer) {\n      this.addEventListener('click', (event: MouseEvent) => {\n        if (!isActivationClick(event) || !this.inputEnd) {\n          return;\n        }\n        this.focus();\n        dispatchActivationClick(this.inputEnd);\n      });\n    }\n  }\n\n  override focus() {\n    this.inputEnd?.focus();\n  }\n\n  protected override willUpdate(changed: PropertyValues) {\n    this.renderValueStart = changed.has('valueStart')\n      ? this.valueStart\n      : this.inputStart?.valueAsNumber;\n    const endValueChanged =\n      (changed.has('valueEnd') && this.range) || changed.has('value');\n    this.renderValueEnd = endValueChanged\n      ? this.range\n        ? this.valueEnd\n        : this.value\n      : this.inputEnd?.valueAsNumber;\n    // manually handle ripple hover state since the handle is pointer events\n    // none.\n    if (changed.get('handleStartHover') !== undefined) {\n      this.toggleRippleHover(this.rippleStart, this.handleStartHover);\n    } else if (changed.get('handleEndHover') !== undefined) {\n      this.toggleRippleHover(this.rippleEnd, this.handleEndHover);\n    }\n  }\n\n  protected override updated(changed: PropertyValues) {\n    // Validate input rendered value and re-render if necessary. This ensures\n    // the rendred handle stays in sync with the input thumb which is used for\n    // interaction. These can get out of sync if a supplied value does not\n    // map to an exactly stepped value between min and max.\n    if (this.range) {\n      this.renderValueStart = this.inputStart!.valueAsNumber;\n    }\n    this.renderValueEnd = this.inputEnd!.valueAsNumber;\n    // update values if they are unset\n    // when using a range, default to equi-distant between\n    // min - valueStart - valueEnd - max\n    if (this.range) {\n      const segment = (this.max - this.min) / 3;\n      if (this.valueStart === undefined) {\n        this.inputStart!.valueAsNumber = this.min + segment;\n        // read actual value from input\n        const v = this.inputStart!.valueAsNumber;\n        this.valueStart = this.renderValueStart = v;\n      }\n      if (this.valueEnd === undefined) {\n        this.inputEnd!.valueAsNumber = this.min + 2 * segment;\n        // read actual value from input\n        const v = this.inputEnd!.valueAsNumber;\n        this.valueEnd = this.renderValueEnd = v;\n      }\n    } else {\n      this.value ??= this.renderValueEnd;\n    }\n    if (\n      changed.has('range') ||\n      changed.has('renderValueStart') ||\n      changed.has('renderValueEnd') ||\n      this.isUpdatePending\n    ) {\n      // Only check if the handle nubs are overlapping, as the ripple touch\n      // target extends subtantially beyond the boundary of the handle nub.\n      const startNub = this.handleStart?.querySelector('.handleNub');\n      const endNub = this.handleEnd?.querySelector('.handleNub');\n      this.handlesOverlapping = isOverlapping(startNub, endNub);\n    }\n    // called to finish the update imediately;\n    // note, this is a no-op unless an update is scheduled\n    this.performUpdate();\n  }\n\n  protected override render() {\n    const step = this.step === 0 ? 1 : this.step;\n    const range = Math.max(this.max - this.min, step);\n    const startFraction = this.range\n      ? ((this.renderValueStart ?? this.min) - this.min) / range\n      : 0;\n    const endFraction = ((this.renderValueEnd ?? this.min) - this.min) / range;\n    const containerStyles = {\n      // for clipping inputs and active track.\n      '--_start-fraction': String(startFraction),\n      '--_end-fraction': String(endFraction),\n      // for generating tick marks\n      '--_tick-count': String(range / step),\n    };\n    const containerClasses = {ranged: this.range};\n\n    // optional label values to show in place of the value.\n    const labelStart = this.valueLabelStart || String(this.renderValueStart);\n    const labelEnd =\n      (this.range ? this.valueLabelEnd : this.valueLabel) ||\n      String(this.renderValueEnd);\n\n    const inputStartProps = {\n      start: true,\n      value: this.renderValueStart,\n      ariaLabel: this.renderAriaLabelStart,\n      ariaValueText: this.renderAriaValueTextStart,\n      ariaMin: this.min,\n      ariaMax: this.valueEnd ?? this.max,\n    };\n\n    const inputEndProps = {\n      start: false,\n      value: this.renderValueEnd,\n      ariaLabel: this.renderAriaLabelEnd,\n      ariaValueText: this.renderAriaValueTextEnd,\n      ariaMin: this.range ? this.valueStart ?? this.min : this.min,\n      ariaMax: this.max,\n    };\n\n    const handleStartProps = {\n      start: true,\n      hover: this.handleStartHover,\n      label: labelStart,\n    };\n\n    const handleEndProps = {\n      start: false,\n      hover: this.handleEndHover,\n      label: labelEnd,\n    };\n\n    const handleContainerClasses = {\n      hover: this.handleStartHover || this.handleEndHover,\n    };\n\n    return html` <div\n      class=\"container ${classMap(containerClasses)}\"\n      style=${styleMap(containerStyles)}>\n      ${when(this.range, () => this.renderInput(inputStartProps))}\n      ${this.renderInput(inputEndProps)} ${this.renderTrack()}\n      <div class=\"handleContainerPadded\">\n        <div class=\"handleContainerBlock\">\n          <div class=\"handleContainer ${classMap(handleContainerClasses)}\">\n            ${when(this.range, () => this.renderHandle(handleStartProps))}\n            ${this.renderHandle(handleEndProps)}\n          </div>\n        </div>\n      </div>\n    </div>`;\n  }\n\n  private renderTrack() {\n    return html`\n      <div class=\"track\"></div>\n      ${this.ticks ? html`<div class=\"tickmarks\"></div>` : nothing}\n    `;\n  }\n\n  private renderLabel(value: string) {\n    return html`<div class=\"label\" aria-hidden=\"true\">\n      <span class=\"labelContent\" part=\"label\">${value}</span>\n    </div>`;\n  }\n\n  private renderHandle({\n    start,\n    hover,\n    label,\n  }: {\n    start: boolean;\n    hover: boolean;\n    label: string;\n  }) {\n    const onTop = !this.disabled && start === this.startOnTop;\n    const isOverlapping = !this.disabled && this.handlesOverlapping;\n    const name = start ? 'start' : 'end';\n    return html`<div\n      class=\"handle ${classMap({\n        [name]: true,\n        hover,\n        onTop,\n        isOverlapping,\n      })}\">\n      <md-focus-ring part=\"focus-ring\" for=${name}></md-focus-ring>\n      <md-ripple\n        for=${name}\n        class=${name}\n        ?disabled=${this.disabled}></md-ripple>\n      <div class=\"handleNub\">\n        <md-elevation part=\"elevation\"></md-elevation>\n      </div>\n      ${when(this.labeled, () => this.renderLabel(label))}\n    </div>`;\n  }\n\n  private renderInput({\n    start,\n    value,\n    ariaLabel,\n    ariaValueText,\n    ariaMin,\n    ariaMax,\n  }: {\n    start: boolean;\n    value?: number;\n    ariaLabel: string;\n    ariaValueText: string;\n    ariaMin: number;\n    ariaMax: number;\n  }) {\n    // Slider requires min/max set to the overall min/max for both inputs.\n    // This is reported to screen readers, which is why we need aria-valuemin\n    // and aria-valuemax.\n    const name = start ? `start` : `end`;\n    return html`<input\n      type=\"range\"\n      class=\"${classMap({\n        start,\n        end: !start,\n      })}\"\n      @focus=${this.handleFocus}\n      @pointerdown=${this.handleDown}\n      @pointerup=${this.handleUp}\n      @pointerenter=${this.handleEnter}\n      @pointermove=${this.handleMove}\n      @pointerleave=${this.handleLeave}\n      @keydown=${this.handleKeydown}\n      @keyup=${this.handleKeyup}\n      @input=${this.handleInput}\n      @change=${this.handleChange}\n      id=${name}\n      .disabled=${this.disabled}\n      .min=${String(this.min)}\n      aria-valuemin=${ariaMin}\n      .max=${String(this.max)}\n      aria-valuemax=${ariaMax}\n      .step=${String(this.step)}\n      .value=${String(value)}\n      .tabIndex=${start ? 1 : 0}\n      aria-label=${ariaLabel || nothing}\n      aria-valuetext=${ariaValueText} />`;\n  }\n\n  private async toggleRippleHover(\n    ripple: Promise<MdRipple | null>,\n    hovering: boolean,\n  ) {\n    const rippleEl = await ripple;\n    if (!rippleEl) {\n      return;\n    }\n    // TODO(b/269799771): improve slider ripple connection\n    if (hovering) {\n      rippleEl.handlePointerenter(\n        new PointerEvent('pointerenter', {\n          isPrimary: true,\n          pointerId: this.ripplePointerId,\n        }),\n      );\n    } else {\n      rippleEl.handlePointerleave(\n        new PointerEvent('pointerleave', {\n          isPrimary: true,\n          pointerId: this.ripplePointerId,\n        }),\n      );\n    }\n  }\n\n  private handleFocus(event: Event) {\n    this.updateOnTop(event.target as HTMLInputElement);\n  }\n\n  private startAction(event: Event) {\n    const target = event.target as HTMLInputElement;\n    const fixed =\n      target === this.inputStart ? this.inputEnd! : this.inputStart!;\n    this.action = {\n      canFlip: event.type === 'pointerdown',\n      flipped: false,\n      target,\n      fixed,\n      values: new Map([\n        [target, target.valueAsNumber],\n        [fixed, fixed?.valueAsNumber],\n      ]),\n    };\n  }\n\n  private finishAction(event: Event) {\n    this.action = undefined;\n  }\n\n  private handleKeydown(event: KeyboardEvent) {\n    this.startAction(event);\n  }\n\n  private handleKeyup(event: KeyboardEvent) {\n    this.finishAction(event);\n  }\n\n  private handleDown(event: PointerEvent) {\n    this.startAction(event);\n    this.ripplePointerId = event.pointerId;\n    const isStart = (event.target as HTMLInputElement) === this.inputStart;\n    // Since handle moves to pointer on down and there may not be a move,\n    // it needs to be considered hovered..\n    this.handleStartHover =\n      !this.disabled && isStart && Boolean(this.handleStart);\n    this.handleEndHover = !this.disabled && !isStart && Boolean(this.handleEnd);\n  }\n\n  private async handleUp(event: PointerEvent) {\n    if (!this.action) {\n      return;\n    }\n\n    const {target, values, flipped} = this.action;\n    //  Async here for Firefox because input can be after pointerup\n    //  when value is calmped.\n    await new Promise(requestAnimationFrame);\n    if (target !== undefined) {\n      // Ensure Safari focuses input so label renders.\n      // Ensure any flipped input is focused so the tab order is right.\n      target.focus();\n      // When action is flipped, change must be fired manually since the\n      // real event target did not change.\n      if (flipped && target.valueAsNumber !== values.get(target)!) {\n        target.dispatchEvent(new Event('change', {bubbles: true}));\n      }\n    }\n    this.finishAction(event);\n  }\n\n  /**\n   * The move handler tracks handle hovering to facilitate proper ripple\n   * behavior on the slider handle. This is needed because user interaction with\n   * the native input is leveraged to position the handle. Because the separate\n   * displayed handle element has pointer events disabled (to allow interaction\n   * with the input) and the input's handle is a pseudo-element, neither can be\n   * the ripple's interactive element. Therefore the input is the ripple's\n   * interactive element and has a `ripple` directive; however the ripple\n   * is gated on the handle being hovered. In addition, because the ripple\n   * hover state is being specially handled, it must be triggered independent\n   * of the directive. This is done based on the hover state when the\n   * slider is updated.\n   */\n  private handleMove(event: PointerEvent) {\n    this.handleStartHover = !this.disabled && inBounds(event, this.handleStart);\n    this.handleEndHover = !this.disabled && inBounds(event, this.handleEnd);\n  }\n\n  private handleEnter(event: PointerEvent) {\n    this.handleMove(event);\n  }\n\n  private handleLeave() {\n    this.handleStartHover = false;\n    this.handleEndHover = false;\n  }\n\n  private updateOnTop(input: HTMLInputElement) {\n    this.startOnTop = input.classList.contains('start');\n  }\n\n  private needsClamping() {\n    if (!this.action) {\n      return false;\n    }\n\n    const {target, fixed} = this.action;\n    const isStart = target === this.inputStart;\n    return isStart\n      ? target.valueAsNumber > fixed.valueAsNumber\n      : target.valueAsNumber < fixed.valueAsNumber;\n  }\n\n  // if start/end start coincident and the first drag input would e.g. move\n  // start > end, avoid clamping and \"flip\" to use the other input\n  // as the action target.\n  private isActionFlipped() {\n    const {action} = this;\n    if (!action) {\n      return false;\n    }\n\n    const {target, fixed, values} = action;\n    if (action.canFlip) {\n      const coincident = values.get(target) === values.get(fixed);\n      if (coincident && this.needsClamping()) {\n        action.canFlip = false;\n        action.flipped = true;\n        action.target = fixed;\n        action.fixed = target;\n      }\n    }\n    return action.flipped;\n  }\n\n  // when flipped, apply the drag input to the flipped target and reset\n  // the actual target.\n  private flipAction() {\n    if (!this.action) {\n      return false;\n    }\n\n    const {target, fixed, values} = this.action;\n    const changed = target.valueAsNumber !== fixed.valueAsNumber;\n    target.valueAsNumber = fixed.valueAsNumber;\n    fixed.valueAsNumber = values.get(fixed)!;\n    return changed;\n  }\n\n  // clamp such that start does not move beyond end and visa versa.\n  private clampAction() {\n    if (!this.needsClamping() || !this.action) {\n      return false;\n    }\n    const {target, fixed} = this.action;\n    target.valueAsNumber = fixed.valueAsNumber;\n    return true;\n  }\n\n  private handleInput(event: InputEvent) {\n    // avoid processing a re-dispatched event\n    if (this.isRedispatchingEvent) {\n      return;\n    }\n    let stopPropagation = false;\n    let redispatch = false;\n    if (this.range) {\n      if (this.isActionFlipped()) {\n        stopPropagation = true;\n        redispatch = this.flipAction();\n      }\n      if (this.clampAction()) {\n        stopPropagation = true;\n        redispatch = false;\n      }\n    }\n    const target = event.target as HTMLInputElement;\n    this.updateOnTop(target);\n    // update value only on interaction\n    if (this.range) {\n      this.valueStart = this.inputStart!.valueAsNumber;\n      this.valueEnd = this.inputEnd!.valueAsNumber;\n    } else {\n      this.value = this.inputEnd!.valueAsNumber;\n    }\n    // control external visibility of input event\n    if (stopPropagation) {\n      event.stopPropagation();\n    }\n    // ensure event path is correct when flipped.\n    if (redispatch) {\n      this.isRedispatchingEvent = true;\n      redispatchEvent(target, event);\n      this.isRedispatchingEvent = false;\n    }\n  }\n\n  private handleChange(event: Event) {\n    // prevent keyboard triggered changes from dispatching for\n    // clamped values; note, this only occurs for keyboard\n    const changeTarget = event.target as HTMLInputElement;\n    const {target, values} = this.action ?? {};\n    const squelch =\n      target && target.valueAsNumber === values!.get(changeTarget)!;\n    if (!squelch) {\n      redispatchEvent(this, event);\n    }\n    // ensure keyboard triggered change clears action.\n    this.finishAction(event);\n  }\n\n  // Writable mixin properties for lit-html binding, needed for lit-analyzer\n  declare disabled: boolean;\n  declare name: string;\n\n  override [getFormValue]() {\n    if (this.range) {\n      const data = new FormData();\n      data.append(this.nameStart, String(this.valueStart));\n      data.append(this.nameEnd, String(this.valueEnd));\n      return data;\n    }\n\n    return String(this.value);\n  }\n\n  override formResetCallback() {\n    if (this.range) {\n      const valueStart = this.getAttribute('value-start');\n      this.valueStart = valueStart !== null ? Number(valueStart) : undefined;\n      const valueEnd = this.getAttribute('value-end');\n      this.valueEnd = valueEnd !== null ? Number(valueEnd) : undefined;\n      return;\n    }\n    const value = this.getAttribute('value');\n    this.value = value !== null ? Number(value) : undefined;\n  }\n\n  override formStateRestoreCallback(\n    state: string | Array<[string, string]> | null,\n  ) {\n    if (Array.isArray(state)) {\n      const [[, valueStart], [, valueEnd]] = state;\n      this.valueStart = Number(valueStart);\n      this.valueEnd = Number(valueEnd);\n      this.range = true;\n      return;\n    }\n\n    this.value = Number(state);\n    this.range = false;\n  }\n}\n\nfunction inBounds({x, y}: PointerEvent, element?: HTMLElement | null) {\n  if (!element) {\n    return false;\n  }\n  const {top, left, bottom, right} = element.getBoundingClientRect();\n  return x >= left && x <= right && y >= top && y <= bottom;\n}\n\nfunction isOverlapping(\n  elA: Element | null | undefined,\n  elB: Element | null | undefined,\n) {\n  if (!(elA && elB)) {\n    return false;\n  }\n  const a = elA.getBoundingClientRect();\n  const b = elB.getBoundingClientRect();\n  return !(\n    a.top > b.bottom ||\n    a.right < b.left ||\n    a.bottom < b.top ||\n    a.left > b.right\n  );\n}\n\ninterface Action {\n  canFlip: boolean;\n  flipped: boolean;\n  target: HTMLInputElement;\n  fixed: HTMLInputElement;\n  values: Map<HTMLInputElement | undefined, number | undefined>;\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n// Generated stylesheet for ./slider/internal/slider-styles.css.\nimport {css} from 'lit';\nexport const styles = css`:host{--_active-track-color: var(--md-slider-active-track-color, var(--md-sys-color-primary, #6750a4));--_active-track-height: var(--md-slider-active-track-height, 4px);--_active-track-shape: var(--md-slider-active-track-shape, var(--md-sys-shape-corner-full, 9999px));--_disabled-active-track-color: var(--md-slider-disabled-active-track-color, var(--md-sys-color-on-surface, #1d1b20));--_disabled-active-track-opacity: var(--md-slider-disabled-active-track-opacity, 0.38);--_disabled-handle-color: var(--md-slider-disabled-handle-color, var(--md-sys-color-on-surface, #1d1b20));--_disabled-handle-elevation: var(--md-slider-disabled-handle-elevation, 0);--_disabled-inactive-track-color: var(--md-slider-disabled-inactive-track-color, var(--md-sys-color-on-surface, #1d1b20));--_disabled-inactive-track-opacity: var(--md-slider-disabled-inactive-track-opacity, 0.12);--_focus-handle-color: var(--md-slider-focus-handle-color, var(--md-sys-color-primary, #6750a4));--_handle-color: var(--md-slider-handle-color, var(--md-sys-color-primary, #6750a4));--_handle-elevation: var(--md-slider-handle-elevation, 1);--_handle-height: var(--md-slider-handle-height, 20px);--_handle-shadow-color: var(--md-slider-handle-shadow-color, var(--md-sys-color-shadow, #000));--_handle-shape: var(--md-slider-handle-shape, var(--md-sys-shape-corner-full, 9999px));--_handle-width: var(--md-slider-handle-width, 20px);--_hover-handle-color: var(--md-slider-hover-handle-color, var(--md-sys-color-primary, #6750a4));--_hover-state-layer-color: var(--md-slider-hover-state-layer-color, var(--md-sys-color-primary, #6750a4));--_hover-state-layer-opacity: var(--md-slider-hover-state-layer-opacity, 0.08);--_inactive-track-color: var(--md-slider-inactive-track-color, var(--md-sys-color-surface-container-highest, #e6e0e9));--_inactive-track-height: var(--md-slider-inactive-track-height, 4px);--_inactive-track-shape: var(--md-slider-inactive-track-shape, var(--md-sys-shape-corner-full, 9999px));--_label-container-color: var(--md-slider-label-container-color, var(--md-sys-color-primary, #6750a4));--_label-container-height: var(--md-slider-label-container-height, 28px);--_pressed-handle-color: var(--md-slider-pressed-handle-color, var(--md-sys-color-primary, #6750a4));--_pressed-state-layer-color: var(--md-slider-pressed-state-layer-color, var(--md-sys-color-primary, #6750a4));--_pressed-state-layer-opacity: var(--md-slider-pressed-state-layer-opacity, 0.12);--_state-layer-size: var(--md-slider-state-layer-size, 40px);--_with-overlap-handle-outline-color: var(--md-slider-with-overlap-handle-outline-color, var(--md-sys-color-on-primary, #fff));--_with-overlap-handle-outline-width: var(--md-slider-with-overlap-handle-outline-width, 1px);--_with-tick-marks-active-container-color: var(--md-slider-with-tick-marks-active-container-color, var(--md-sys-color-on-primary, #fff));--_with-tick-marks-container-size: var(--md-slider-with-tick-marks-container-size, 2px);--_with-tick-marks-disabled-container-color: var(--md-slider-with-tick-marks-disabled-container-color, var(--md-sys-color-on-surface, #1d1b20));--_with-tick-marks-inactive-container-color: var(--md-slider-with-tick-marks-inactive-container-color, var(--md-sys-color-on-surface-variant, #49454f));--_label-text-color: var(--md-slider-label-text-color, var(--md-sys-color-on-primary, #fff));--_label-text-font: var(--md-slider-label-text-font, var(--md-sys-typescale-label-medium-font, var(--md-ref-typeface-plain, Roboto)));--_label-text-line-height: var(--md-slider-label-text-line-height, var(--md-sys-typescale-label-medium-line-height, 1rem));--_label-text-size: var(--md-slider-label-text-size, var(--md-sys-typescale-label-medium-size, 0.75rem));--_label-text-weight: var(--md-slider-label-text-weight, var(--md-sys-typescale-label-medium-weight, var(--md-ref-typeface-weight-medium, 500)));--_start-fraction: 0;--_end-fraction: 0;--_tick-count: 0;display:inline-flex;vertical-align:middle;min-inline-size:200px;--md-elevation-level: var(--_handle-elevation);--md-elevation-shadow-color: var(--_handle-shadow-color)}md-focus-ring{height:48px;inset:unset;width:48px}md-elevation{transition-duration:250ms}@media(prefers-reduced-motion){.label{transition-duration:0}}:host([disabled]){opacity:var(--_disabled-active-track-opacity);--md-elevation-level: var(--_disabled-handle-elevation)}.container{flex:1;display:flex;align-items:center;position:relative;block-size:var(--_state-layer-size);pointer-events:none;touch-action:none}.track,.tickmarks{position:absolute;inset:0;display:flex;align-items:center}.track::before,.tickmarks::before,.track::after,.tickmarks::after{position:absolute;content:\"\";inset-inline-start:calc(var(--_state-layer-size)/2 - var(--_with-tick-marks-container-size));inset-inline-end:calc(var(--_state-layer-size)/2 - var(--_with-tick-marks-container-size));background-size:calc((100% - var(--_with-tick-marks-container-size)*2)/var(--_tick-count)) 100%}.track::before,.tickmarks::before{block-size:var(--_inactive-track-height);border-radius:var(--_inactive-track-shape)}.track::before{background:var(--_inactive-track-color)}.tickmarks::before{background-image:radial-gradient(circle at var(--_with-tick-marks-container-size) center, var(--_with-tick-marks-inactive-container-color) 0, var(--_with-tick-marks-inactive-container-color) calc(var(--_with-tick-marks-container-size) / 2), transparent calc(var(--_with-tick-marks-container-size) / 2))}:host([disabled]) .track::before{opacity:calc(1/var(--_disabled-active-track-opacity)*var(--_disabled-inactive-track-opacity));background:var(--_disabled-inactive-track-color)}.track::after,.tickmarks::after{block-size:var(--_active-track-height);border-radius:var(--_active-track-shape);clip-path:inset(0 calc(var(--_with-tick-marks-container-size) * min((1 - var(--_end-fraction)) * 1000000000, 1) + (100% - var(--_with-tick-marks-container-size) * 2) * (1 - var(--_end-fraction))) 0 calc(var(--_with-tick-marks-container-size) * min(var(--_start-fraction) * 1000000000, 1) + (100% - var(--_with-tick-marks-container-size) * 2) * var(--_start-fraction)))}.track::after{background:var(--_active-track-color)}.tickmarks::after{background-image:radial-gradient(circle at var(--_with-tick-marks-container-size) center, var(--_with-tick-marks-active-container-color) 0, var(--_with-tick-marks-active-container-color) calc(var(--_with-tick-marks-container-size) / 2), transparent calc(var(--_with-tick-marks-container-size) / 2))}.track:dir(rtl)::after{clip-path:inset(0 calc(var(--_with-tick-marks-container-size) * min(var(--_start-fraction) * 1000000000, 1) + (100% - var(--_with-tick-marks-container-size) * 2) * var(--_start-fraction)) 0 calc(var(--_with-tick-marks-container-size) * min((1 - var(--_end-fraction)) * 1000000000, 1) + (100% - var(--_with-tick-marks-container-size) * 2) * (1 - var(--_end-fraction))))}.tickmarks:dir(rtl)::after{clip-path:inset(0 calc(var(--_with-tick-marks-container-size) * min(var(--_start-fraction) * 1000000000, 1) + (100% - var(--_with-tick-marks-container-size) * 2) * var(--_start-fraction)) 0 calc(var(--_with-tick-marks-container-size) * min((1 - var(--_end-fraction)) * 1000000000, 1) + (100% - var(--_with-tick-marks-container-size) * 2) * (1 - var(--_end-fraction))))}:host([disabled]) .track::after{background:var(--_disabled-active-track-color)}:host([disabled]) .tickmarks::before{background-image:radial-gradient(circle at var(--_with-tick-marks-container-size) center, var(--_with-tick-marks-disabled-container-color) 0, var(--_with-tick-marks-disabled-container-color) calc(var(--_with-tick-marks-container-size) / 2), transparent calc(var(--_with-tick-marks-container-size) / 2))}.handleContainerPadded{position:relative;block-size:100%;inline-size:100%;padding-inline:calc(var(--_state-layer-size)/2)}.handleContainerBlock{position:relative;block-size:100%;inline-size:100%}.handleContainer{position:absolute;inset-block-start:0;inset-block-end:0;inset-inline-start:calc(100%*var(--_start-fraction));inline-size:calc(100%*(var(--_end-fraction) - var(--_start-fraction)))}.handle{position:absolute;block-size:var(--_state-layer-size);inline-size:var(--_state-layer-size);border-radius:var(--_handle-shape);display:flex;place-content:center;place-items:center}.handleNub{position:absolute;height:var(--_handle-height);width:var(--_handle-width);border-radius:var(--_handle-shape);background:var(--_handle-color)}:host([disabled]) .handleNub{background:var(--_disabled-handle-color)}input.end:focus~.handleContainerPadded .handle.end>.handleNub,input.start:focus~.handleContainerPadded .handle.start>.handleNub{background:var(--_focus-handle-color)}.container>.handleContainerPadded .handle.hover>.handleNub{background:var(--_hover-handle-color)}:host(:not([disabled])) input.end:active~.handleContainerPadded .handle.end>.handleNub,:host(:not([disabled])) input.start:active~.handleContainerPadded .handle.start>.handleNub{background:var(--_pressed-handle-color)}.onTop.isOverlapping .label,.onTop.isOverlapping .label::before{outline:var(--_with-overlap-handle-outline-color) solid var(--_with-overlap-handle-outline-width)}.onTop.isOverlapping .handleNub{border:var(--_with-overlap-handle-outline-color) solid var(--_with-overlap-handle-outline-width)}.handle.start{inset-inline-start:calc(0px - var(--_state-layer-size)/2)}.handle.end{inset-inline-end:calc(0px - var(--_state-layer-size)/2)}.label{position:absolute;box-sizing:border-box;display:flex;padding:4px;place-content:center;place-items:center;border-radius:var(--md-sys-shape-corner-full, 9999px);color:var(--_label-text-color);font-family:var(--_label-text-font);font-size:var(--_label-text-size);line-height:var(--_label-text-line-height);font-weight:var(--_label-text-weight);inset-block-end:100%;min-inline-size:var(--_label-container-height);min-block-size:var(--_label-container-height);background:var(--_label-container-color);transition:transform 100ms cubic-bezier(0.2, 0, 0, 1);transform-origin:center bottom;transform:scale(0)}:host(:focus-within) .label,.handleContainer.hover .label,:where(:has(input:active)) .label{transform:scale(1)}.label::before,.label::after{position:absolute;display:block;content:\"\";background:inherit}.label::before{inline-size:calc(var(--_label-container-height)/2);block-size:calc(var(--_label-container-height)/2);bottom:calc(var(--_label-container-height)/-10);transform:rotate(45deg)}.label::after{inset:0px;border-radius:inherit}.labelContent{z-index:1}input[type=range]{opacity:0;-webkit-tap-highlight-color:rgba(0,0,0,0);position:absolute;box-sizing:border-box;height:100%;width:100%;margin:0;background:rgba(0,0,0,0);cursor:pointer;pointer-events:auto;appearance:none}input[type=range]:focus{outline:none}::-webkit-slider-runnable-track{-webkit-appearance:none}::-moz-range-track{appearance:none}::-webkit-slider-thumb{-webkit-appearance:none;appearance:none;block-size:var(--_handle-height);inline-size:var(--_handle-width);opacity:0;z-index:2}input.end::-webkit-slider-thumb{--_track-and-knob-padding: calc( (var(--_state-layer-size) - var(--_handle-width)) / 2 );--_x-translate: calc( var(--_track-and-knob-padding) - 2 * var(--_end-fraction) * var(--_track-and-knob-padding) );transform:translateX(var(--_x-translate))}input.end:dir(rtl)::-webkit-slider-thumb{transform:translateX(calc(-1 * var(--_x-translate)))}input.start::-webkit-slider-thumb{--_track-and-knob-padding: calc( (var(--_state-layer-size) - var(--_handle-width)) / 2 );--_x-translate: calc( var(--_track-and-knob-padding) - 2 * var(--_start-fraction) * var(--_track-and-knob-padding) );transform:translateX(var(--_x-translate))}input.start:dir(rtl)::-webkit-slider-thumb{transform:translateX(calc(-1 * var(--_x-translate)))}::-moz-range-thumb{appearance:none;block-size:var(--_state-layer-size);inline-size:var(--_state-layer-size);transform:scaleX(0);opacity:0;z-index:2}.ranged input.start{clip-path:inset(0 calc(100% - (var(--_state-layer-size) / 2 + (100% - var(--_state-layer-size)) * (var(--_start-fraction) + (var(--_end-fraction) - var(--_start-fraction)) / 2))) 0 0)}.ranged input.start:dir(rtl){clip-path:inset(0 0 0 calc(100% - (var(--_state-layer-size) / 2 + (100% - var(--_state-layer-size)) * (var(--_start-fraction) + (var(--_end-fraction) - var(--_start-fraction)) / 2))))}.ranged input.end{clip-path:inset(0 0 0 calc(var(--_state-layer-size) / 2 + (100% - var(--_state-layer-size)) * (var(--_start-fraction) + (var(--_end-fraction) - var(--_start-fraction)) / 2)))}.ranged input.end:dir(rtl){clip-path:inset(0 calc(var(--_state-layer-size) / 2 + (100% - var(--_state-layer-size)) * (var(--_start-fraction) + (var(--_end-fraction) - var(--_start-fraction)) / 2)) 0 0)}.onTop{z-index:1}.handle{--md-ripple-hover-color: var(--_hover-state-layer-color);--md-ripple-hover-opacity: var(--_hover-state-layer-opacity);--md-ripple-pressed-color: var(--_pressed-state-layer-color);--md-ripple-pressed-opacity: var(--_pressed-state-layer-opacity)}md-ripple{border-radius:50%;height:var(--_state-layer-size);width:var(--_state-layer-size)}\n`;\n", "/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {styles as forcedColorsStyles} from './internal/forced-colors-styles.js';\nimport {Slider} from './internal/slider.js';\nimport {styles} from './internal/slider-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-slider': MdSlider;\n  }\n}\n\n/**\n * @summary Sliders allow users to view and select a value (or range) along\n * a track.\n *\n * @description\n * Changes made with sliders are immediate, allowing the user to make slider\n * adjustments while determining a selection. Sliders shouldn’t be used to\n * adjust settings with any delay in providing user feedback. Sliders reflect\n * the current state of the settings they control.\n *\n * __Example usages:__\n * - Sliders are ideal for adjusting settings such as volume and brightness, or\n * for applying image filters.\n *\n * @final\n * @suppress {visibility}\n */\n@customElement('md-slider')\nexport class MdSlider extends Slider {\n  static override styles: CSSResultOrNative[] = [styles, forcedColorsStyles];\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOO,IAAM,SAAS;;;;ACiChB,SAAU,KACd,WACA,UACA,WAAmC;AAEnC,SAAO,YAAY,SAAS,SAAS,IAAI,YAAY,SAAS;AAChE;;;ACZA,IAAM,kBAAkB,mBACtB,oBAAoB,sBAAsB,UAAU,CAAC,CAAC;AAclD,IAAO,SAAP,cAAsB,gBAAe;;;;;;EAqGzC,IAAI,YAAS;AACX,WAAO,KAAK,aAAa,YAAY,KAAK,KAAK;EACjD;EACA,IAAI,UAAU,MAAY;AACxB,SAAK,aAAa,cAAc,IAAI;EACtC;;;;;;EAOA,IAAI,UAAO;AACT,WAAO,KAAK,aAAa,UAAU,KAAK,KAAK;EAC/C;EACA,IAAI,QAAQ,MAAY;AACtB,SAAK,aAAa,YAAY,IAAI;EACpC;;;EA0BA,IAAY,uBAAoB;AAE9B,UAAM,EAAC,UAAS,IAAI;AACpB,WACE,KAAK,kBACJ,aAAa,GAAG,SAAS,YAC1B,KAAK,mBACL,OAAO,KAAK,UAAU;EAE1B;EAEA,IAAY,2BAAwB;AAClC,WACE,KAAK,sBAAsB,KAAK,mBAAmB,OAAO,KAAK,UAAU;EAE7E;;;;EAKA,IAAY,qBAAkB;AAE5B,UAAM,EAAC,UAAS,IAAI;AACpB,QAAI,KAAK,OAAO;AACd,aACE,KAAK,gBACJ,aAAa,GAAG,SAAS,UAC1B,KAAK,iBACL,OAAO,KAAK,QAAQ;IAExB;AAEA,WAAO,aAAa,KAAK,cAAc,OAAO,KAAK,KAAK;EAC1D;EAEA,IAAY,yBAAsB;AAChC,QAAI,KAAK,OAAO;AACd,aACE,KAAK,oBAAoB,KAAK,iBAAiB,OAAO,KAAK,QAAQ;IAEvE;AAGA,UAAM,EAAC,cAAa,IAAI;AACxB,WAAO,iBAAiB,KAAK,cAAc,OAAO,KAAK,KAAK;EAC9D;EAUA,cAAA;AACE,UAAK;AA9LmB,SAAA,MAAM;AAKN,SAAA,MAAM;AAqBM,SAAA,aAAa;AAMP,SAAA,kBAAkB;AAMpB,SAAA,gBAAgB;AAMf,SAAA,iBAAiB;AAMb,SAAA,qBAAqB;AAM3B,SAAA,eAAe;AAMX,SAAA,mBAAmB;AAKtC,SAAA,OAAO;AAKN,SAAA,QAAQ;AAKR,SAAA,UAAU;AAOV,SAAA,QAAQ;AAuClB,SAAA,mBAAmB;AACnB,SAAA,iBAAiB;AAEjB,SAAA,aAAa;AACb,SAAA,qBAAqB;AAuD9B,SAAA,kBAAkB;AAGlB,SAAA,uBAAuB;AAM7B,QAAI,CAAC,UAAU;AACb,WAAK,iBAAiB,SAAS,CAAC,UAAqB;AACnD,YAAI,CAAC,kBAAkB,KAAK,KAAK,CAAC,KAAK,UAAU;AAC/C;QACF;AACA,aAAK,MAAK;AACV,gCAAwB,KAAK,QAAQ;MACvC,CAAC;IACH;EACF;EAES,QAAK;AACZ,SAAK,UAAU,MAAK;EACtB;EAEmB,WAAW,SAAuB;AACnD,SAAK,mBAAmB,QAAQ,IAAI,YAAY,IAC5C,KAAK,aACL,KAAK,YAAY;AACrB,UAAM,kBACH,QAAQ,IAAI,UAAU,KAAK,KAAK,SAAU,QAAQ,IAAI,OAAO;AAChE,SAAK,iBAAiB,kBAClB,KAAK,QACH,KAAK,WACL,KAAK,QACP,KAAK,UAAU;AAGnB,QAAI,QAAQ,IAAI,kBAAkB,MAAM,QAAW;AACjD,WAAK,kBAAkB,KAAK,aAAa,KAAK,gBAAgB;IAChE,WAAW,QAAQ,IAAI,gBAAgB,MAAM,QAAW;AACtD,WAAK,kBAAkB,KAAK,WAAW,KAAK,cAAc;IAC5D;EACF;EAEmB,QAAQ,SAAuB;AAKhD,QAAI,KAAK,OAAO;AACd,WAAK,mBAAmB,KAAK,WAAY;IAC3C;AACA,SAAK,iBAAiB,KAAK,SAAU;AAIrC,QAAI,KAAK,OAAO;AACd,YAAM,WAAW,KAAK,MAAM,KAAK,OAAO;AACxC,UAAI,KAAK,eAAe,QAAW;AACjC,aAAK,WAAY,gBAAgB,KAAK,MAAM;AAE5C,cAAM,IAAI,KAAK,WAAY;AAC3B,aAAK,aAAa,KAAK,mBAAmB;MAC5C;AACA,UAAI,KAAK,aAAa,QAAW;AAC/B,aAAK,SAAU,gBAAgB,KAAK,MAAM,IAAI;AAE9C,cAAM,IAAI,KAAK,SAAU;AACzB,aAAK,WAAW,KAAK,iBAAiB;MACxC;IACF,OAAO;AACL,WAAK,UAAU,KAAK;IACtB;AACA,QACE,QAAQ,IAAI,OAAO,KACnB,QAAQ,IAAI,kBAAkB,KAC9B,QAAQ,IAAI,gBAAgB,KAC5B,KAAK,iBACL;AAGA,YAAM,WAAW,KAAK,aAAa,cAAc,YAAY;AAC7D,YAAM,SAAS,KAAK,WAAW,cAAc,YAAY;AACzD,WAAK,qBAAqB,cAAc,UAAU,MAAM;IAC1D;AAGA,SAAK,cAAa;EACpB;EAEmB,SAAM;AACvB,UAAM,OAAO,KAAK,SAAS,IAAI,IAAI,KAAK;AACxC,UAAM,QAAQ,KAAK,IAAI,KAAK,MAAM,KAAK,KAAK,IAAI;AAChD,UAAM,gBAAgB,KAAK,UACrB,KAAK,oBAAoB,KAAK,OAAO,KAAK,OAAO,QACnD;AACJ,UAAM,gBAAgB,KAAK,kBAAkB,KAAK,OAAO,KAAK,OAAO;AACrE,UAAM,kBAAkB;;MAEtB,qBAAqB,OAAO,aAAa;MACzC,mBAAmB,OAAO,WAAW;;MAErC,iBAAiB,OAAO,QAAQ,IAAI;;AAEtC,UAAM,mBAAmB,EAAC,QAAQ,KAAK,MAAK;AAG5C,UAAM,aAAa,KAAK,mBAAmB,OAAO,KAAK,gBAAgB;AACvE,UAAM,YACH,KAAK,QAAQ,KAAK,gBAAgB,KAAK,eACxC,OAAO,KAAK,cAAc;AAE5B,UAAM,kBAAkB;MACtB,OAAO;MACP,OAAO,KAAK;MACZ,WAAW,KAAK;MAChB,eAAe,KAAK;MACpB,SAAS,KAAK;MACd,SAAS,KAAK,YAAY,KAAK;;AAGjC,UAAM,gBAAgB;MACpB,OAAO;MACP,OAAO,KAAK;MACZ,WAAW,KAAK;MAChB,eAAe,KAAK;MACpB,SAAS,KAAK,QAAQ,KAAK,cAAc,KAAK,MAAM,KAAK;MACzD,SAAS,KAAK;;AAGhB,UAAM,mBAAmB;MACvB,OAAO;MACP,OAAO,KAAK;MACZ,OAAO;;AAGT,UAAM,iBAAiB;MACrB,OAAO;MACP,OAAO,KAAK;MACZ,OAAO;;AAGT,UAAM,yBAAyB;MAC7B,OAAO,KAAK,oBAAoB,KAAK;;AAGvC,WAAO;yBACc,SAAS,gBAAgB,CAAC;cACrC,SAAS,eAAe,CAAC;QAC/B,KAAK,KAAK,OAAO,MAAM,KAAK,YAAY,eAAe,CAAC,CAAC;QACzD,KAAK,YAAY,aAAa,CAAC,IAAI,KAAK,YAAW,CAAE;;;wCAGrB,SAAS,sBAAsB,CAAC;cAC1D,KAAK,KAAK,OAAO,MAAM,KAAK,aAAa,gBAAgB,CAAC,CAAC;cAC3D,KAAK,aAAa,cAAc,CAAC;;;;;EAK7C;EAEQ,cAAW;AACjB,WAAO;;QAEH,KAAK,QAAQ,sCAAsC,OAAO;;EAEhE;EAEQ,YAAY,OAAa;AAC/B,WAAO;gDACqC,KAAK;;EAEnD;EAEQ,aAAa,EACnB,OACA,OACA,MAAK,GAKN;AACC,UAAM,QAAQ,CAAC,KAAK,YAAY,UAAU,KAAK;AAC/C,UAAMA,iBAAgB,CAAC,KAAK,YAAY,KAAK;AAC7C,UAAM,OAAO,QAAQ,UAAU;AAC/B,WAAO;sBACW,SAAS;MACvB,CAAC,IAAI,GAAG;MACR;MACA;MACA,eAAAA;KACD,CAAC;6CACqC,IAAI;;cAEnC,IAAI;gBACF,IAAI;oBACA,KAAK,QAAQ;;;;QAIzB,KAAK,KAAK,SAAS,MAAM,KAAK,YAAY,KAAK,CAAC,CAAC;;EAEvD;EAEQ,YAAY,EAClB,OACA,OACA,WACA,eACA,SACA,QAAO,GAQR;AAIC,UAAM,OAAO,QAAQ,UAAU;AAC/B,WAAO;;eAEI,SAAS;MAChB;MACA,KAAK,CAAC;KACP,CAAC;eACO,KAAK,WAAW;qBACV,KAAK,UAAU;mBACjB,KAAK,QAAQ;sBACV,KAAK,WAAW;qBACjB,KAAK,UAAU;sBACd,KAAK,WAAW;iBACrB,KAAK,aAAa;eACpB,KAAK,WAAW;eAChB,KAAK,WAAW;gBACf,KAAK,YAAY;WACtB,IAAI;kBACG,KAAK,QAAQ;aAClB,OAAO,KAAK,GAAG,CAAC;sBACP,OAAO;aAChB,OAAO,KAAK,GAAG,CAAC;sBACP,OAAO;cACf,OAAO,KAAK,IAAI,CAAC;eAChB,OAAO,KAAK,CAAC;kBACV,QAAQ,IAAI,CAAC;mBACZ,aAAa,OAAO;uBAChB,aAAa;EAClC;EAEQ,MAAM,kBACZ,QACA,UAAiB;AAEjB,UAAM,WAAW,MAAM;AACvB,QAAI,CAAC,UAAU;AACb;IACF;AAEA,QAAI,UAAU;AACZ,eAAS,mBACP,IAAI,aAAa,gBAAgB;QAC/B,WAAW;QACX,WAAW,KAAK;OACjB,CAAC;IAEN,OAAO;AACL,eAAS,mBACP,IAAI,aAAa,gBAAgB;QAC/B,WAAW;QACX,WAAW,KAAK;OACjB,CAAC;IAEN;EACF;EAEQ,YAAY,OAAY;AAC9B,SAAK,YAAY,MAAM,MAA0B;EACnD;EAEQ,YAAY,OAAY;AAC9B,UAAM,SAAS,MAAM;AACrB,UAAM,QACJ,WAAW,KAAK,aAAa,KAAK,WAAY,KAAK;AACrD,SAAK,SAAS;MACZ,SAAS,MAAM,SAAS;MACxB,SAAS;MACT;MACA;MACA,QAAQ,oBAAI,IAAI;QACd,CAAC,QAAQ,OAAO,aAAa;QAC7B,CAAC,OAAO,OAAO,aAAa;OAC7B;;EAEL;EAEQ,aAAa,OAAY;AAC/B,SAAK,SAAS;EAChB;EAEQ,cAAc,OAAoB;AACxC,SAAK,YAAY,KAAK;EACxB;EAEQ,YAAY,OAAoB;AACtC,SAAK,aAAa,KAAK;EACzB;EAEQ,WAAW,OAAmB;AACpC,SAAK,YAAY,KAAK;AACtB,SAAK,kBAAkB,MAAM;AAC7B,UAAM,UAAW,MAAM,WAAgC,KAAK;AAG5D,SAAK,mBACH,CAAC,KAAK,YAAY,WAAW,QAAQ,KAAK,WAAW;AACvD,SAAK,iBAAiB,CAAC,KAAK,YAAY,CAAC,WAAW,QAAQ,KAAK,SAAS;EAC5E;EAEQ,MAAM,SAAS,OAAmB;AACxC,QAAI,CAAC,KAAK,QAAQ;AAChB;IACF;AAEA,UAAM,EAAC,QAAQ,QAAQ,QAAO,IAAI,KAAK;AAGvC,UAAM,IAAI,QAAQ,qBAAqB;AACvC,QAAI,WAAW,QAAW;AAGxB,aAAO,MAAK;AAGZ,UAAI,WAAW,OAAO,kBAAkB,OAAO,IAAI,MAAM,GAAI;AAC3D,eAAO,cAAc,IAAI,MAAM,UAAU,EAAC,SAAS,KAAI,CAAC,CAAC;MAC3D;IACF;AACA,SAAK,aAAa,KAAK;EACzB;;;;;;;;;;;;;;EAeQ,WAAW,OAAmB;AACpC,SAAK,mBAAmB,CAAC,KAAK,YAAY,SAAS,OAAO,KAAK,WAAW;AAC1E,SAAK,iBAAiB,CAAC,KAAK,YAAY,SAAS,OAAO,KAAK,SAAS;EACxE;EAEQ,YAAY,OAAmB;AACrC,SAAK,WAAW,KAAK;EACvB;EAEQ,cAAW;AACjB,SAAK,mBAAmB;AACxB,SAAK,iBAAiB;EACxB;EAEQ,YAAY,OAAuB;AACzC,SAAK,aAAa,MAAM,UAAU,SAAS,OAAO;EACpD;EAEQ,gBAAa;AACnB,QAAI,CAAC,KAAK,QAAQ;AAChB,aAAO;IACT;AAEA,UAAM,EAAC,QAAQ,MAAK,IAAI,KAAK;AAC7B,UAAM,UAAU,WAAW,KAAK;AAChC,WAAO,UACH,OAAO,gBAAgB,MAAM,gBAC7B,OAAO,gBAAgB,MAAM;EACnC;;;;EAKQ,kBAAe;AACrB,UAAM,EAAC,OAAM,IAAI;AACjB,QAAI,CAAC,QAAQ;AACX,aAAO;IACT;AAEA,UAAM,EAAC,QAAQ,OAAO,OAAM,IAAI;AAChC,QAAI,OAAO,SAAS;AAClB,YAAM,aAAa,OAAO,IAAI,MAAM,MAAM,OAAO,IAAI,KAAK;AAC1D,UAAI,cAAc,KAAK,cAAa,GAAI;AACtC,eAAO,UAAU;AACjB,eAAO,UAAU;AACjB,eAAO,SAAS;AAChB,eAAO,QAAQ;MACjB;IACF;AACA,WAAO,OAAO;EAChB;;;EAIQ,aAAU;AAChB,QAAI,CAAC,KAAK,QAAQ;AAChB,aAAO;IACT;AAEA,UAAM,EAAC,QAAQ,OAAO,OAAM,IAAI,KAAK;AACrC,UAAM,UAAU,OAAO,kBAAkB,MAAM;AAC/C,WAAO,gBAAgB,MAAM;AAC7B,UAAM,gBAAgB,OAAO,IAAI,KAAK;AACtC,WAAO;EACT;;EAGQ,cAAW;AACjB,QAAI,CAAC,KAAK,cAAa,KAAM,CAAC,KAAK,QAAQ;AACzC,aAAO;IACT;AACA,UAAM,EAAC,QAAQ,MAAK,IAAI,KAAK;AAC7B,WAAO,gBAAgB,MAAM;AAC7B,WAAO;EACT;EAEQ,YAAY,OAAiB;AAEnC,QAAI,KAAK,sBAAsB;AAC7B;IACF;AACA,QAAI,kBAAkB;AACtB,QAAI,aAAa;AACjB,QAAI,KAAK,OAAO;AACd,UAAI,KAAK,gBAAe,GAAI;AAC1B,0BAAkB;AAClB,qBAAa,KAAK,WAAU;MAC9B;AACA,UAAI,KAAK,YAAW,GAAI;AACtB,0BAAkB;AAClB,qBAAa;MACf;IACF;AACA,UAAM,SAAS,MAAM;AACrB,SAAK,YAAY,MAAM;AAEvB,QAAI,KAAK,OAAO;AACd,WAAK,aAAa,KAAK,WAAY;AACnC,WAAK,WAAW,KAAK,SAAU;IACjC,OAAO;AACL,WAAK,QAAQ,KAAK,SAAU;IAC9B;AAEA,QAAI,iBAAiB;AACnB,YAAM,gBAAe;IACvB;AAEA,QAAI,YAAY;AACd,WAAK,uBAAuB;AAC5B,sBAAgB,QAAQ,KAAK;AAC7B,WAAK,uBAAuB;IAC9B;EACF;EAEQ,aAAa,OAAY;AAG/B,UAAM,eAAe,MAAM;AAC3B,UAAM,EAAC,QAAQ,OAAM,IAAI,KAAK,UAAU,CAAA;AACxC,UAAM,UACJ,UAAU,OAAO,kBAAkB,OAAQ,IAAI,YAAY;AAC7D,QAAI,CAAC,SAAS;AACZ,sBAAgB,MAAM,KAAK;IAC7B;AAEA,SAAK,aAAa,KAAK;EACzB;EAMS,CAAC,YAAY,IAAC;AACrB,QAAI,KAAK,OAAO;AACd,YAAM,OAAO,IAAI,SAAQ;AACzB,WAAK,OAAO,KAAK,WAAW,OAAO,KAAK,UAAU,CAAC;AACnD,WAAK,OAAO,KAAK,SAAS,OAAO,KAAK,QAAQ,CAAC;AAC/C,aAAO;IACT;AAEA,WAAO,OAAO,KAAK,KAAK;EAC1B;EAES,oBAAiB;AACxB,QAAI,KAAK,OAAO;AACd,YAAM,aAAa,KAAK,aAAa,aAAa;AAClD,WAAK,aAAa,eAAe,OAAO,OAAO,UAAU,IAAI;AAC7D,YAAM,WAAW,KAAK,aAAa,WAAW;AAC9C,WAAK,WAAW,aAAa,OAAO,OAAO,QAAQ,IAAI;AACvD;IACF;AACA,UAAM,QAAQ,KAAK,aAAa,OAAO;AACvC,SAAK,QAAQ,UAAU,OAAO,OAAO,KAAK,IAAI;EAChD;EAES,yBACPC,QAA8C;AAE9C,QAAI,MAAM,QAAQA,MAAK,GAAG;AACxB,YAAM,CAAC,CAAC,EAAE,UAAU,GAAG,CAAC,EAAE,QAAQ,CAAC,IAAIA;AACvC,WAAK,aAAa,OAAO,UAAU;AACnC,WAAK,WAAW,OAAO,QAAQ;AAC/B,WAAK,QAAQ;AACb;IACF;AAEA,SAAK,QAAQ,OAAOA,MAAK;AACzB,SAAK,QAAQ;EACf;;AA5sBgB,OAAA,oBAAoC;EAClD,GAAG,WAAW;EACd,gBAAgB;;AAMQ,WAAA;EAAzB,SAAS,EAAC,MAAM,OAAM,CAAC;;AAKE,WAAA;EAAzB,SAAS,EAAC,MAAM,OAAM,CAAC;;AAKE,WAAA;EAAzB,SAAS,EAAC,MAAM,OAAM,CAAC;;AAK4B,WAAA;EAAnD,SAAS,EAAC,MAAM,QAAQ,WAAW,cAAa,CAAC;;AAKA,WAAA;EAAjD,SAAS,EAAC,MAAM,QAAQ,WAAW,YAAW,CAAC;;AAMV,WAAA;EAArC,SAAS,EAAC,WAAW,cAAa,CAAC;;AAMQ,WAAA;EAA3C,SAAS,EAAC,WAAW,oBAAmB,CAAC;;AAMA,WAAA;EAAzC,SAAS,EAAC,WAAW,kBAAiB,CAAC;;AAMG,WAAA;EAA1C,SAAS,EAAC,WAAW,mBAAkB,CAAC;;AAMM,WAAA;EAA9C,SAAS,EAAC,WAAW,uBAAsB,CAAC;;AAMJ,WAAA;EAAxC,SAAS,EAAC,WAAW,iBAAgB,CAAC;;AAMM,WAAA;EAA5C,SAAS,EAAC,WAAW,qBAAoB,CAAC;;AAKjB,WAAA;EAAzB,SAAS,EAAC,MAAM,OAAM,CAAC;;AAKG,WAAA;EAA1B,SAAS,EAAC,MAAM,QAAO,CAAC;;AAKE,WAAA;EAA1B,SAAS,EAAC,MAAM,QAAO,CAAC;;AAOE,WAAA;EAA1B,SAAS,EAAC,MAAM,QAAO,CAAC;;AA0Bc,WAAA;EAAtC,MAAM,aAAa;;AACqB,WAAA;EAAxC,MAAM,eAAe;;AAEL,WAAA;EADhB,WAAW,iBAAiB;;AAGQ,WAAA;EAApC,MAAM,WAAW;;AACqB,WAAA;EAAtC,MAAM,aAAa;;AAEH,WAAA;EADhB,WAAW,eAAe;;AAMV,WAAA;EAAhB,MAAK;;AACW,WAAA;EAAhB,MAAK;;AAEW,WAAA;EAAhB,MAAK;;AACW,WAAA;EAAhB,MAAK;;AAEW,WAAA;EAAhB,MAAK;;AACW,WAAA;EAAhB,MAAK;;AAqkBR,SAAS,SAAS,EAAC,GAAG,EAAC,GAAiB,SAA4B;AAClE,MAAI,CAAC,SAAS;AACZ,WAAO;EACT;AACA,QAAM,EAAC,KAAK,MAAM,QAAQ,MAAK,IAAI,QAAQ,sBAAqB;AAChE,SAAO,KAAK,QAAQ,KAAK,SAAS,KAAK,OAAO,KAAK;AACrD;AAEA,SAAS,cACP,KACA,KAA+B;AAE/B,MAAI,EAAE,OAAO,MAAM;AACjB,WAAO;EACT;AACA,QAAM,IAAI,IAAI,sBAAqB;AACnC,QAAM,IAAI,IAAI,sBAAqB;AACnC,SAAO,EACL,EAAE,MAAM,EAAE,UACV,EAAE,QAAQ,EAAE,QACZ,EAAE,SAAS,EAAE,OACb,EAAE,OAAO,EAAE;AAEf;;;AClxBO,IAAMC,UAAS;;;;AC8Bf,IAAM,WAAN,MAAMC,kBAAiB,OAAM;;AAClB,SAAA,SAA8B,CAACC,SAAQ,MAAkB;AAD9D,WAAQ,WAAA;EADpB,cAAc,WAAW;GACb,QAAQ;", "names": ["isOverlapping", "state", "styles", "MdSlider", "styles"]}