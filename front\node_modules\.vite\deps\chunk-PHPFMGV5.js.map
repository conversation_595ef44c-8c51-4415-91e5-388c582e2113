{"version": 3, "sources": ["../../@material/web/chips/internal/chip.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport '../../focus/md-focus-ring.js';\nimport '../../ripple/ripple.js';\n\nimport {html, isServer, LitElement, PropertyValues, TemplateResult} from 'lit';\nimport {property} from 'lit/decorators.js';\nimport {ClassInfo, classMap} from 'lit/directives/class-map.js';\n\nimport {mixinDelegatesAria} from '../../internal/aria/delegate.js';\n\n// Separate variable needed for closure.\nconst chipBaseClass = mixinDelegatesAria(LitElement);\n\n/**\n * A chip component.\n *\n * @fires update-focus {Event} Dispatched when `disabled` is toggled. --bubbles\n */\nexport abstract class Chip extends chipBaseClass {\n  /** @nocollapse */\n  static override shadowRootOptions = {\n    ...LitElement.shadowRootOptions,\n    delegatesFocus: true,\n  };\n\n  /**\n   * Whether or not the chip is disabled.\n   *\n   * Disabled chips are not focusable, unless `always-focusable` is set.\n   */\n  @property({type: Boolean, reflect: true}) disabled = false;\n\n  /**\n   * Whether or not the chip is \"soft-disabled\" (disabled but still\n   * focusable).\n   *\n   * Use this when a chip needs increased visibility when disabled. See\n   * https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/#kbd_disabled_controls\n   * for more guidance on when this is needed.\n   */\n  @property({type: Boolean, attribute: 'soft-disabled', reflect: true})\n  softDisabled = false;\n\n  /**\n   * When true, allow disabled chips to be focused with arrow keys.\n   *\n   * Add this when a chip needs increased visibility when disabled. See\n   * https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/#kbd_disabled_controls\n   * for more guidance on when this is needed.\n   *\n   * @deprecated Use `softDisabled` instead of `alwaysFocusable` + `disabled`.\n   */\n  @property({type: Boolean, attribute: 'always-focusable'})\n  alwaysFocusable = false;\n\n  // TODO(b/350810013): remove the label property.\n  /**\n   * The label of the chip.\n   *\n   * @deprecated Set text as content of the chip instead.\n   */\n  @property() label = '';\n\n  /**\n   * Only needed for SSR.\n   *\n   * Add this attribute when a chip has a `slot=\"icon\"` to avoid a Flash Of\n   * Unstyled Content.\n   */\n  @property({type: Boolean, reflect: true, attribute: 'has-icon'}) hasIcon =\n    false;\n\n  /**\n   * The `id` of the action the primary focus ring and ripple are for.\n   * TODO(b/310046938): use the same id for both elements\n   */\n  protected abstract readonly primaryId: string;\n\n  /**\n   * Whether or not the primary ripple is disabled (defaults to `disabled`).\n   * Some chip actions such as links cannot be disabled.\n   */\n  protected get rippleDisabled() {\n    return this.disabled || this.softDisabled;\n  }\n\n  constructor() {\n    super();\n    if (!isServer) {\n      this.addEventListener('click', this.handleClick.bind(this));\n    }\n  }\n\n  override focus(options?: FocusOptions) {\n    if (this.disabled && !this.alwaysFocusable) {\n      return;\n    }\n\n    super.focus(options);\n  }\n\n  protected override render() {\n    return html`\n      <div class=\"container ${classMap(this.getContainerClasses())}\">\n        ${this.renderContainerContent()}\n      </div>\n    `;\n  }\n\n  protected override updated(changed: PropertyValues<Chip>) {\n    if (changed.has('disabled') && changed.get('disabled') !== undefined) {\n      this.dispatchEvent(new Event('update-focus', {bubbles: true}));\n    }\n  }\n\n  protected getContainerClasses(): ClassInfo {\n    return {\n      'disabled': this.disabled || this.softDisabled,\n      'has-icon': this.hasIcon,\n    };\n  }\n\n  protected renderContainerContent() {\n    return html`\n      ${this.renderOutline()}\n      <md-focus-ring part=\"focus-ring\" for=${this.primaryId}></md-focus-ring>\n      <md-ripple\n        for=${this.primaryId}\n        ?disabled=${this.rippleDisabled}></md-ripple>\n      ${this.renderPrimaryAction(this.renderPrimaryContent())}\n    `;\n  }\n\n  protected renderOutline() {\n    return html`<span class=\"outline\"></span>`;\n  }\n\n  protected renderLeadingIcon(): TemplateResult {\n    return html`<slot name=\"icon\" @slotchange=${this.handleIconChange}></slot>`;\n  }\n\n  protected abstract renderPrimaryAction(content: unknown): unknown;\n\n  private renderPrimaryContent() {\n    return html`\n      <span class=\"leading icon\" aria-hidden=\"true\">\n        ${this.renderLeadingIcon()}\n      </span>\n      <span class=\"label\">\n        <span class=\"label-text\" id=\"label\">\n          ${this.label ? this.label : html`<slot></slot>`}\n        </span>\n      </span>\n      <span class=\"touch\"></span>\n    `;\n  }\n\n  private handleIconChange(event: Event) {\n    const slot = event.target as HTMLSlotElement;\n    this.hasIcon = slot.assignedElements({flatten: true}).length > 0;\n  }\n\n  private handleClick(event: Event) {\n    // If the chip is soft-disabled or disabled + always-focusable, we need to\n    // explicitly prevent the click from propagating to other event listeners\n    // as well as prevent the default action.\n    if (this.softDisabled || (this.disabled && this.alwaysFocusable)) {\n      event.stopImmediatePropagation();\n      event.preventDefault();\n      return;\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;AAgBA,IAAM,gBAAgB,mBAAmB,UAAU;AAO7C,IAAgB,OAAhB,cAA6B,cAAa;;;;;EAgE9C,IAAc,iBAAc;AAC1B,WAAO,KAAK,YAAY,KAAK;EAC/B;EAEA,cAAA;AACE,UAAK;AAzDmC,SAAA,WAAW;AAWrD,SAAA,eAAe;AAYf,SAAA,kBAAkB;AAQN,SAAA,QAAQ;AAQ6C,SAAA,UAC/D;AAkBA,QAAI,CAAC,UAAU;AACb,WAAK,iBAAiB,SAAS,KAAK,YAAY,KAAK,IAAI,CAAC;IAC5D;EACF;EAES,MAAM,SAAsB;AACnC,QAAI,KAAK,YAAY,CAAC,KAAK,iBAAiB;AAC1C;IACF;AAEA,UAAM,MAAM,OAAO;EACrB;EAEmB,SAAM;AACvB,WAAO;8BACmB,SAAS,KAAK,oBAAmB,CAAE,CAAC;UACxD,KAAK,uBAAsB,CAAE;;;EAGrC;EAEmB,QAAQ,SAA6B;AACtD,QAAI,QAAQ,IAAI,UAAU,KAAK,QAAQ,IAAI,UAAU,MAAM,QAAW;AACpE,WAAK,cAAc,IAAI,MAAM,gBAAgB,EAAC,SAAS,KAAI,CAAC,CAAC;IAC/D;EACF;EAEU,sBAAmB;AAC3B,WAAO;MACL,YAAY,KAAK,YAAY,KAAK;MAClC,YAAY,KAAK;;EAErB;EAEU,yBAAsB;AAC9B,WAAO;QACH,KAAK,cAAa,CAAE;6CACiB,KAAK,SAAS;;cAE7C,KAAK,SAAS;oBACR,KAAK,cAAc;QAC/B,KAAK,oBAAoB,KAAK,qBAAoB,CAAE,CAAC;;EAE3D;EAEU,gBAAa;AACrB,WAAO;EACT;EAEU,oBAAiB;AACzB,WAAO,qCAAqC,KAAK,gBAAgB;EACnE;EAIQ,uBAAoB;AAC1B,WAAO;;UAED,KAAK,kBAAiB,CAAE;;;;YAItB,KAAK,QAAQ,KAAK,QAAQ,mBAAmB;;;;;EAKvD;EAEQ,iBAAiB,OAAY;AACnC,UAAM,OAAO,MAAM;AACnB,SAAK,UAAU,KAAK,iBAAiB,EAAC,SAAS,KAAI,CAAC,EAAE,SAAS;EACjE;EAEQ,YAAY,OAAY;AAI9B,QAAI,KAAK,gBAAiB,KAAK,YAAY,KAAK,iBAAkB;AAChE,YAAM,yBAAwB;AAC9B,YAAM,eAAc;AACpB;IACF;EACF;;AAvJgB,KAAA,oBAAoB;EAClC,GAAG,WAAW;EACd,gBAAgB;;AAQwB,WAAA;EAAzC,SAAS,EAAC,MAAM,SAAS,SAAS,KAAI,CAAC;;AAWxC,WAAA;EADC,SAAS,EAAC,MAAM,SAAS,WAAW,iBAAiB,SAAS,KAAI,CAAC;;AAapE,WAAA;EADC,SAAS,EAAC,MAAM,SAAS,WAAW,mBAAkB,CAAC;;AAS5C,WAAA;EAAX,SAAQ;;AAQwD,WAAA;EAAhE,SAAS,EAAC,MAAM,SAAS,SAAS,MAAM,WAAW,WAAU,CAAC;;", "names": []}