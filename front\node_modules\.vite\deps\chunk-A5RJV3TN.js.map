{"version": 3, "sources": ["../../@material/web/labs/behaviors/validators/checkbox-validator.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {Validator} from './validator.js';\n\n/**\n * Constraint validation properties for a checkbox.\n */\nexport interface CheckboxState {\n  /**\n   * Whether the checkbox is checked.\n   */\n  readonly checked: boolean;\n\n  /**\n   * Whether the checkbox is required.\n   */\n  readonly required: boolean;\n}\n\n/**\n * A validator that provides constraint validation that emulates\n * `<input type=\"checkbox\">` validation.\n */\nexport class CheckboxValidator extends Validator<CheckboxState> {\n  private checkboxControl?: HTMLInputElement;\n\n  protected override computeValidity(state: CheckboxState) {\n    if (!this.checkboxControl) {\n      // Lazily create the platform input\n      this.checkboxControl = document.createElement('input');\n      this.checkboxControl.type = 'checkbox';\n    }\n\n    this.checkboxControl.checked = state.checked;\n    this.checkboxControl.required = state.required;\n    return {\n      validity: this.checkboxControl.validity,\n      validationMessage: this.checkboxControl.validationMessage,\n    };\n  }\n\n  protected override equals(prev: CheckboxState, next: CheckboxState) {\n    return prev.checked === next.checked && prev.required === next.required;\n  }\n\n  protected override copy({checked, required}: CheckboxState) {\n    return {checked, required};\n  }\n}\n"], "mappings": ";;;;;AA2BM,IAAO,oBAAP,cAAiC,UAAwB;EAG1C,gBAAgB,OAAoB;AACrD,QAAI,CAAC,KAAK,iBAAiB;AAEzB,WAAK,kBAAkB,SAAS,cAAc,OAAO;AACrD,WAAK,gBAAgB,OAAO;IAC9B;AAEA,SAAK,gBAAgB,UAAU,MAAM;AACrC,SAAK,gBAAgB,WAAW,MAAM;AACtC,WAAO;MACL,UAAU,KAAK,gBAAgB;MAC/B,mBAAmB,KAAK,gBAAgB;;EAE5C;EAEmB,OAAO,MAAqB,MAAmB;AAChE,WAAO,KAAK,YAAY,KAAK,WAAW,KAAK,aAAa,KAAK;EACjE;EAEmB,KAAK,EAAC,SAAS,SAAQ,GAAgB;AACxD,WAAO,EAAC,SAAS,SAAQ;EAC3B;;", "names": []}