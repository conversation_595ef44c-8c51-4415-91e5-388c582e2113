{"version": 3, "sources": ["../../@material/web/menu/internal/menuitem/menu-item-styles.ts", "../../@material/web/menu/internal/controllers/menuItemController.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2024 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n// Generated stylesheet for ./menu/internal/menuitem/menu-item-styles.css.\nimport {css} from 'lit';\nexport const styles = css`:host{display:flex;--md-ripple-hover-color: var(--md-menu-item-hover-state-layer-color, var(--md-sys-color-on-surface, #1d1b20));--md-ripple-hover-opacity: var(--md-menu-item-hover-state-layer-opacity, 0.08);--md-ripple-pressed-color: var(--md-menu-item-pressed-state-layer-color, var(--md-sys-color-on-surface, #1d1b20));--md-ripple-pressed-opacity: var(--md-menu-item-pressed-state-layer-opacity, 0.12)}:host([disabled]){opacity:var(--md-menu-item-disabled-opacity, 0.3);pointer-events:none}md-focus-ring{z-index:1;--md-focus-ring-shape: 8px}a,button,li{background:none;border:none;padding:0;margin:0;text-align:unset;text-decoration:none}.list-item{border-radius:inherit;display:flex;flex:1;max-width:inherit;min-width:inherit;outline:none;-webkit-tap-highlight-color:rgba(0,0,0,0)}.list-item:not(.disabled){cursor:pointer}[slot=container]{pointer-events:none}md-ripple{border-radius:inherit}md-item{border-radius:inherit;flex:1;color:var(--md-menu-item-label-text-color, var(--md-sys-color-on-surface, #1d1b20));font-family:var(--md-menu-item-label-text-font, var(--md-sys-typescale-body-large-font, var(--md-ref-typeface-plain, Roboto)));font-size:var(--md-menu-item-label-text-size, var(--md-sys-typescale-body-large-size, 1rem));line-height:var(--md-menu-item-label-text-line-height, var(--md-sys-typescale-body-large-line-height, 1.5rem));font-weight:var(--md-menu-item-label-text-weight, var(--md-sys-typescale-body-large-weight, var(--md-ref-typeface-weight-regular, 400)));min-height:var(--md-menu-item-one-line-container-height, 56px);padding-top:var(--md-menu-item-top-space, 12px);padding-bottom:var(--md-menu-item-bottom-space, 12px);padding-inline-start:var(--md-menu-item-leading-space, 16px);padding-inline-end:var(--md-menu-item-trailing-space, 16px)}md-item[multiline]{min-height:var(--md-menu-item-two-line-container-height, 72px)}[slot=supporting-text]{color:var(--md-menu-item-supporting-text-color, var(--md-sys-color-on-surface-variant, #49454f));font-family:var(--md-menu-item-supporting-text-font, var(--md-sys-typescale-body-medium-font, var(--md-ref-typeface-plain, Roboto)));font-size:var(--md-menu-item-supporting-text-size, var(--md-sys-typescale-body-medium-size, 0.875rem));line-height:var(--md-menu-item-supporting-text-line-height, var(--md-sys-typescale-body-medium-line-height, 1.25rem));font-weight:var(--md-menu-item-supporting-text-weight, var(--md-sys-typescale-body-medium-weight, var(--md-ref-typeface-weight-regular, 400)))}[slot=trailing-supporting-text]{color:var(--md-menu-item-trailing-supporting-text-color, var(--md-sys-color-on-surface-variant, #49454f));font-family:var(--md-menu-item-trailing-supporting-text-font, var(--md-sys-typescale-label-small-font, var(--md-ref-typeface-plain, Roboto)));font-size:var(--md-menu-item-trailing-supporting-text-size, var(--md-sys-typescale-label-small-size, 0.6875rem));line-height:var(--md-menu-item-trailing-supporting-text-line-height, var(--md-sys-typescale-label-small-line-height, 1rem));font-weight:var(--md-menu-item-trailing-supporting-text-weight, var(--md-sys-typescale-label-small-weight, var(--md-ref-typeface-weight-medium, 500)))}:is([slot=start],[slot=end])::slotted(*){fill:currentColor}[slot=start]{color:var(--md-menu-item-leading-icon-color, var(--md-sys-color-on-surface-variant, #49454f))}[slot=end]{color:var(--md-menu-item-trailing-icon-color, var(--md-sys-color-on-surface-variant, #49454f))}.list-item{background-color:var(--md-menu-item-container-color, transparent)}.list-item.selected{background-color:var(--md-menu-item-selected-container-color, var(--md-sys-color-secondary-container, #e8def8))}.selected:not(.disabled) ::slotted(*){color:var(--md-menu-item-selected-label-text-color, var(--md-sys-color-on-secondary-container, #1d192b))}@media(forced-colors: active){:host([disabled]),:host([disabled]) slot{color:GrayText;opacity:1}.list-item{position:relative}.list-item.selected::before{content:\"\";position:absolute;inset:0;box-sizing:border-box;border-radius:inherit;pointer-events:none;border:3px double CanvasText}}\n`;\n", "/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {Reactive<PERSON><PERSON>roller, ReactiveControllerHost} from 'lit';\n\nimport {\n  CloseReason,\n  createDefaultCloseMenuEvent,\n  isClosableKey,\n} from './shared.js';\n\n/**\n * Interface specific to menu item and not HTMLElement.\n *\n * NOTE: required properties are expected to be reactive.\n */\ninterface MenuItemAdditions {\n  /**\n   * Whether or not the item is in the disabled state.\n   */\n  disabled: boolean;\n  /**\n   * The text of the item that will be used for typeahead. If not set, defaults\n   * to the textContent of the element slotted into the headline.\n   */\n  typeaheadText: string;\n  /**\n   * Whether or not the item is in the selected visual state.\n   */\n  selected: boolean;\n  /**\n   * Sets the behavior and role of the menu item, defaults to \"menuitem\".\n   */\n  type: MenuItemType;\n  /**\n   * Whether it should keep the menu open after click.\n   */\n  keepOpen?: boolean;\n  /**\n   * Sets the underlying `HTMLAnchorElement`'s `href` resource attribute.\n   */\n  href?: string;\n  /**\n   * Focuses the item.\n   */\n  focus: () => void;\n}\n\n/**\n * The interface of every menu item interactive with a menu. All menu items\n * should implement this interface to be compatible with md-menu. Additionally\n * it should have the `md-menu-item` attribute set.\n *\n * NOTE, the required properties are recommended to be reactive properties.\n */\nexport type MenuItem = MenuItemAdditions & HTMLElement;\n\n/**\n * Supported behaviors for a menu item.\n */\nexport type MenuItemType = 'menuitem' | 'option' | 'button' | 'link';\n\n/**\n * The options used to inialize MenuItemController.\n */\nexport interface MenuItemControllerConfig {\n  /**\n   * A function that returns the headline element of the menu item.\n   */\n  getHeadlineElements: () => HTMLElement[];\n\n  /**\n   * A function that returns the supporting-text element of the menu item.\n   */\n  getSupportingTextElements: () => HTMLElement[];\n\n  /**\n   * A function that returns the default slot / misc content.\n   */\n  getDefaultElements: () => Node[];\n\n  /**\n   * The HTML Element that accepts user interactions like click. Used for\n   * occasions like programmatically clicking anchor tags when `Enter` is\n   * pressed.\n   */\n  getInteractiveElement: () => HTMLElement | null;\n}\n\n/**\n * A controller that provides most functionality of an element that implements\n * the MenuItem interface.\n */\nexport class MenuItemController implements ReactiveController {\n  private internalTypeaheadText: string | null = null;\n  private readonly getHeadlineElements: MenuItemControllerConfig['getHeadlineElements'];\n  private readonly getSupportingTextElements: MenuItemControllerConfig['getSupportingTextElements'];\n  private readonly getDefaultElements: MenuItemControllerConfig['getDefaultElements'];\n  private readonly getInteractiveElement: MenuItemControllerConfig['getInteractiveElement'];\n\n  /**\n   * @param host The MenuItem in which to attach this controller to.\n   * @param config The object that configures this controller's behavior.\n   */\n  constructor(\n    private readonly host: ReactiveControllerHost & MenuItem,\n    config: MenuItemControllerConfig,\n  ) {\n    this.getHeadlineElements = config.getHeadlineElements;\n    this.getSupportingTextElements = config.getSupportingTextElements;\n    this.getDefaultElements = config.getDefaultElements;\n    this.getInteractiveElement = config.getInteractiveElement;\n    this.host.addController(this);\n  }\n\n  /**\n   * The text that is selectable via typeahead. If not set, defaults to the\n   * innerText of the item slotted into the `\"headline\"` slot, and if there are\n   * no slotted elements into headline, then it checks the _default_ slot, and\n   * then the `\"supporting-text\"` slot if nothing is in _default_.\n   */\n  get typeaheadText() {\n    if (this.internalTypeaheadText !== null) {\n      return this.internalTypeaheadText;\n    }\n\n    const headlineElements = this.getHeadlineElements();\n\n    const textParts: string[] = [];\n    headlineElements.forEach((headlineElement) => {\n      if (headlineElement.textContent && headlineElement.textContent.trim()) {\n        textParts.push(headlineElement.textContent.trim());\n      }\n    });\n\n    // If there are no headline elements, check the default slot's text content\n    if (textParts.length === 0) {\n      this.getDefaultElements().forEach((defaultElement) => {\n        if (defaultElement.textContent && defaultElement.textContent.trim()) {\n          textParts.push(defaultElement.textContent.trim());\n        }\n      });\n    }\n\n    // If there are no headline nor default slot elements, check the\n    //supporting-text slot's text content\n    if (textParts.length === 0) {\n      this.getSupportingTextElements().forEach((supportingTextElement) => {\n        if (\n          supportingTextElement.textContent &&\n          supportingTextElement.textContent.trim()\n        ) {\n          textParts.push(supportingTextElement.textContent.trim());\n        }\n      });\n    }\n\n    return textParts.join(' ');\n  }\n\n  /**\n   * The recommended tag name to render as the list item.\n   */\n  get tagName() {\n    const type = this.host.type;\n\n    switch (type) {\n      case 'link':\n        return 'a' as const;\n      case 'button':\n        return 'button' as const;\n      default:\n      case 'menuitem':\n      case 'option':\n        return 'li' as const;\n    }\n  }\n\n  /**\n   * The recommended role of the menu item.\n   */\n  get role() {\n    return this.host.type === 'option' ? 'option' : 'menuitem';\n  }\n\n  hostConnected() {\n    this.host.toggleAttribute('md-menu-item', true);\n  }\n\n  hostUpdate() {\n    if (this.host.href) {\n      this.host.type = 'link';\n    }\n  }\n\n  /**\n   * Bind this click listener to the interactive element. Handles closing the\n   * menu.\n   */\n  onClick = () => {\n    if (this.host.keepOpen) return;\n\n    this.host.dispatchEvent(\n      createDefaultCloseMenuEvent(this.host, {\n        kind: CloseReason.CLICK_SELECTION,\n      }),\n    );\n  };\n\n  /**\n   * Bind this click listener to the interactive element. Handles closing the\n   * menu.\n   */\n  onKeydown = (event: KeyboardEvent) => {\n    // Check if the interactive element is an anchor tag. If so, click it.\n    if (this.host.href && event.code === 'Enter') {\n      const interactiveElement = this.getInteractiveElement();\n      if (interactiveElement instanceof HTMLAnchorElement) {\n        interactiveElement.click();\n      }\n    }\n\n    if (event.defaultPrevented) return;\n\n    // If the host has keepOpen = true we should ignore clicks & Space/Enter,\n    // however we always maintain the ability to close a menu with a explicit\n    // `escape` keypress.\n    const keyCode = event.code;\n    if (this.host.keepOpen && keyCode !== 'Escape') return;\n\n    if (isClosableKey(keyCode)) {\n      event.preventDefault();\n      this.host.dispatchEvent(\n        createDefaultCloseMenuEvent(this.host, {\n          kind: CloseReason.KEYDOWN,\n          key: keyCode,\n        }),\n      );\n    }\n  };\n\n  /**\n   * Use to set the typeaheadText when it changes.\n   */\n  setTypeaheadText(text: string) {\n    this.internalTypeaheadText = text;\n  }\n}\n"], "mappings": ";;;;;;;;;;AAOO,IAAM,SAAS;;;;ACyFhB,IAAO,qBAAP,MAAyB;;;;;EAW7B,YACmB,MACjB,QAAgC;AADf,SAAA,OAAA;AAXX,SAAA,wBAAuC;AAyG/C,SAAA,UAAU,MAAK;AACb,UAAI,KAAK,KAAK;AAAU;AAExB,WAAK,KAAK,cACR,4BAA4B,KAAK,MAAM;QACrC,MAAM,YAAY;OACnB,CAAC;IAEN;AAMA,SAAA,YAAY,CAAC,UAAwB;AAEnC,UAAI,KAAK,KAAK,QAAQ,MAAM,SAAS,SAAS;AAC5C,cAAM,qBAAqB,KAAK,sBAAqB;AACrD,YAAI,8BAA8B,mBAAmB;AACnD,6BAAmB,MAAK;QAC1B;MACF;AAEA,UAAI,MAAM;AAAkB;AAK5B,YAAM,UAAU,MAAM;AACtB,UAAI,KAAK,KAAK,YAAY,YAAY;AAAU;AAEhD,UAAI,cAAc,OAAO,GAAG;AAC1B,cAAM,eAAc;AACpB,aAAK,KAAK,cACR,4BAA4B,KAAK,MAAM;UACrC,MAAM,YAAY;UAClB,KAAK;SACN,CAAC;MAEN;IACF;AAnIE,SAAK,sBAAsB,OAAO;AAClC,SAAK,4BAA4B,OAAO;AACxC,SAAK,qBAAqB,OAAO;AACjC,SAAK,wBAAwB,OAAO;AACpC,SAAK,KAAK,cAAc,IAAI;EAC9B;;;;;;;EAQA,IAAI,gBAAa;AACf,QAAI,KAAK,0BAA0B,MAAM;AACvC,aAAO,KAAK;IACd;AAEA,UAAM,mBAAmB,KAAK,oBAAmB;AAEjD,UAAM,YAAsB,CAAA;AAC5B,qBAAiB,QAAQ,CAAC,oBAAmB;AAC3C,UAAI,gBAAgB,eAAe,gBAAgB,YAAY,KAAI,GAAI;AACrE,kBAAU,KAAK,gBAAgB,YAAY,KAAI,CAAE;MACnD;IACF,CAAC;AAGD,QAAI,UAAU,WAAW,GAAG;AAC1B,WAAK,mBAAkB,EAAG,QAAQ,CAAC,mBAAkB;AACnD,YAAI,eAAe,eAAe,eAAe,YAAY,KAAI,GAAI;AACnE,oBAAU,KAAK,eAAe,YAAY,KAAI,CAAE;QAClD;MACF,CAAC;IACH;AAIA,QAAI,UAAU,WAAW,GAAG;AAC1B,WAAK,0BAAyB,EAAG,QAAQ,CAAC,0BAAyB;AACjE,YACE,sBAAsB,eACtB,sBAAsB,YAAY,KAAI,GACtC;AACA,oBAAU,KAAK,sBAAsB,YAAY,KAAI,CAAE;QACzD;MACF,CAAC;IACH;AAEA,WAAO,UAAU,KAAK,GAAG;EAC3B;;;;EAKA,IAAI,UAAO;AACT,UAAM,OAAO,KAAK,KAAK;AAEvB,YAAQ,MAAM;MACZ,KAAK;AACH,eAAO;MACT,KAAK;AACH,eAAO;MACT;MACA,KAAK;MACL,KAAK;AACH,eAAO;IACX;EACF;;;;EAKA,IAAI,OAAI;AACN,WAAO,KAAK,KAAK,SAAS,WAAW,WAAW;EAClD;EAEA,gBAAa;AACX,SAAK,KAAK,gBAAgB,gBAAgB,IAAI;EAChD;EAEA,aAAU;AACR,QAAI,KAAK,KAAK,MAAM;AAClB,WAAK,KAAK,OAAO;IACnB;EACF;;;;EAmDA,iBAAiB,MAAY;AAC3B,SAAK,wBAAwB;EAC/B;;", "names": []}