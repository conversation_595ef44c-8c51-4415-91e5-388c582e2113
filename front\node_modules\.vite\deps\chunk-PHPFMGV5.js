import {
  mixinDelegatesAria
} from "./chunk-GXE4MBY5.js";
import {
  classMap
} from "./chunk-SZQCPKZF.js";
import {
  __decorate,
  property
} from "./chunk-PZNDE6JX.js";
import {
  LitElement,
  html,
  isServer
} from "./chunk-4GZ3EDRH.js";

// node_modules/@material/web/chips/internal/chip.js
var chipBaseClass = mixinDelegatesAria(LitElement);
var Chip = class extends chipBaseClass {
  /**
   * Whether or not the primary ripple is disabled (defaults to `disabled`).
   * Some chip actions such as links cannot be disabled.
   */
  get rippleDisabled() {
    return this.disabled || this.softDisabled;
  }
  constructor() {
    super();
    this.disabled = false;
    this.softDisabled = false;
    this.alwaysFocusable = false;
    this.label = "";
    this.hasIcon = false;
    if (!isServer) {
      this.addEventListener("click", this.handleClick.bind(this));
    }
  }
  focus(options) {
    if (this.disabled && !this.alwaysFocusable) {
      return;
    }
    super.focus(options);
  }
  render() {
    return html`
      <div class="container ${classMap(this.getContainerClasses())}">
        ${this.renderContainerContent()}
      </div>
    `;
  }
  updated(changed) {
    if (changed.has("disabled") && changed.get("disabled") !== void 0) {
      this.dispatchEvent(new Event("update-focus", { bubbles: true }));
    }
  }
  getContainerClasses() {
    return {
      "disabled": this.disabled || this.softDisabled,
      "has-icon": this.hasIcon
    };
  }
  renderContainerContent() {
    return html`
      ${this.renderOutline()}
      <md-focus-ring part="focus-ring" for=${this.primaryId}></md-focus-ring>
      <md-ripple
        for=${this.primaryId}
        ?disabled=${this.rippleDisabled}></md-ripple>
      ${this.renderPrimaryAction(this.renderPrimaryContent())}
    `;
  }
  renderOutline() {
    return html`<span class="outline"></span>`;
  }
  renderLeadingIcon() {
    return html`<slot name="icon" @slotchange=${this.handleIconChange}></slot>`;
  }
  renderPrimaryContent() {
    return html`
      <span class="leading icon" aria-hidden="true">
        ${this.renderLeadingIcon()}
      </span>
      <span class="label">
        <span class="label-text" id="label">
          ${this.label ? this.label : html`<slot></slot>`}
        </span>
      </span>
      <span class="touch"></span>
    `;
  }
  handleIconChange(event) {
    const slot = event.target;
    this.hasIcon = slot.assignedElements({ flatten: true }).length > 0;
  }
  handleClick(event) {
    if (this.softDisabled || this.disabled && this.alwaysFocusable) {
      event.stopImmediatePropagation();
      event.preventDefault();
      return;
    }
  }
};
Chip.shadowRootOptions = {
  ...LitElement.shadowRootOptions,
  delegatesFocus: true
};
__decorate([
  property({ type: Boolean, reflect: true })
], Chip.prototype, "disabled", void 0);
__decorate([
  property({ type: Boolean, attribute: "soft-disabled", reflect: true })
], Chip.prototype, "softDisabled", void 0);
__decorate([
  property({ type: Boolean, attribute: "always-focusable" })
], Chip.prototype, "alwaysFocusable", void 0);
__decorate([
  property()
], Chip.prototype, "label", void 0);
__decorate([
  property({ type: Boolean, reflect: true, attribute: "has-icon" })
], Chip.prototype, "hasIcon", void 0);

export {
  Chip
};
/*! Bundled license information:

@material/web/chips/internal/chip.js:
  (**
   * @license
   * Copyright 2023 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   *)
*/
//# sourceMappingURL=chunk-PHPFMGV5.js.map
