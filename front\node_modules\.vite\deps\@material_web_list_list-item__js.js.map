{"version": 3, "sources": ["../../@material/web/list/internal/listitem/list-item.ts", "../../@material/web/list/internal/listitem/list-item-styles.ts", "../../@material/web/list/list-item.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2022 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport '../../../focus/md-focus-ring.js';\nimport '../../../labs/item/item.js';\nimport '../../../ripple/ripple.js';\n\nimport {html, LitElement, nothing, PropertyValues, TemplateResult} from 'lit';\nimport {property, query} from 'lit/decorators.js';\nimport {ClassInfo, classMap} from 'lit/directives/class-map.js';\nimport {literal, html as staticHtml, StaticValue} from 'lit/static-html.js';\n\nimport {ARIAMixinStrict} from '../../../internal/aria/aria.js';\nimport {mixinDelegatesAria} from '../../../internal/aria/delegate.js';\nimport {\n  createRequestActivationEvent,\n  ListItem,\n} from '../list-navigation-helpers.js';\n\n/**\n * Supported behaviors for a list item.\n */\nexport type ListItemType = 'text' | 'button' | 'link';\n\n// Separate variable needed for closure.\nconst listItemBaseClass = mixinDelegatesAria(LitElement);\n\n/**\n * @fires request-activation {Event} Requests the list to set `tabindex=0` on\n * the item and focus it. --bubbles --composed\n */\nexport class ListItemEl extends listItemBaseClass implements ListItem {\n  /** @nocollapse */\n  static override shadowRootOptions = {\n    ...LitElement.shadowRootOptions,\n    delegatesFocus: true,\n  };\n\n  /**\n   * Disables the item and makes it non-selectable and non-interactive.\n   */\n  @property({type: Boolean, reflect: true}) disabled = false;\n\n  /**\n   * Sets the behavior of the list item, defaults to \"text\". Change to \"link\" or\n   * \"button\" for interactive items.\n   */\n  @property({reflect: true}) type: ListItemType = 'text';\n\n  /**\n   * READONLY. Sets the `md-list-item` attribute on the element.\n   */\n  @property({type: Boolean, attribute: 'md-list-item', reflect: true})\n  isListItem = true;\n\n  /**\n   * Sets the underlying `HTMLAnchorElement`'s `href` resource attribute.\n   */\n  @property() href = '';\n\n  /**\n   * Sets the underlying `HTMLAnchorElement`'s `target` attribute when `href` is\n   * set.\n   */\n  @property() target: '_blank' | '_parent' | '_self' | '_top' | '' = '';\n\n  @query('.list-item') protected readonly listItemRoot!: HTMLElement | null;\n\n  private get isDisabled() {\n    return this.disabled && this.type !== 'link';\n  }\n\n  protected override willUpdate(changed: PropertyValues<ListItemEl>) {\n    if (this.href) {\n      this.type = 'link';\n    }\n\n    super.willUpdate(changed);\n  }\n\n  protected override render() {\n    return this.renderListItem(html`\n      <md-item>\n        <div slot=\"container\">\n          ${this.renderRipple()} ${this.renderFocusRing()}\n        </div>\n        <slot name=\"start\" slot=\"start\"></slot>\n        <slot name=\"end\" slot=\"end\"></slot>\n        ${this.renderBody()}\n      </md-item>\n    `);\n  }\n\n  /**\n   * Renders the root list item.\n   *\n   * @param content the child content of the list item.\n   */\n  protected renderListItem(content: unknown) {\n    const isAnchor = this.type === 'link';\n    let tag: StaticValue;\n    switch (this.type) {\n      case 'link':\n        tag = literal`a`;\n        break;\n      case 'button':\n        tag = literal`button`;\n        break;\n      default:\n      case 'text':\n        tag = literal`li`;\n        break;\n    }\n\n    const isInteractive = this.type !== 'text';\n    // TODO(b/265339866): announce \"button\"/\"link\" inside of a list item. Until\n    // then all are \"listitem\" roles for correct announcement.\n    const target = isAnchor && !!this.target ? this.target : nothing;\n    return staticHtml`\n      <${tag}\n        id=\"item\"\n        tabindex=\"${this.isDisabled || !isInteractive ? -1 : 0}\"\n        ?disabled=${this.isDisabled}\n        role=\"listitem\"\n        aria-selected=${(this as ARIAMixinStrict).ariaSelected || nothing}\n        aria-checked=${(this as ARIAMixinStrict).ariaChecked || nothing}\n        aria-expanded=${(this as ARIAMixinStrict).ariaExpanded || nothing}\n        aria-haspopup=${(this as ARIAMixinStrict).ariaHasPopup || nothing}\n        class=\"list-item ${classMap(this.getRenderClasses())}\"\n        href=${this.href || nothing}\n        target=${target}\n        @focus=${this.onFocus}\n      >${content}</${tag}>\n    `;\n  }\n\n  /**\n   * Handles rendering of the ripple element.\n   */\n  protected renderRipple(): TemplateResult | typeof nothing {\n    if (this.type === 'text') {\n      return nothing;\n    }\n\n    return html` <md-ripple\n      part=\"ripple\"\n      for=\"item\"\n      ?disabled=${this.isDisabled}></md-ripple>`;\n  }\n\n  /**\n   * Handles rendering of the focus ring.\n   */\n  protected renderFocusRing(): TemplateResult | typeof nothing {\n    if (this.type === 'text') {\n      return nothing;\n    }\n\n    return html` <md-focus-ring\n      @visibility-changed=${this.onFocusRingVisibilityChanged}\n      part=\"focus-ring\"\n      for=\"item\"\n      inward></md-focus-ring>`;\n  }\n\n  protected onFocusRingVisibilityChanged(e: Event) {}\n\n  /**\n   * Classes applied to the list item root.\n   */\n  protected getRenderClasses(): ClassInfo {\n    return {'disabled': this.isDisabled};\n  }\n\n  /**\n   * Handles rendering the headline and supporting text.\n   */\n  protected renderBody() {\n    return html`\n      <slot></slot>\n      <slot name=\"overline\" slot=\"overline\"></slot>\n      <slot name=\"headline\" slot=\"headline\"></slot>\n      <slot name=\"supporting-text\" slot=\"supporting-text\"></slot>\n      <slot\n        name=\"trailing-supporting-text\"\n        slot=\"trailing-supporting-text\"></slot>\n    `;\n  }\n\n  protected onFocus() {\n    if (this.tabIndex !== -1) {\n      return;\n    }\n    // Handles the case where the user clicks on the element and then tabs.\n    this.dispatchEvent(createRequestActivationEvent());\n  }\n\n  override focus() {\n    // TODO(b/300334509): needed for some cases where delegatesFocus doesn't\n    // work programmatically like in FF and select-option\n    this.listItemRoot?.focus();\n  }\n\n  override click() {\n    if (!this.listItemRoot) {\n      // If the element has not finished rendering, call super to ensure click\n      // events are dispatched.\n      super.click();\n      return;\n    }\n\n    // Forward click to the element to ensure link <a>.click() works correctly.\n    this.listItemRoot.click();\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n// Generated stylesheet for ./list/internal/listitem/list-item-styles.css.\nimport {css} from 'lit';\nexport const styles = css`:host{display:flex;-webkit-tap-highlight-color:rgba(0,0,0,0);--md-ripple-hover-color: var(--md-list-item-hover-state-layer-color, var(--md-sys-color-on-surface, #1d1b20));--md-ripple-hover-opacity: var(--md-list-item-hover-state-layer-opacity, 0.08);--md-ripple-pressed-color: var(--md-list-item-pressed-state-layer-color, var(--md-sys-color-on-surface, #1d1b20));--md-ripple-pressed-opacity: var(--md-list-item-pressed-state-layer-opacity, 0.12)}:host(:is([type=button]:not([disabled]),[type=link])){cursor:pointer}md-focus-ring{z-index:1;--md-focus-ring-shape: 8px}a,button,li{background:none;border:none;cursor:inherit;padding:0;margin:0;text-align:unset;text-decoration:none}.list-item{border-radius:inherit;display:flex;flex:1;max-width:inherit;min-width:inherit;outline:none;-webkit-tap-highlight-color:rgba(0,0,0,0);width:100%}.list-item.interactive{cursor:pointer}.list-item.disabled{opacity:var(--md-list-item-disabled-opacity, 0.3);pointer-events:none}[slot=container]{pointer-events:none}md-ripple{border-radius:inherit}md-item{border-radius:inherit;flex:1;height:100%;color:var(--md-list-item-label-text-color, var(--md-sys-color-on-surface, #1d1b20));font-family:var(--md-list-item-label-text-font, var(--md-sys-typescale-body-large-font, var(--md-ref-typeface-plain, Roboto)));font-size:var(--md-list-item-label-text-size, var(--md-sys-typescale-body-large-size, 1rem));line-height:var(--md-list-item-label-text-line-height, var(--md-sys-typescale-body-large-line-height, 1.5rem));font-weight:var(--md-list-item-label-text-weight, var(--md-sys-typescale-body-large-weight, var(--md-ref-typeface-weight-regular, 400)));min-height:var(--md-list-item-one-line-container-height, 56px);padding-top:var(--md-list-item-top-space, 12px);padding-bottom:var(--md-list-item-bottom-space, 12px);padding-inline-start:var(--md-list-item-leading-space, 16px);padding-inline-end:var(--md-list-item-trailing-space, 16px)}md-item[multiline]{min-height:var(--md-list-item-two-line-container-height, 72px)}[slot=supporting-text]{color:var(--md-list-item-supporting-text-color, var(--md-sys-color-on-surface-variant, #49454f));font-family:var(--md-list-item-supporting-text-font, var(--md-sys-typescale-body-medium-font, var(--md-ref-typeface-plain, Roboto)));font-size:var(--md-list-item-supporting-text-size, var(--md-sys-typescale-body-medium-size, 0.875rem));line-height:var(--md-list-item-supporting-text-line-height, var(--md-sys-typescale-body-medium-line-height, 1.25rem));font-weight:var(--md-list-item-supporting-text-weight, var(--md-sys-typescale-body-medium-weight, var(--md-ref-typeface-weight-regular, 400)))}[slot=trailing-supporting-text]{color:var(--md-list-item-trailing-supporting-text-color, var(--md-sys-color-on-surface-variant, #49454f));font-family:var(--md-list-item-trailing-supporting-text-font, var(--md-sys-typescale-label-small-font, var(--md-ref-typeface-plain, Roboto)));font-size:var(--md-list-item-trailing-supporting-text-size, var(--md-sys-typescale-label-small-size, 0.6875rem));line-height:var(--md-list-item-trailing-supporting-text-line-height, var(--md-sys-typescale-label-small-line-height, 1rem));font-weight:var(--md-list-item-trailing-supporting-text-weight, var(--md-sys-typescale-label-small-weight, var(--md-ref-typeface-weight-medium, 500)))}:is([slot=start],[slot=end])::slotted(*){fill:currentColor}[slot=start]{color:var(--md-list-item-leading-icon-color, var(--md-sys-color-on-surface-variant, #49454f))}[slot=end]{color:var(--md-list-item-trailing-icon-color, var(--md-sys-color-on-surface-variant, #49454f))}@media(forced-colors: active){.disabled slot{color:GrayText}.list-item.disabled{color:GrayText;opacity:1}}\n`;\n", "/**\n * @license\n * Copyright 2022 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {ListItemEl as ListItem} from './internal/listitem/list-item.js';\nimport {styles} from './internal/listitem/list-item-styles.js';\n\nexport {type ListItemType} from './internal/listitem/list-item.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-list-item': MdListItem;\n  }\n}\n\n/**\n * @summary\n * Lists are continuous, vertical indexes of text or images. Items are placed\n * inside the list.\n *\n * @description\n * Lists consist of one or more list items, and can contain actions represented\n * by icons and text. List items come in three sizes: one-line, two-line, and\n * three-line.\n *\n * __Takeaways:__\n *\n * - Lists should be sorted in logical ways that make content easy to scan, such\n *   as alphabetical, numerical, chronological, or by user preference.\n * - Lists present content in a way that makes it easy to identify a specific\n *   item in a collection and act on it.\n * - Lists should present icons, text, and actions in a consistent format.\n *\n * Acceptable slot child variants are:\n *\n * - `img[slot=end]`\n * - `img[slot=start]`\n *\n *  @example\n * ```html\n * <md-list-item\n *     headline=\"User Name\"\n *     supportingText=\"<EMAIL>\">\n *   <md-icon slot=\"start\">account_circle</md-icon>\n *   <md-icon slot=\"end\">check</md-icon>\n * </md-list-item>\n * ```\n *\n * @example\n *\n * @final\n * @suppress {visibility}\n */\n@customElement('md-list-item')\nexport class MdListItem extends ListItem {\n  static override styles: CSSResultOrNative[] = [styles];\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BA,IAAM,oBAAoB,mBAAmB,UAAU;AAMjD,IAAO,aAAP,cAA0B,kBAAiB;EAAjD,cAAA;;AAU4C,SAAA,WAAW;AAM1B,SAAA,OAAqB;AAMhD,SAAA,aAAa;AAKD,SAAA,OAAO;AAMP,SAAA,SAAuD;EAsJrE;EAlJE,IAAY,aAAU;AACpB,WAAO,KAAK,YAAY,KAAK,SAAS;EACxC;EAEmB,WAAW,SAAmC;AAC/D,QAAI,KAAK,MAAM;AACb,WAAK,OAAO;IACd;AAEA,UAAM,WAAW,OAAO;EAC1B;EAEmB,SAAM;AACvB,WAAO,KAAK,eAAe;;;YAGnB,KAAK,aAAY,CAAE,IAAI,KAAK,gBAAe,CAAE;;;;UAI/C,KAAK,WAAU,CAAE;;KAEtB;EACH;;;;;;EAOU,eAAe,SAAgB;AACvC,UAAM,WAAW,KAAK,SAAS;AAC/B,QAAI;AACJ,YAAQ,KAAK,MAAM;MACjB,KAAK;AACH,cAAM;AACN;MACF,KAAK;AACH,cAAM;AACN;MACF;MACA,KAAK;AACH,cAAM;AACN;IACJ;AAEA,UAAM,gBAAgB,KAAK,SAAS;AAGpC,UAAM,SAAS,YAAY,CAAC,CAAC,KAAK,SAAS,KAAK,SAAS;AACzD,WAAOA;SACF,GAAG;;oBAEQ,KAAK,cAAc,CAAC,gBAAgB,KAAK,CAAC;oBAC1C,KAAK,UAAU;;wBAEV,KAAyB,gBAAgB,OAAO;uBACjD,KAAyB,eAAe,OAAO;wBAC9C,KAAyB,gBAAgB,OAAO;wBAChD,KAAyB,gBAAgB,OAAO;2BAC9C,SAAS,KAAK,iBAAgB,CAAE,CAAC;eAC7C,KAAK,QAAQ,OAAO;iBAClB,MAAM;iBACN,KAAK,OAAO;SACpB,OAAO,KAAK,GAAG;;EAEtB;;;;EAKU,eAAY;AACpB,QAAI,KAAK,SAAS,QAAQ;AACxB,aAAO;IACT;AAEA,WAAO;;;kBAGO,KAAK,UAAU;EAC/B;;;;EAKU,kBAAe;AACvB,QAAI,KAAK,SAAS,QAAQ;AACxB,aAAO;IACT;AAEA,WAAO;4BACiB,KAAK,4BAA4B;;;;EAI3D;EAEU,6BAA6B,GAAQ;EAAG;;;;EAKxC,mBAAgB;AACxB,WAAO,EAAC,YAAY,KAAK,WAAU;EACrC;;;;EAKU,aAAU;AAClB,WAAO;;;;;;;;;EAST;EAEU,UAAO;AACf,QAAI,KAAK,aAAa,IAAI;AACxB;IACF;AAEA,SAAK,cAAc,6BAA4B,CAAE;EACnD;EAES,QAAK;AAGZ,SAAK,cAAc,MAAK;EAC1B;EAES,QAAK;AACZ,QAAI,CAAC,KAAK,cAAc;AAGtB,YAAM,MAAK;AACX;IACF;AAGA,SAAK,aAAa,MAAK;EACzB;;AApLgB,WAAA,oBAAoB;EAClC,GAAG,WAAW;EACd,gBAAgB;;AAMwB,WAAA;EAAzC,SAAS,EAAC,MAAM,SAAS,SAAS,KAAI,CAAC;;AAMb,WAAA;EAA1B,SAAS,EAAC,SAAS,KAAI,CAAC;;AAMzB,WAAA;EADC,SAAS,EAAC,MAAM,SAAS,WAAW,gBAAgB,SAAS,KAAI,CAAC;;AAMvD,WAAA;EAAX,SAAQ;;AAMG,WAAA;EAAX,SAAQ;;AAE+B,WAAA;EAAvC,MAAM,YAAY;;;;AC9Dd,IAAM,SAAS;;;;ACoDf,IAAM,aAAN,MAAMC,oBAAmB,WAAQ;;AACtB,WAAA,SAA8B,CAAC,MAAM;AAD1C,aAAU,WAAA;EADtB,cAAc,cAAc;GAChB,UAAU;", "names": ["html", "MdListItem"]}