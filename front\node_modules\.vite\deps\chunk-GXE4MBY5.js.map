{"version": 3, "sources": ["../../@material/web/internal/aria/aria.ts", "../../@material/web/internal/aria/delegate.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\n/**\n * Accessibility Object Model reflective aria property name types.\n */\nexport type ARIAProperty = keyof ARIAMixin;\n\n/**\n * Accessibility Object Model reflective aria properties.\n */\nexport const ARIA_PROPERTIES: ARIAProperty[] = [\n  'role',\n  'ariaAtomic',\n  'ariaAutoComplete',\n  'ariaBusy',\n  'ariaChecked',\n  'ariaColCount',\n  'ariaColIndex',\n  'ariaColSpan',\n  'ariaCurrent',\n  'ariaDisabled',\n  'ariaExpanded',\n  'ariaHasPopup',\n  'ariaHidden',\n  'ariaInvalid',\n  'ariaKeyShortcuts',\n  'ariaLabel',\n  'ariaLevel',\n  'ariaLive',\n  'ariaModal',\n  'ariaMultiLine',\n  'ariaMultiSelectable',\n  'ariaOrientation',\n  'ariaPlaceholder',\n  'ariaPosInSet',\n  'ariaPressed',\n  'ariaReadOnly',\n  'ariaRequired',\n  'ariaRoleDescription',\n  'ariaRowCount',\n  'ariaRowIndex',\n  'ariaRowSpan',\n  'ariaSelected',\n  'ariaSetSize',\n  'ariaSort',\n  'ariaValueMax',\n  'ariaValueMin',\n  'ariaValueNow',\n  'ariaValueText',\n];\n\n/**\n * Accessibility Object Model aria attribute name types.\n */\nexport type ARIAAttribute = ARIAPropertyToAttribute<ARIAProperty>;\n\n/**\n * Accessibility Object Model aria attributes.\n */\nexport const ARIA_ATTRIBUTES = ARIA_PROPERTIES.map(ariaPropertyToAttribute);\n\n/**\n * Checks if an attribute is one of the AOM aria attributes.\n *\n * @example\n * isAriaAttribute('aria-label'); // true\n *\n * @param attribute The attribute to check.\n * @return True if the attribute is an aria attribute, or false if not.\n */\nexport function isAriaAttribute(attribute: string): attribute is ARIAAttribute {\n  return ARIA_ATTRIBUTES.includes(attribute as ARIAAttribute);\n}\n\n/**\n * Converts an AOM aria property into its corresponding attribute.\n *\n * @example\n * ariaPropertyToAttribute('ariaLabel'); // 'aria-label'\n *\n * @param property The aria property.\n * @return The aria attribute.\n */\nexport function ariaPropertyToAttribute<K extends ARIAProperty>(property: K) {\n  return (\n    property\n      .replace('aria', 'aria-')\n      // IDREF attributes also include an \"Element\" or \"Elements\" suffix\n      .replace(/Elements?/g, '')\n      .toLowerCase() as ARIAPropertyToAttribute<K>\n  );\n}\n\n// Converts an `ariaFoo` string type to an `aria-foo` string type.\ntype ARIAPropertyToAttribute<K extends string> =\n  K extends `aria${infer Suffix}Element${infer OptS}`\n    ? `aria-${Lowercase<Suffix>}`\n    : K extends `aria${infer Suffix}`\n      ? `aria-${Lowercase<Suffix>}`\n      : K;\n\n/**\n * An extension of `ARIAMixin` that enforces strict value types for aria\n * properties.\n *\n * This is needed for correct typing in render functions with lit analyzer.\n *\n * @example\n * render() {\n *   const {ariaLabel} = this as ARIAMixinStrict;\n *   return html`\n *     <button aria-label=${ariaLabel || nothing}>\n *       <slot></slot>\n *     </button>\n *   `;\n * }\n */\nexport interface ARIAMixinStrict extends ARIAMixin {\n  ariaAtomic: 'true' | 'false' | null;\n  ariaAutoComplete: 'none' | 'inline' | 'list' | 'both' | null;\n  ariaBusy: 'true' | 'false' | null;\n  ariaChecked: 'true' | 'false' | null;\n  ariaColCount: `${number}` | null;\n  ariaColIndex: `${number}` | null;\n  ariaColSpan: `${number}` | null;\n  ariaCurrent:\n    | 'page'\n    | 'step'\n    | 'location'\n    | 'date'\n    | 'time'\n    | 'true'\n    | 'false'\n    | null;\n  ariaDisabled: 'true' | 'false' | null;\n  ariaExpanded: 'true' | 'false' | null;\n  ariaHasPopup:\n    | 'false'\n    | 'true'\n    | 'menu'\n    | 'listbox'\n    | 'tree'\n    | 'grid'\n    | 'dialog'\n    | null;\n  ariaHidden: 'true' | 'false' | null;\n  ariaInvalid: 'true' | 'false' | null;\n  ariaKeyShortcuts: string | null;\n  ariaLabel: string | null;\n  ariaLevel: `${number}` | null;\n  ariaLive: 'assertive' | 'off' | 'polite' | null;\n  ariaModal: 'true' | 'false' | null;\n  ariaMultiLine: 'true' | 'false' | null;\n  ariaMultiSelectable: 'true' | 'false' | null;\n  ariaOrientation: 'horizontal' | 'vertical' | 'undefined' | null;\n  ariaPlaceholder: string | null;\n  ariaPosInSet: `${number}` | null;\n  ariaPressed: 'true' | 'false' | null;\n  ariaReadOnly: 'true' | 'false' | null;\n  ariaRequired: 'true' | 'false' | null;\n  ariaRoleDescription: string | null;\n  ariaRowCount: `${number}` | null;\n  ariaRowIndex: `${number}` | null;\n  ariaRowSpan: `${number}` | null;\n  ariaSelected: 'true' | 'false' | null;\n  ariaSetSize: `${number}` | null;\n  ariaSort: 'ascending' | 'descending' | 'none' | 'other' | null;\n  ariaValueMax: `${number}` | null;\n  ariaValueMin: `${number}` | null;\n  ariaValueNow: `${number}` | null;\n  ariaValueText: string | null;\n  role: ARIARole | null;\n}\n\n/**\n * Valid values for `role`.\n */\nexport type ARIARole =\n  | 'alert'\n  | 'alertdialog'\n  | 'button'\n  | 'checkbox'\n  | 'dialog'\n  | 'gridcell'\n  | 'link'\n  | 'log'\n  | 'marquee'\n  | 'menuitem'\n  | 'menuitemcheckbox'\n  | 'menuitemradio'\n  | 'option'\n  | 'progressbar'\n  | 'radio'\n  | 'scrollbar'\n  | 'searchbox'\n  | 'slider'\n  | 'spinbutton'\n  | 'status'\n  | 'switch'\n  | 'tab'\n  | 'tabpanel'\n  | 'textbox'\n  | 'timer'\n  | 'tooltip'\n  | 'treeitem'\n  | 'combobox'\n  | 'grid'\n  | 'listbox'\n  | 'menu'\n  | 'menubar'\n  | 'radiogroup'\n  | 'tablist'\n  | 'tree'\n  | 'treegrid'\n  | 'application'\n  | 'article'\n  | 'cell'\n  | 'columnheader'\n  | 'definition'\n  | 'directory'\n  | 'document'\n  | 'feed'\n  | 'figure'\n  | 'group'\n  | 'heading'\n  | 'img'\n  | 'list'\n  | 'listitem'\n  | 'math'\n  | 'none'\n  | 'note'\n  | 'presentation'\n  | 'region'\n  | 'row'\n  | 'rowgroup'\n  | 'rowheader'\n  | 'separator'\n  | 'table'\n  | 'term'\n  | 'text'\n  | 'toolbar'\n  | 'banner'\n  | 'complementary'\n  | 'contentinfo'\n  | 'form'\n  | 'main'\n  | 'navigation'\n  | 'region'\n  | 'search'\n  | 'doc-abstract'\n  | 'doc-acknowledgments'\n  | 'doc-afterword'\n  | 'doc-appendix'\n  | 'doc-backlink'\n  | 'doc-biblioentry'\n  | 'doc-bibliography'\n  | 'doc-biblioref'\n  | 'doc-chapter'\n  | 'doc-colophon'\n  | 'doc-conclusion'\n  | 'doc-cover'\n  | 'doc-credit'\n  | 'doc-credits'\n  | 'doc-dedication'\n  | 'doc-endnote'\n  | 'doc-endnotes'\n  | 'doc-epigraph'\n  | 'doc-epilogue'\n  | 'doc-errata'\n  | 'doc-example'\n  | 'doc-footnote'\n  | 'doc-foreword'\n  | 'doc-glossary'\n  | 'doc-glossref'\n  | 'doc-index'\n  | 'doc-introduction'\n  | 'doc-noteref'\n  | 'doc-notice'\n  | 'doc-pagebreak'\n  | 'doc-pagelist'\n  | 'doc-part'\n  | 'doc-preface'\n  | 'doc-prologue'\n  | 'doc-pullquote'\n  | 'doc-qna'\n  | 'doc-subtitle'\n  | 'doc-tip'\n  | 'doc-toc';\n", "/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {LitElement, ReactiveElement, isServer} from 'lit';\n\nimport {MixinBase, MixinReturn} from '../../labs/behaviors/mixin.js';\nimport {\n  ARIA_PROPERTIES,\n  ariaPropertyToAttribute,\n  isAriaAttribute,\n} from './aria.js';\n\n// Private symbols\nconst privateIgnoreAttributeChangesFor = Symbol(\n  'privateIgnoreAttributeChangesFor',\n);\n\n/**\n * Mixes in aria delegation for elements that delegate focus and aria to inner\n * shadow root elements.\n *\n * This mixin fixes invalid aria announcements with shadow roots, caused by\n * duplicate aria attributes on both the host and the inner shadow root element.\n *\n * Note: this mixin **does not yet support** ID reference attributes, such as\n * `aria-labelledby` or `aria-controls`.\n *\n * @example\n * ```ts\n * class MyButton extends mixinDelegatesAria(LitElement) {\n *   static shadowRootOptions = {mode: 'open', delegatesFocus: true};\n *\n *   render() {\n *     return html`\n *       <button aria-label=${this.ariaLabel || nothing}>\n *         <slot></slot>\n *       </button>\n *     `;\n *   }\n * }\n * ```\n * ```html\n * <my-button aria-label=\"Plus one\">+1</my-button>\n * ```\n *\n * Use `ARIAMixinStrict` for lit analyzer strict types, such as the \"role\"\n * attribute.\n *\n * @example\n * ```ts\n * return html`\n *   <button role=${(this as ARIAMixinStrict).role || nothing}>\n *     <slot></slot>\n *   </button>\n * `;\n * ```\n *\n * In the future, updates to the Accessibility Object Model (AOM) will provide\n * built-in aria delegation features that will replace this mixin.\n *\n * @param base The class to mix functionality into.\n * @return The provided class with aria delegation mixed in.\n */\nexport function mixinDelegatesAria<T extends MixinBase<LitElement>>(\n  base: T,\n): MixinReturn<T> {\n  if (isServer) {\n    // Don't shift attributes when running with lit-ssr. The SSR renderer\n    // implements a subset of DOM APIs, including the methods this mixin\n    // overrides, causing errors. We don't need to shift on the server anyway\n    // since elements will shift attributes immediately once they hydrate.\n    return base;\n  }\n\n  abstract class WithDelegatesAriaElement extends base {\n    [privateIgnoreAttributeChangesFor] = new Set();\n\n    override attributeChangedCallback(\n      name: string,\n      oldValue: string | null,\n      newValue: string | null,\n    ) {\n      if (!isAriaAttribute(name)) {\n        super.attributeChangedCallback(name, oldValue, newValue);\n        return;\n      }\n\n      if (this[privateIgnoreAttributeChangesFor].has(name)) {\n        return;\n      }\n\n      // Don't trigger another `attributeChangedCallback` once we remove the\n      // aria attribute from the host. We check the explicit name of the\n      // attribute to ignore since `attributeChangedCallback` can be called\n      // multiple times out of an expected order when hydrating an element with\n      // multiple attributes.\n      this[privateIgnoreAttributeChangesFor].add(name);\n      this.removeAttribute(name);\n      this[privateIgnoreAttributeChangesFor].delete(name);\n      const dataProperty = ariaAttributeToDataProperty(name);\n      if (newValue === null) {\n        delete this.dataset[dataProperty];\n      } else {\n        this.dataset[dataProperty] = newValue;\n      }\n\n      this.requestUpdate(ariaAttributeToDataProperty(name), oldValue);\n    }\n\n    override getAttribute(name: string) {\n      if (isAriaAttribute(name)) {\n        return super.getAttribute(ariaAttributeToDataAttribute(name));\n      }\n\n      return super.getAttribute(name);\n    }\n\n    override removeAttribute(name: string) {\n      super.removeAttribute(name);\n      if (isAriaAttribute(name)) {\n        super.removeAttribute(ariaAttributeToDataAttribute(name));\n        // Since `aria-*` attributes are already removed`, we need to request\n        // an update because `attributeChangedCallback` will not be called.\n        this.requestUpdate();\n      }\n    }\n  }\n\n  setupDelegatesAriaProperties(\n    WithDelegatesAriaElement as unknown as typeof ReactiveElement,\n  );\n\n  return WithDelegatesAriaElement;\n}\n\n/**\n * Overrides the constructor's native `ARIAMixin` properties to ensure that\n * aria properties reflect the values that were shifted to a data attribute.\n *\n * @param ctor The `ReactiveElement` constructor to patch.\n */\nfunction setupDelegatesAriaProperties(ctor: typeof ReactiveElement) {\n  for (const ariaProperty of ARIA_PROPERTIES) {\n    // The casing between ariaProperty and the dataProperty may be different.\n    // ex: aria-haspopup -> ariaHasPopup\n    const ariaAttribute = ariaPropertyToAttribute(ariaProperty);\n    // ex: aria-haspopup -> data-aria-haspopup\n    const dataAttribute = ariaAttributeToDataAttribute(ariaAttribute);\n    // ex: aria-haspopup -> dataset.ariaHaspopup\n    const dataProperty = ariaAttributeToDataProperty(ariaAttribute);\n\n    // Call `ReactiveElement.createProperty()` so that the `aria-*` and `data-*`\n    // attributes are added to the `static observedAttributes` array. This\n    // triggers `attributeChangedCallback` for the delegates aria mixin to\n    // handle.\n    ctor.createProperty(ariaProperty, {\n      attribute: ariaAttribute,\n      noAccessor: true,\n    });\n    ctor.createProperty(Symbol(dataAttribute), {\n      attribute: dataAttribute,\n      noAccessor: true,\n    });\n\n    // Re-define the `ARIAMixin` properties to handle data attribute shifting.\n    // It is safe to use `Object.defineProperty` here because the properties\n    // are native and not renamed.\n    // tslint:disable-next-line:ban-unsafe-reflection\n    Object.defineProperty(ctor.prototype, ariaProperty, {\n      configurable: true,\n      enumerable: true,\n      get(this: ReactiveElement): string | null {\n        return this.dataset[dataProperty] ?? null;\n      },\n      set(this: ReactiveElement, value: string | null): void {\n        const prevValue = this.dataset[dataProperty] ?? null;\n        if (value === prevValue) {\n          return;\n        }\n\n        if (value === null) {\n          delete this.dataset[dataProperty];\n        } else {\n          this.dataset[dataProperty] = value;\n        }\n\n        this.requestUpdate(ariaProperty, prevValue);\n      },\n    });\n  }\n}\n\nfunction ariaAttributeToDataAttribute(ariaAttribute: string) {\n  // aria-haspopup -> data-aria-haspopup\n  return `data-${ariaAttribute}`;\n}\n\nfunction ariaAttributeToDataProperty(ariaAttribute: string) {\n  // aria-haspopup -> dataset.ariaHaspopup\n  return ariaAttribute.replace(/-\\w/, (dashLetter) =>\n    dashLetter[1].toUpperCase(),\n  );\n}\n"], "mappings": ";;;;;AAcO,IAAM,kBAAkC;EAC7C;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAWK,IAAM,kBAAkB,gBAAgB,IAAI,uBAAuB;AAWpE,SAAU,gBAAgB,WAAiB;AAC/C,SAAO,gBAAgB,SAAS,SAA0B;AAC5D;AAWM,SAAU,wBAAgD,UAAW;AACzE,SACE,SACG,QAAQ,QAAQ,OAAO,EAEvB,QAAQ,cAAc,EAAE,EACxB,YAAW;AAElB;;;AC/EA,IAAM,mCAAmC,OACvC,kCAAkC;AAiD9B,SAAU,mBACd,MAAO;;AAEP,MAAI,UAAU;AAKZ,WAAO;EACT;EAEA,MAAe,iCAAiC,KAAI;IAApD,cAAA;;AACE,WAAA,EAAA,IAAqC,oBAAI,IAAG;IAmD9C;IAjDW,yBACP,MACA,UACA,UAAuB;AAEvB,UAAI,CAAC,gBAAgB,IAAI,GAAG;AAC1B,cAAM,yBAAyB,MAAM,UAAU,QAAQ;AACvD;MACF;AAEA,UAAI,KAAK,gCAAgC,EAAE,IAAI,IAAI,GAAG;AACpD;MACF;AAOA,WAAK,gCAAgC,EAAE,IAAI,IAAI;AAC/C,WAAK,gBAAgB,IAAI;AACzB,WAAK,gCAAgC,EAAE,OAAO,IAAI;AAClD,YAAM,eAAe,4BAA4B,IAAI;AACrD,UAAI,aAAa,MAAM;AACrB,eAAO,KAAK,QAAQ,YAAY;MAClC,OAAO;AACL,aAAK,QAAQ,YAAY,IAAI;MAC/B;AAEA,WAAK,cAAc,4BAA4B,IAAI,GAAG,QAAQ;IAChE;IAES,aAAa,MAAY;AAChC,UAAI,gBAAgB,IAAI,GAAG;AACzB,eAAO,MAAM,aAAa,6BAA6B,IAAI,CAAC;MAC9D;AAEA,aAAO,MAAM,aAAa,IAAI;IAChC;IAES,gBAAgB,MAAY;AACnC,YAAM,gBAAgB,IAAI;AAC1B,UAAI,gBAAgB,IAAI,GAAG;AACzB,cAAM,gBAAgB,6BAA6B,IAAI,CAAC;AAGxD,aAAK,cAAa;MACpB;IACF;;OAlDC;AAqDH,+BACE,wBAA6D;AAG/D,SAAO;AACT;AAQA,SAAS,6BAA6B,MAA4B;AAChE,aAAW,gBAAgB,iBAAiB;AAG1C,UAAM,gBAAgB,wBAAwB,YAAY;AAE1D,UAAM,gBAAgB,6BAA6B,aAAa;AAEhE,UAAM,eAAe,4BAA4B,aAAa;AAM9D,SAAK,eAAe,cAAc;MAChC,WAAW;MACX,YAAY;KACb;AACD,SAAK,eAAe,OAAO,aAAa,GAAG;MACzC,WAAW;MACX,YAAY;KACb;AAMD,WAAO,eAAe,KAAK,WAAW,cAAc;MAClD,cAAc;MACd,YAAY;MACZ,MAAG;AACD,eAAO,KAAK,QAAQ,YAAY,KAAK;MACvC;MACA,IAA2B,OAAoB;AAC7C,cAAM,YAAY,KAAK,QAAQ,YAAY,KAAK;AAChD,YAAI,UAAU,WAAW;AACvB;QACF;AAEA,YAAI,UAAU,MAAM;AAClB,iBAAO,KAAK,QAAQ,YAAY;QAClC,OAAO;AACL,eAAK,QAAQ,YAAY,IAAI;QAC/B;AAEA,aAAK,cAAc,cAAc,SAAS;MAC5C;KACD;EACH;AACF;AAEA,SAAS,6BAA6B,eAAqB;AAEzD,SAAO,QAAQ,aAAa;AAC9B;AAEA,SAAS,4BAA4B,eAAqB;AAExD,SAAO,cAAc,QAAQ,OAAO,CAAC,eACnC,WAAW,CAAC,EAAE,YAAW,CAAE;AAE/B;", "names": []}