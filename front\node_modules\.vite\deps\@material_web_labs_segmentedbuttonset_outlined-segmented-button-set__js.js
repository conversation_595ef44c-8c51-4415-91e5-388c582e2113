import {
  mixinDelegatesAria
} from "./chunk-GXE4MBY5.js";
import {
  customElement,
  property,
  queryAssignedElements
} from "./chunk-T3WMJB5E.js";
import {
  LitElement,
  css,
  html,
  nothing
} from "./chunk-4GZ3EDRH.js";
import {
  __decorate
} from "./chunk-HMZZ7KLC.js";
import "./chunk-G3PMV62Z.js";

// node_modules/@material/web/labs/segmentedbuttonset/internal/segmented-button-set.js
var segmentedButtonSetBaseClass = mixinDelegatesAria(LitElement);
var SegmentedButtonSet = class extends segmentedButtonSetBaseClass {
  constructor() {
    super(...arguments);
    this.multiselect = false;
  }
  getButtonDisabled(index) {
    if (this.indexOutOfBounds(index))
      return false;
    return this.buttons[index].disabled;
  }
  setButtonDisabled(index, disabled) {
    if (this.indexOutOfBounds(index))
      return;
    this.buttons[index].disabled = disabled;
  }
  getButtonSelected(index) {
    if (this.indexOutOfBounds(index))
      return false;
    return this.buttons[index].selected;
  }
  setButtonSelected(index, selected) {
    if (this.indexOutOfBounds(index))
      return;
    if (this.getButtonDisabled(index))
      return;
    if (this.multiselect) {
      this.buttons[index].selected = selected;
      this.emitSelectionEvent(index);
      return;
    }
    if (!selected)
      return;
    this.buttons[index].selected = true;
    this.emitSelectionEvent(index);
    for (let i = 0; i < this.buttons.length; i++) {
      if (i === index)
        continue;
      this.buttons[i].selected = false;
    }
  }
  handleSegmentedButtonInteraction(event) {
    const index = this.buttons.indexOf(event.target);
    this.toggleSelection(index);
  }
  toggleSelection(index) {
    if (this.indexOutOfBounds(index))
      return;
    this.setButtonSelected(index, !this.buttons[index].selected);
  }
  indexOutOfBounds(index) {
    return index < 0 || index >= this.buttons.length;
  }
  emitSelectionEvent(index) {
    this.dispatchEvent(new CustomEvent("segmented-button-set-selection", {
      detail: {
        button: this.buttons[index],
        selected: this.buttons[index].selected,
        index
      },
      bubbles: true,
      composed: true
    }));
  }
  render() {
    const { ariaLabel } = this;
    return html`
      <span
        role="group"
        @segmented-button-interaction="${this.handleSegmentedButtonInteraction}"
        aria-label=${ariaLabel || nothing}
        class="md3-segmented-button-set">
        <slot></slot>
      </span>
    `;
  }
  getRenderClasses() {
    return {};
  }
};
__decorate([
  property({ type: Boolean })
], SegmentedButtonSet.prototype, "multiselect", void 0);
__decorate([
  queryAssignedElements({ flatten: true })
], SegmentedButtonSet.prototype, "buttons", void 0);

// node_modules/@material/web/labs/segmentedbuttonset/internal/outlined-segmented-button-set.js
var OutlinedSegmentedButtonSet = class extends SegmentedButtonSet {
  getRenderClasses() {
    return {
      ...super.getRenderClasses(),
      "md3-segmented-button-set--outlined": true
    };
  }
};

// node_modules/@material/web/labs/segmentedbuttonset/internal/outlined-styles.js
var styles = css`:host{--_container-height: var(--md-outlined-segmented-button-container-height, 40px);--_disabled-icon-color: var(--md-outlined-segmented-button-disabled-icon-color, var(--md-sys-color-on-surface, #1d1b20));--_disabled-label-text-color: var(--md-outlined-segmented-button-disabled-label-text-color, var(--md-sys-color-on-surface, #1d1b20));--_disabled-outline-color: var(--md-outlined-segmented-button-disabled-outline-color, var(--md-sys-color-on-surface, #1d1b20));--_hover-state-layer-opacity: var(--md-outlined-segmented-button-hover-state-layer-opacity, 0.08);--_label-text-font: var(--md-outlined-segmented-button-label-text-font, var(--md-sys-typescale-label-large-font, var(--md-ref-typeface-plain, Roboto)));--_label-text-line-height: var(--md-outlined-segmented-button-label-text-line-height, var(--md-sys-typescale-label-large-line-height, 1.25rem));--_label-text-size: var(--md-outlined-segmented-button-label-text-size, var(--md-sys-typescale-label-large-size, 0.875rem));--_label-text-weight: var(--md-outlined-segmented-button-label-text-weight, var(--md-sys-typescale-label-large-weight, var(--md-ref-typeface-weight-medium, 500)));--_outline-color: var(--md-outlined-segmented-button-outline-color, var(--md-sys-color-outline, #79747e));--_pressed-state-layer-opacity: var(--md-outlined-segmented-button-pressed-state-layer-opacity, 0.12);--_selected-container-color: var(--md-outlined-segmented-button-selected-container-color, var(--md-sys-color-secondary-container, #e8def8));--_selected-focus-icon-color: var(--md-outlined-segmented-button-selected-focus-icon-color, var(--md-sys-color-on-secondary-container, #1d192b));--_selected-focus-label-text-color: var(--md-outlined-segmented-button-selected-focus-label-text-color, var(--md-sys-color-on-secondary-container, #1d192b));--_selected-hover-icon-color: var(--md-outlined-segmented-button-selected-hover-icon-color, var(--md-sys-color-on-secondary-container, #1d192b));--_selected-hover-label-text-color: var(--md-outlined-segmented-button-selected-hover-label-text-color, var(--md-sys-color-on-secondary-container, #1d192b));--_selected-hover-state-layer-color: var(--md-outlined-segmented-button-selected-hover-state-layer-color, var(--md-sys-color-on-secondary-container, #1d192b));--_selected-label-text-color: var(--md-outlined-segmented-button-selected-label-text-color, var(--md-sys-color-on-secondary-container, #1d192b));--_selected-pressed-icon-color: var(--md-outlined-segmented-button-selected-pressed-icon-color, var(--md-sys-color-on-secondary-container, #1d192b));--_selected-pressed-label-text-color: var(--md-outlined-segmented-button-selected-pressed-label-text-color, var(--md-sys-color-on-secondary-container, #1d192b));--_selected-pressed-state-layer-color: var(--md-outlined-segmented-button-selected-pressed-state-layer-color, var(--md-sys-color-on-secondary-container, #1d192b));--_unselected-focus-icon-color: var(--md-outlined-segmented-button-unselected-focus-icon-color, var(--md-sys-color-on-surface, #1d1b20));--_unselected-focus-label-text-color: var(--md-outlined-segmented-button-unselected-focus-label-text-color, var(--md-sys-color-on-surface, #1d1b20));--_unselected-hover-icon-color: var(--md-outlined-segmented-button-unselected-hover-icon-color, var(--md-sys-color-on-surface, #1d1b20));--_unselected-hover-label-text-color: var(--md-outlined-segmented-button-unselected-hover-label-text-color, var(--md-sys-color-on-surface, #1d1b20));--_unselected-hover-state-layer-color: var(--md-outlined-segmented-button-unselected-hover-state-layer-color, var(--md-sys-color-on-surface, #1d1b20));--_unselected-label-text-color: var(--md-outlined-segmented-button-unselected-label-text-color, var(--md-sys-color-on-surface, #1d1b20));--_unselected-pressed-icon-color: var(--md-outlined-segmented-button-unselected-pressed-icon-color, var(--md-sys-color-on-surface, #1d1b20));--_unselected-pressed-label-text-color: var(--md-outlined-segmented-button-unselected-pressed-label-text-color, var(--md-sys-color-on-surface, #1d1b20));--_unselected-pressed-state-layer-color: var(--md-outlined-segmented-button-unselected-pressed-state-layer-color, var(--md-sys-color-on-surface, #1d1b20));--_icon-size: var(--md-outlined-segmented-button-icon-size, 18px);--_selected-icon-color: var(--md-outlined-segmented-button-selected-icon-color, var(--md-sys-color-on-secondary-container, #1d192b));--_unselected-icon-color: var(--md-outlined-segmented-button-unselected-icon-color, var(--md-sys-color-on-surface, #1d1b20));--_shape-start-start: var(--md-outlined-segmented-button-shape-start-start, var(--md-outlined-segmented-button-shape, var(--md-sys-shape-corner-full, 9999px)));--_shape-start-end: var(--md-outlined-segmented-button-shape-start-end, var(--md-outlined-segmented-button-shape, var(--md-sys-shape-corner-full, 9999px)));--_shape-end-end: var(--md-outlined-segmented-button-shape-end-end, var(--md-outlined-segmented-button-shape, var(--md-sys-shape-corner-full, 9999px)));--_shape-end-start: var(--md-outlined-segmented-button-shape-end-start, var(--md-outlined-segmented-button-shape, var(--md-sys-shape-corner-full, 9999px)))}
`;

// node_modules/@material/web/labs/segmentedbuttonset/internal/shared-styles.js
var styles2 = css`:host{display:flex;outline:none}.md3-segmented-button-set{display:grid;grid-auto-columns:1fr;grid-auto-flow:column;grid-auto-rows:auto;width:100%;height:var(--_container-height)}.md3-segmented-button-set ::slotted(:first-child){border-start-start-radius:var(--_shape-start-start);border-end-start-radius:var(--_shape-end-start)}.md3-segmented-button-set ::slotted(:last-child){border-start-end-radius:var(--_shape-start-end);border-end-end-radius:var(--_shape-end-end)}
`;

// node_modules/@material/web/labs/segmentedbuttonset/outlined-segmented-button-set.js
var MdOutlinedSegmentedButtonSet = class MdOutlinedSegmentedButtonSet2 extends OutlinedSegmentedButtonSet {
};
MdOutlinedSegmentedButtonSet.styles = [styles2, styles];
MdOutlinedSegmentedButtonSet = __decorate([
  customElement("md-outlined-segmented-button-set")
], MdOutlinedSegmentedButtonSet);
export {
  MdOutlinedSegmentedButtonSet
};
/*! Bundled license information:

@material/web/labs/segmentedbuttonset/internal/segmented-button-set.js:
  (**
   * @license
   * Copyright 2021 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   *)

@material/web/labs/segmentedbuttonset/internal/outlined-segmented-button-set.js:
@material/web/labs/segmentedbuttonset/outlined-segmented-button-set.js:
  (**
   * @license
   * Copyright 2022 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   *)

@material/web/labs/segmentedbuttonset/internal/outlined-styles.js:
@material/web/labs/segmentedbuttonset/internal/shared-styles.js:
  (**
   * @license
   * Copyright 2024 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   *)
*/
//# sourceMappingURL=@material_web_labs_segmentedbuttonset_outlined-segmented-button-set__js.js.map
