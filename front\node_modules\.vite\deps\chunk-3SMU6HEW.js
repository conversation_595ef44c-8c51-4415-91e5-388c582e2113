import {
  property
} from "./chunk-T3WMJB5E.js";
import {
  __decorate
} from "./chunk-HMZZ7KLC.js";

// node_modules/@material/web/labs/behaviors/focusable.js
var isFocusable = Symbol("isFocusable");
var privateIsFocusable = Symbol("privateIsFocusable");
var externalTabIndex = Symbol("externalTabIndex");
var isUpdatingTabIndex = Symbol("isUpdatingTabIndex");
var updateTabIndex = Symbol("updateTabIndex");
function mixinFocusable(base) {
  var _a, _b, _c;
  class FocusableElement extends base {
    constructor() {
      super(...arguments);
      this[_a] = true;
      this[_b] = null;
      this[_c] = false;
    }
    get [isFocusable]() {
      return this[privateIsFocusable];
    }
    set [isFocusable](value) {
      if (this[isFocusable] === value) {
        return;
      }
      this[privateIsFocusable] = value;
      this[updateTabIndex]();
    }
    connectedCallback() {
      super.connectedCallback();
      this[updateTabIndex]();
    }
    attributeChangedCallback(name, old, value) {
      if (name !== "tabindex") {
        super.attributeChangedCallback(name, old, value);
        return;
      }
      this.requestUpdate("tabIndex", Number(old ?? -1));
      if (this[isUpdatingTabIndex]) {
        return;
      }
      if (!this.hasAttribute("tabindex")) {
        this[externalTabIndex] = null;
        this[updateTabIndex]();
        return;
      }
      this[externalTabIndex] = this.tabIndex;
    }
    [(_a = privateIsFocusable, _b = externalTabIndex, _c = isUpdatingTabIndex, updateTabIndex)]() {
      const internalTabIndex = this[isFocusable] ? 0 : -1;
      const computedTabIndex = this[externalTabIndex] ?? internalTabIndex;
      this[isUpdatingTabIndex] = true;
      this.tabIndex = computedTabIndex;
      this[isUpdatingTabIndex] = false;
    }
  }
  __decorate([
    property({ noAccessor: true })
  ], FocusableElement.prototype, "tabIndex", void 0);
  return FocusableElement;
}

export {
  mixinFocusable
};
/*! Bundled license information:

@material/web/labs/behaviors/focusable.js:
  (**
   * @license
   * Copyright 2023 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   *)
*/
//# sourceMappingURL=chunk-3SMU6HEW.js.map
