{"version": 3, "sources": ["../../@material/web/menu/internal/controllers/shared.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {LitElement} from 'lit';\n\nimport {MenuItem} from './menuItemController.js';\nimport type {Corner, SurfacePositionTarget} from './surfacePositionController.js';\n\n/**\n * The interface needed for a Menu to work with other md-menu elements.\n */\nexport interface MenuSelf {\n  /**\n   * Whether or not the menu is currently opened.\n   */\n  open: boolean;\n  /**\n   * Skips the opening and closing animations.\n   */\n  quick: boolean;\n  /**\n   * Displays overflow content like a submenu.\n   *\n   * __NOTE__: This may cause adverse effects if you set\n   * `md-menu {max-height:...}`\n   * and have items overflowing items in the \"y\" direction.\n   */\n  hasOverflow: boolean;\n  /**\n   * Communicates to the menu that it is a submenu and should not handle the\n   * ArrowLeft button in LTR and ArrowRight button in RTL.\n   */\n  isSubmenu: boolean;\n  /**\n   * After closing, does not restore focus to the last focused element before\n   * the menu was opened.\n   */\n  skipRestoreFocus: boolean;\n  /**\n   * The corner of the anchor in which the menu should anchor to.\n   */\n  anchorCorner: Corner;\n  /**\n   * The corner of the menu in which the menu should anchor from.\n   */\n  menuCorner: Corner;\n  /**\n   * The element the menu should anchor to.\n   */\n  anchorElement: (HTMLElement & Partial<SurfacePositionTarget>) | null;\n  /**\n   * What the menu should focus by default when opened.\n   */\n  defaultFocus: FocusState;\n  /**\n   * An array of items managed by the list.\n   */\n  items: MenuItem[];\n  /**\n   * The positioning strategy of the menu.\n   *\n   * - `absolute` is relative to the anchor element.\n   * - `fixed` is relative to the window\n   * - `document` is relative to the document\n   */\n  positioning?: 'absolute' | 'fixed' | 'document';\n  /**\n   * Opens the menu.\n   */\n  show: () => void;\n  /**\n   * Closes the menu.\n   */\n  close: () => void;\n}\n\n/**\n * The interface needed for a Menu to work with other md-menu elements. Useful\n * for keeping your types safe when wrapping `md-menu`.\n */\nexport type Menu = MenuSelf & LitElement;\n\n/**\n * The reason the `close-menu` event was dispatched.\n */\nexport interface Reason {\n  kind: string;\n}\n\n/**\n * The click selection reason for the `close-menu` event. The menu was closed\n * because an item was selected via user click.\n */\nexport interface ClickReason extends Reason {\n  kind: typeof CloseReason.CLICK_SELECTION;\n}\n\n/**\n * The keydown reason for the `close-menu` event. The menu was closed\n * because a specific key was pressed. The default closing keys for\n * `md-menu-item` are, Space, Enter or Escape.\n */\nexport interface KeydownReason extends Reason {\n  kind: typeof CloseReason.KEYDOWN;\n  key: string;\n}\n\n/**\n * The default menu closing reasons for the material md-menu package.\n */\nexport type DefaultReasons = ClickReason | KeydownReason;\n\n/**\n * Creates an event that closes any parent menus.\n */\nexport function createCloseMenuEvent<T extends Reason = DefaultReasons>(\n  initiator: MenuItem,\n  reason: T,\n) {\n  return new CustomEvent<{\n    initiator: MenuItem;\n    itemPath: MenuItem[];\n    reason: T;\n  }>('close-menu', {\n    bubbles: true,\n    composed: true,\n    detail: {initiator, reason, itemPath: [initiator]},\n  });\n}\n\n/**\n * Creates an event that signals to the menu that it should stay open on the\n * focusout event.\n */\nexport function createStayOpenOnFocusoutEvent() {\n  return new Event('stay-open-on-focusout', {bubbles: true, composed: true});\n}\n\n/**\n * Creates an event that signals to the menu that it should close open on the\n * focusout event.\n */\nexport function createCloseOnFocusoutEvent() {\n  return new Event('close-on-focusout', {bubbles: true, composed: true});\n}\n\n/**\n * Creates a default close menu event used by md-menu.\n */\nexport const createDefaultCloseMenuEvent = createCloseMenuEvent<DefaultReasons>;\n\n/**\n * The type of the default close menu event used by md-menu.\n */\n// tslint:disable-next-line\nexport type CloseMenuEvent<T extends Reason = DefaultReasons> = ReturnType<\n  typeof createCloseMenuEvent<T>\n>;\n\n/**\n * Creates an event that requests the given item be selected.\n */\nexport function createDeactivateTypeaheadEvent() {\n  return new Event('deactivate-typeahead', {bubbles: true, composed: true});\n}\n\n/**\n * The type of the event that requests the typeahead functionality of containing\n * menu be deactivated.\n */\nexport type DeactivateTypeaheadEvent = ReturnType<\n  typeof createDeactivateTypeaheadEvent\n>;\n\n/**\n * Creates an event that requests the typeahead functionality of containing menu\n * be activated.\n */\nexport function createActivateTypeaheadEvent() {\n  return new Event('activate-typeahead', {bubbles: true, composed: true});\n}\n\n/**\n * The type of the event that requests the typeahead functionality of containing\n * menu be activated.\n */\nexport type ActivateTypeaheadEvent = ReturnType<\n  typeof createActivateTypeaheadEvent\n>;\n\n/**\n * Keys that are used to navigate menus.\n */\n// tslint:disable-next-line:enforce-name-casing We are mimicking enum style\nexport const NavigableKey = {\n  UP: 'ArrowUp',\n  DOWN: 'ArrowDown',\n  RIGHT: 'ArrowRight',\n  LEFT: 'ArrowLeft',\n} as const;\n\n/**\n * Keys that are used for selection in menus.\n */\n// tslint:disable-next-line:enforce-name-casing We are mimicking enum style\nexport const SelectionKey = {\n  SPACE: 'Space',\n  ENTER: 'Enter',\n} as const;\n\n/**\n * Default close `Reason` kind values.\n */\n// tslint:disable-next-line:enforce-name-casing We are mimicking enum style\nexport const CloseReason = {\n  CLICK_SELECTION: 'click-selection',\n  KEYDOWN: 'keydown',\n} as const;\n\n/**\n * Keys that can close menus.\n */\n// tslint:disable-next-line:enforce-name-casing We are mimicking enum style\nexport const KeydownCloseKey = {\n  ESCAPE: 'Escape',\n  SPACE: SelectionKey.SPACE,\n  ENTER: SelectionKey.ENTER,\n} as const;\n\ntype Values<T> = T[keyof T];\n\n/**\n * Determines whether the given key code is a key code that should close the\n * menu.\n *\n * @param code The KeyboardEvent code to check.\n * @return Whether or not the key code is in the predetermined list to close the\n * menu.\n */\nexport function isClosableKey(\n  code: string,\n): code is Values<typeof KeydownCloseKey> {\n  return Object.values(KeydownCloseKey).some((value) => value === code);\n}\n\n/**\n * Determines whether the given key code is a key code that should select a menu\n * item.\n *\n * @param code They KeyboardEvent code to check.\n * @return Whether or not the key code is in the predetermined list to select a\n * menu item.\n */\nexport function isSelectableKey(\n  code: string,\n): code is Values<typeof SelectionKey> {\n  return Object.values(SelectionKey).some((value) => value === code);\n}\n\n/**\n * Determines whether a target element is contained inside another element's\n * composed tree.\n *\n * @param target The potential contained element.\n * @param container The potential containing element of the target.\n * @returns Whether the target element is contained inside the container's\n * composed subtree\n */\nexport function isElementInSubtree(\n  target: EventTarget,\n  container: EventTarget,\n) {\n  // Dispatch a composed, bubbling event to check its path to see if the\n  // newly-focused element is contained in container's subtree\n  const focusEv = new Event('md-contains', {bubbles: true, composed: true});\n  let composedPath: EventTarget[] = [];\n  const listener = (ev: Event) => {\n    composedPath = ev.composedPath();\n  };\n\n  container.addEventListener('md-contains', listener);\n  target.dispatchEvent(focusEv);\n  container.removeEventListener('md-contains', listener);\n\n  const isContained = composedPath.length > 0;\n  return isContained;\n}\n\n/**\n * Element to focus on when menu is first opened.\n */\n// tslint:disable-next-line:enforce-name-casing We are mimicking enum style\nexport const FocusState = {\n  NONE: 'none',\n  LIST_ROOT: 'list-root',\n  FIRST_ITEM: 'first-item',\n  LAST_ITEM: 'last-item',\n} as const;\n\n/**\n * Element to focus on when menu is first opened.\n */\nexport type FocusState = (typeof FocusState)[keyof typeof FocusState];\n"], "mappings": ";AAsHM,SAAU,qBACd,WACA,QAAS;AAET,SAAO,IAAI,YAIR,cAAc;IACf,SAAS;IACT,UAAU;IACV,QAAQ,EAAC,WAAW,QAAQ,UAAU,CAAC,SAAS,EAAC;GAClD;AACH;AAqBO,IAAM,8BAA8B;AAarC,SAAU,iCAA8B;AAC5C,SAAO,IAAI,MAAM,wBAAwB,EAAC,SAAS,MAAM,UAAU,KAAI,CAAC;AAC1E;AAcM,SAAU,+BAA4B;AAC1C,SAAO,IAAI,MAAM,sBAAsB,EAAC,SAAS,MAAM,UAAU,KAAI,CAAC;AACxE;AAcO,IAAM,eAAe;EAC1B,IAAI;EACJ,MAAM;EACN,OAAO;EACP,MAAM;;AAOD,IAAM,eAAe;EAC1B,OAAO;EACP,OAAO;;AAOF,IAAM,cAAc;EACzB,iBAAiB;EACjB,SAAS;;AAOJ,IAAM,kBAAkB;EAC7B,QAAQ;EACR,OAAO,aAAa;EACpB,OAAO,aAAa;;AAahB,SAAU,cACd,MAAY;AAEZ,SAAO,OAAO,OAAO,eAAe,EAAE,KAAK,CAAC,UAAU,UAAU,IAAI;AACtE;AAUM,SAAU,gBACd,MAAY;AAEZ,SAAO,OAAO,OAAO,YAAY,EAAE,KAAK,CAAC,UAAU,UAAU,IAAI;AACnE;AAWM,SAAU,mBACd,QACA,WAAsB;AAItB,QAAM,UAAU,IAAI,MAAM,eAAe,EAAC,SAAS,MAAM,UAAU,KAAI,CAAC;AACxE,MAAI,eAA8B,CAAA;AAClC,QAAM,WAAW,CAAC,OAAa;AAC7B,mBAAe,GAAG,aAAY;EAChC;AAEA,YAAU,iBAAiB,eAAe,QAAQ;AAClD,SAAO,cAAc,OAAO;AAC5B,YAAU,oBAAoB,eAAe,QAAQ;AAErD,QAAM,cAAc,aAAa,SAAS;AAC1C,SAAO;AACT;AAMO,IAAM,aAAa;EACxB,MAAM;EACN,WAAW;EACX,YAAY;EACZ,WAAW;;", "names": []}