{"version": 3, "sources": ["../../@material/web/labs/navigationdrawer/internal/navigation-drawer-modal.ts", "../../@material/web/labs/navigationdrawer/internal/navigation-drawer-modal-styles.ts", "../../@material/web/labs/navigationdrawer/navigation-drawer-modal.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {html, LitElement, nothing, PropertyValues} from 'lit';\nimport {property} from 'lit/decorators.js';\nimport {classMap} from 'lit/directives/class-map.js';\n\nimport {ARIAMixinStrict} from '../../../internal/aria/aria.js';\nimport {mixinDelegatesAria} from '../../../internal/aria/delegate.js';\n\n// Separate variable needed for closure.\nconst navigationDrawerModalBaseClass = mixinDelegatesAria(LitElement);\n\n/**\n * b/265346501 - add docs\n *\n * @fires navigation-drawer-changed {CustomEvent<{opened: boolean}>}\n * Dispatched whenever the drawer opens or closes --bubbles --composed\n */\nexport class NavigationDrawerModal extends navigationDrawerModalBaseClass {\n  @property({type: Boolean}) opened = false;\n  @property() pivot: 'start' | 'end' = 'end';\n\n  protected override render() {\n    const ariaExpanded = this.opened ? 'true' : 'false';\n    const ariaHidden = !this.opened ? 'true' : 'false';\n    // Needed for closure conformance\n    const {ariaLabel, ariaModal} = this as ARIAMixinStrict;\n    return html`\n      <div\n        class=\"md3-navigation-drawer-modal__scrim ${this.getScrimClasses()}\"\n        @click=\"${this.handleScrimClick}\">\n      </div>\n      <div\n        aria-expanded=${ariaExpanded}\n        aria-hidden=${ariaHidden}\n        aria-label=${ariaLabel || nothing}\n        aria-modal=${ariaModal || nothing}\n        class=\"md3-navigation-drawer-modal ${this.getRenderClasses()}\"\n        @keydown=\"${this.handleKeyDown}\"\n        role=\"dialog\"\n        ><div class=\"md3-elevation-overlay\"></div>\n        <div class=\"md3-navigation-drawer-modal__slot-content\">\n          <slot></slot>\n        </div>\n      </div>\n    `;\n  }\n\n  private getScrimClasses() {\n    return classMap({\n      'md3-navigation-drawer-modal--scrim-visible': this.opened,\n    });\n  }\n\n  private getRenderClasses() {\n    return classMap({\n      'md3-navigation-drawer-modal--opened': this.opened,\n      'md3-navigation-drawer-modal--pivot-at-start': this.pivot === 'start',\n    });\n  }\n\n  protected override updated(\n    changedProperties: PropertyValues<NavigationDrawerModal>,\n  ) {\n    if (changedProperties.has('opened')) {\n      setTimeout(() => {\n        this.dispatchEvent(\n          new CustomEvent('navigation-drawer-changed', {\n            detail: {opened: this.opened},\n            bubbles: true,\n            composed: true,\n          }),\n        );\n      }, 250);\n    }\n  }\n\n  private handleKeyDown(event: KeyboardEvent) {\n    if (event.code === 'Escape') {\n      this.opened = false;\n    }\n  }\n\n  private handleScrimClick() {\n    this.opened = !this.opened;\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n// Generated stylesheet for ./labs/navigationdrawer/internal/navigation-drawer-modal-styles.css.\nimport {css} from 'lit';\nexport const styles = css`:host{--_container-color: var(--md-navigation-drawer-modal-container-color, #fff);--_container-height: var(--md-navigation-drawer-modal-container-height, 100%);--_container-shape: var(--md-navigation-drawer-modal-container-shape, 0 16px 16px 0);--_container-width: var(--md-navigation-drawer-modal-container-width, 360px);--_divider-color: var(--md-navigation-drawer-modal-divider-color, #000);--_modal-container-elevation: var(--md-navigation-drawer-modal-modal-container-elevation, 1);--_scrim-color: var(--md-navigation-drawer-modal-scrim-color, );--_scrim-opacity: var(--md-navigation-drawer-modal-scrim-opacity, 0.04);--_standard-container-elevation: var(--md-navigation-drawer-modal-standard-container-elevation, 0);--md-elevation-level: var(--_modal-container-elevation)}.md3-navigation-drawer-modal{bottom:0;box-sizing:border-box;display:flex;justify-content:flex-end;overflow:hidden;position:absolute;top:0;inline-size:0;transition:inline-size .25s cubic-bezier(0.4, 0, 0.2, 1) 0s,visibility 0s cubic-bezier(0.4, 0, 0.2, 1) .25s}.md3-navigation-drawer-modal--opened{transition:inline-size .25s cubic-bezier(0.4, 0, 0.2, 1) 0s,visibility 0s cubic-bezier(0.4, 0, 0.2, 1) 0s}.md3-navigation-drawer-modal--pivot-at-start{justify-content:flex-start}.md3-navigation-drawer-modal__slot-content{display:flex;flex-direction:column;position:relative}.md3-navigation-drawer-modal__scrim{inset:0;opacity:0;position:absolute;visibility:hidden;background-color:var(--_scrim-color);transition:opacity .25s cubic-bezier(0.4, 0, 0.2, 1) 0s,visibility 0s cubic-bezier(0.4, 0, 0.2, 1) .25s}.md3-navigation-drawer-modal--scrim-visible{visibility:visible;opacity:var(--_scrim-opacity);transition:opacity .25s cubic-bezier(0.4, 0, 0.2, 1) 0s,visibility 0s cubic-bezier(0.4, 0, 0.2, 1) 0s}\n`;\n", "/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {customElement} from 'lit/decorators.js';\n\nimport {NavigationDrawerModal} from './internal/navigation-drawer-modal.js';\nimport {styles} from './internal/navigation-drawer-modal-styles.js';\nimport {styles as sharedStyles} from './internal/shared-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-navigation-drawer-modal': MdNavigationDrawerModal;\n  }\n}\n\n/**\n * @final\n * @suppress {visibility}\n */\n@customElement('md-navigation-drawer-modal')\nexport class MdNavigationDrawerModal extends NavigationDrawerModal {\n  static override readonly styles = [sharedStyles, styles];\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAcA,IAAM,iCAAiC,mBAAmB,UAAU;AAQ9D,IAAO,wBAAP,cAAqC,+BAA8B;EAAzE,cAAA;;AAC6B,SAAA,SAAS;AACxB,SAAA,QAAyB;EAkEvC;EAhEqB,SAAM;AACvB,UAAM,eAAe,KAAK,SAAS,SAAS;AAC5C,UAAM,aAAa,CAAC,KAAK,SAAS,SAAS;AAE3C,UAAM,EAAC,WAAW,UAAS,IAAI;AAC/B,WAAO;;oDAEyC,KAAK,gBAAe,CAAE;kBACxD,KAAK,gBAAgB;;;wBAGf,YAAY;sBACd,UAAU;qBACX,aAAa,OAAO;qBACpB,aAAa,OAAO;6CACI,KAAK,iBAAgB,CAAE;oBAChD,KAAK,aAAa;;;;;;;;EAQpC;EAEQ,kBAAe;AACrB,WAAO,SAAS;MACd,8CAA8C,KAAK;KACpD;EACH;EAEQ,mBAAgB;AACtB,WAAO,SAAS;MACd,uCAAuC,KAAK;MAC5C,+CAA+C,KAAK,UAAU;KAC/D;EACH;EAEmB,QACjB,mBAAwD;AAExD,QAAI,kBAAkB,IAAI,QAAQ,GAAG;AACnC,iBAAW,MAAK;AACd,aAAK,cACH,IAAI,YAAY,6BAA6B;UAC3C,QAAQ,EAAC,QAAQ,KAAK,OAAM;UAC5B,SAAS;UACT,UAAU;SACX,CAAC;MAEN,GAAG,GAAG;IACR;EACF;EAEQ,cAAc,OAAoB;AACxC,QAAI,MAAM,SAAS,UAAU;AAC3B,WAAK,SAAS;IAChB;EACF;EAEQ,mBAAgB;AACtB,SAAK,SAAS,CAAC,KAAK;EACtB;;AAlE2B,WAAA;EAA1B,SAAS,EAAC,MAAM,QAAO,CAAC;;AACb,WAAA;EAAX,SAAQ;;;;ACjBJ,IAAMA,UAAS;;;;ACgBf,IAAM,0BAAN,MAAMC,iCAAgC,sBAAqB;;AACvC,wBAAA,SAAS,CAAC,QAAcC,OAAM;AAD5C,0BAAuB,WAAA;EADnC,cAAc,4BAA4B;GAC9B,uBAAuB;", "names": ["styles", "MdNavigationDrawerModal", "styles"]}