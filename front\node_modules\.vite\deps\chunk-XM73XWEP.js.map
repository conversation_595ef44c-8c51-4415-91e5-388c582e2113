{"version": 3, "sources": ["../../@material/web/internal/controller/attachable-controller.ts", "../../@material/web/focus/internal/focus-ring.ts", "../../@material/web/focus/internal/focus-ring-styles.ts", "../../@material/web/focus/md-focus-ring.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {is<PERSON><PERSON><PERSON>, ReactiveController, ReactiveControllerHost} from 'lit';\n\n/**\n * An element that can be attached to an associated controlling element.\n */\nexport interface Attachable {\n  /**\n   * Reflects the value of the `for` attribute, which is the ID of the element's\n   * associated control.\n   *\n   * Use this when the elements's associated control is not its parent.\n   *\n   * To manually control an element, set its `for` attribute to `\"\"`.\n   *\n   * @example\n   * ```html\n   * <div class=\"container\">\n   *   <md-attachable for=\"interactive\"></md-attachable>\n   *   <button id=\"interactive\">Action</button>\n   * </div>\n   * ```\n   *\n   * @example\n   * ```html\n   * <button class=\"manually-controlled\">\n   *   <md-attachable for=\"\"></md-attachable>\n   * </button>\n   * ```\n   */\n  htmlFor: string | null;\n\n  /**\n   * Gets or sets the element that controls the visibility of the attachable\n   * element. It is one of:\n   *\n   * - The control referenced by the `for` attribute.\n   * - The control provided to `element.attach(control)`\n   * - The element's parent.\n   * - `null` if the element is not controlled.\n   */\n  control: HTMLElement | null;\n\n  /**\n   * Attaches the element to an interactive control.\n   *\n   * @param control The element that controls the attachable element.\n   */\n  attach(control: HTMLElement): void;\n\n  /**\n   * Detaches the element from its current control.\n   */\n  detach(): void;\n}\n\n/**\n * A key to retrieve an `Attachable` element's `AttachableController` from a\n * global `MutationObserver`.\n */\nconst ATTACHABLE_CONTROLLER = Symbol('attachableController');\n\n/**\n * The host of an `AttachableController`. The controller will add itself to\n * the host so it can be retrieved in a global `MutationObserver`.\n */\ninterface AttachableControllerHost extends ReactiveControllerHost, HTMLElement {\n  [ATTACHABLE_CONTROLLER]?: AttachableController;\n}\n\nlet FOR_ATTRIBUTE_OBSERVER: MutationObserver | undefined;\n\nif (!isServer) {\n  /**\n   * A global `MutationObserver` that reacts to `for` attribute changes on\n   * `Attachable` elements. If the `for` attribute changes, the controller will\n   * re-attach to the new referenced element.\n   */\n  FOR_ATTRIBUTE_OBSERVER = new MutationObserver((records) => {\n    for (const record of records) {\n      // When a control's `for` attribute changes, inform its\n      // `AttachableController` to update to a new control.\n      (record.target as AttachableControllerHost)[\n        ATTACHABLE_CONTROLLER\n      ]?.hostConnected();\n    }\n  });\n}\n\n/**\n * A controller that provides an implementation for `Attachable` elements.\n *\n * @example\n * ```ts\n * class MyElement extends LitElement implements Attachable {\n *   get control() { return this.attachableController.control; }\n *\n *   private readonly attachableController = new AttachableController(\n *     this,\n *     (previousControl, newControl) => {\n *       previousControl?.removeEventListener('click', this.handleClick);\n *       newControl?.addEventListener('click', this.handleClick);\n *     }\n *   );\n *\n *   // Implement remaining `Attachable` properties/methods that call the\n *   // controller's properties/methods.\n * }\n * ```\n */\nexport class AttachableController implements ReactiveController, Attachable {\n  get htmlFor() {\n    return this.host.getAttribute('for');\n  }\n\n  set htmlFor(htmlFor: string | null) {\n    if (htmlFor === null) {\n      this.host.removeAttribute('for');\n    } else {\n      this.host.setAttribute('for', htmlFor);\n    }\n  }\n\n  get control() {\n    if (this.host.hasAttribute('for')) {\n      if (!this.htmlFor || !this.host.isConnected) {\n        return null;\n      }\n\n      return (\n        this.host.getRootNode() as Document | ShadowRoot\n      ).querySelector<HTMLElement>(`#${this.htmlFor}`);\n    }\n\n    return this.currentControl || this.host.parentElement;\n  }\n  set control(control: HTMLElement | null) {\n    if (control) {\n      this.attach(control);\n    } else {\n      this.detach();\n    }\n  }\n\n  private currentControl: HTMLElement | null = null;\n\n  /**\n   * Creates a new controller for an `Attachable` element.\n   *\n   * @param host The `Attachable` element.\n   * @param onControlChange A callback with two parameters for the previous and\n   *     next control. An `Attachable` element may perform setup or teardown\n   *     logic whenever the control changes.\n   */\n  constructor(\n    private readonly host: AttachableControllerHost,\n    private readonly onControlChange: (\n      prev: HTMLElement | null,\n      next: HTMLElement | null,\n    ) => void,\n  ) {\n    host.addController(this);\n    host[ATTACHABLE_CONTROLLER] = this;\n    FOR_ATTRIBUTE_OBSERVER?.observe(host, {attributeFilter: ['for']});\n  }\n\n  attach(control: HTMLElement) {\n    if (control === this.currentControl) {\n      return;\n    }\n\n    this.setCurrentControl(control);\n    // When imperatively attaching, remove the `for` attribute so\n    // that the attached control is used instead of a referenced one.\n    this.host.removeAttribute('for');\n  }\n\n  detach() {\n    this.setCurrentControl(null);\n    // When imperatively detaching, add an empty `for=\"\"` attribute. This will\n    // ensure the control is `null` rather than the `parentElement`.\n    this.host.setAttribute('for', '');\n  }\n\n  /** @private */\n  hostConnected() {\n    this.setCurrentControl(this.control);\n  }\n\n  /** @private */\n  hostDisconnected() {\n    this.setCurrentControl(null);\n  }\n\n  private setCurrentControl(control: HTMLElement | null) {\n    this.onControlChange(this.currentControl, control);\n    this.currentControl = control;\n  }\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {isServer, LitElement, PropertyValues} from 'lit';\nimport {property} from 'lit/decorators.js';\n\nimport {\n  Attachable,\n  AttachableController,\n} from '../../internal/controller/attachable-controller.js';\n\n/**\n * Events that the focus ring listens to.\n */\nconst EVENTS = ['focusin', 'focusout', 'pointerdown'];\n\n/**\n * A focus ring component.\n *\n * @fires visibility-changed {Event} Fired whenever `visible` changes.\n */\nexport class FocusRing extends LitElement implements Attachable {\n  /**\n   * Makes the focus ring visible.\n   */\n  @property({type: Boolean, reflect: true}) visible = false;\n\n  /**\n   * Makes the focus ring animate inwards instead of outwards.\n   */\n  @property({type: Boolean, reflect: true}) inward = false;\n\n  get htmlFor() {\n    return this.attachableController.htmlFor;\n  }\n\n  set htmlFor(htmlFor: string | null) {\n    this.attachableController.htmlFor = htmlFor;\n  }\n\n  get control() {\n    return this.attachableController.control;\n  }\n  set control(control: HTMLElement | null) {\n    this.attachableController.control = control;\n  }\n\n  private readonly attachableController = new AttachableController(\n    this,\n    this.onControlChange.bind(this),\n  );\n\n  attach(control: HTMLElement) {\n    this.attachableController.attach(control);\n  }\n\n  detach() {\n    this.attachableController.detach();\n  }\n\n  override connectedCallback() {\n    super.connectedCallback();\n    // Needed for VoiceOver, which will create a \"group\" if the element is a\n    // sibling to other content.\n    this.setAttribute('aria-hidden', 'true');\n  }\n\n  /** @private */\n  handleEvent(event: FocusRingEvent) {\n    if (event[HANDLED_BY_FOCUS_RING]) {\n      // This ensures the focus ring does not activate when multiple focus rings\n      // are used within a single component.\n      return;\n    }\n\n    switch (event.type) {\n      default:\n        return;\n      case 'focusin':\n        this.visible = this.control?.matches(':focus-visible') ?? false;\n        break;\n      case 'focusout':\n      case 'pointerdown':\n        this.visible = false;\n        break;\n    }\n\n    event[HANDLED_BY_FOCUS_RING] = true;\n  }\n\n  private onControlChange(prev: HTMLElement | null, next: HTMLElement | null) {\n    if (isServer) return;\n\n    for (const event of EVENTS) {\n      prev?.removeEventListener(event, this);\n      next?.addEventListener(event, this);\n    }\n  }\n\n  override update(changed: PropertyValues<FocusRing>) {\n    if (changed.has('visible')) {\n      // This logic can be removed once the `:has` selector has been introduced\n      // to Firefox. This is necessary to allow correct submenu styles.\n      this.dispatchEvent(new Event('visibility-changed'));\n    }\n    super.update(changed);\n  }\n}\n\nconst HANDLED_BY_FOCUS_RING = Symbol('handledByFocusRing');\n\ninterface FocusRingEvent extends Event {\n  [HANDLED_BY_FOCUS_RING]: true;\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n// Generated stylesheet for ./focus/internal/focus-ring-styles.css.\nimport {css} from 'lit';\nexport const styles = css`:host{animation-delay:0s,calc(var(--md-focus-ring-duration, 600ms)*.25);animation-duration:calc(var(--md-focus-ring-duration, 600ms)*.25),calc(var(--md-focus-ring-duration, 600ms)*.75);animation-timing-function:cubic-bezier(0.2, 0, 0, 1);box-sizing:border-box;color:var(--md-focus-ring-color, var(--md-sys-color-secondary, #625b71));display:none;pointer-events:none;position:absolute}:host([visible]){display:flex}:host(:not([inward])){animation-name:outward-grow,outward-shrink;border-end-end-radius:calc(var(--md-focus-ring-shape-end-end, var(--md-focus-ring-shape, var(--md-sys-shape-corner-full, 9999px))) + var(--md-focus-ring-outward-offset, 2px));border-end-start-radius:calc(var(--md-focus-ring-shape-end-start, var(--md-focus-ring-shape, var(--md-sys-shape-corner-full, 9999px))) + var(--md-focus-ring-outward-offset, 2px));border-start-end-radius:calc(var(--md-focus-ring-shape-start-end, var(--md-focus-ring-shape, var(--md-sys-shape-corner-full, 9999px))) + var(--md-focus-ring-outward-offset, 2px));border-start-start-radius:calc(var(--md-focus-ring-shape-start-start, var(--md-focus-ring-shape, var(--md-sys-shape-corner-full, 9999px))) + var(--md-focus-ring-outward-offset, 2px));inset:calc(-1*var(--md-focus-ring-outward-offset, 2px));outline:var(--md-focus-ring-width, 3px) solid currentColor}:host([inward]){animation-name:inward-grow,inward-shrink;border-end-end-radius:calc(var(--md-focus-ring-shape-end-end, var(--md-focus-ring-shape, var(--md-sys-shape-corner-full, 9999px))) - var(--md-focus-ring-inward-offset, 0px));border-end-start-radius:calc(var(--md-focus-ring-shape-end-start, var(--md-focus-ring-shape, var(--md-sys-shape-corner-full, 9999px))) - var(--md-focus-ring-inward-offset, 0px));border-start-end-radius:calc(var(--md-focus-ring-shape-start-end, var(--md-focus-ring-shape, var(--md-sys-shape-corner-full, 9999px))) - var(--md-focus-ring-inward-offset, 0px));border-start-start-radius:calc(var(--md-focus-ring-shape-start-start, var(--md-focus-ring-shape, var(--md-sys-shape-corner-full, 9999px))) - var(--md-focus-ring-inward-offset, 0px));border:var(--md-focus-ring-width, 3px) solid currentColor;inset:var(--md-focus-ring-inward-offset, 0px)}@keyframes outward-grow{from{outline-width:0}to{outline-width:var(--md-focus-ring-active-width, 8px)}}@keyframes outward-shrink{from{outline-width:var(--md-focus-ring-active-width, 8px)}}@keyframes inward-grow{from{border-width:0}to{border-width:var(--md-focus-ring-active-width, 8px)}}@keyframes inward-shrink{from{border-width:var(--md-focus-ring-active-width, 8px)}}@media(prefers-reduced-motion){:host{animation:none}}\n`;\n", "/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {FocusRing} from './internal/focus-ring.js';\nimport {styles} from './internal/focus-ring-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-focus-ring': MdFocusRing;\n  }\n}\n\n/**\n * TODO(b/*********): add docs\n *\n * @final\n * @suppress {visibility}\n */\n@customElement('md-focus-ring')\nexport class MdFocusRing extends FocusRing {\n  static override styles: CSSResultOrNative[] = [styles];\n}\n"], "mappings": ";;;;;;;;;;;;;;AAiEA,IAAM,wBAAwB,OAAO,sBAAsB;AAU3D,IAAI;AAEJ,IAAI,CAAC,UAAU;AAMb,2BAAyB,IAAI,iBAAiB,CAAC,YAAW;AACxD,eAAW,UAAU,SAAS;AAG3B,aAAO,OACN,qBAAqB,GACpB,cAAa;IAClB;EACF,CAAC;AACH;AAuBM,IAAO,uBAAP,MAA2B;EAC/B,IAAI,UAAO;AACT,WAAO,KAAK,KAAK,aAAa,KAAK;EACrC;EAEA,IAAI,QAAQ,SAAsB;AAChC,QAAI,YAAY,MAAM;AACpB,WAAK,KAAK,gBAAgB,KAAK;IACjC,OAAO;AACL,WAAK,KAAK,aAAa,OAAO,OAAO;IACvC;EACF;EAEA,IAAI,UAAO;AACT,QAAI,KAAK,KAAK,aAAa,KAAK,GAAG;AACjC,UAAI,CAAC,KAAK,WAAW,CAAC,KAAK,KAAK,aAAa;AAC3C,eAAO;MACT;AAEA,aACE,KAAK,KAAK,YAAW,EACrB,cAA2B,IAAI,KAAK,OAAO,EAAE;IACjD;AAEA,WAAO,KAAK,kBAAkB,KAAK,KAAK;EAC1C;EACA,IAAI,QAAQ,SAA2B;AACrC,QAAI,SAAS;AACX,WAAK,OAAO,OAAO;IACrB,OAAO;AACL,WAAK,OAAM;IACb;EACF;;;;;;;;;EAYA,YACmB,MACA,iBAGR;AAJQ,SAAA,OAAA;AACA,SAAA,kBAAA;AAZX,SAAA,iBAAqC;AAiB3C,SAAK,cAAc,IAAI;AACvB,SAAK,qBAAqB,IAAI;AAC9B,4BAAwB,QAAQ,MAAM,EAAC,iBAAiB,CAAC,KAAK,EAAC,CAAC;EAClE;EAEA,OAAO,SAAoB;AACzB,QAAI,YAAY,KAAK,gBAAgB;AACnC;IACF;AAEA,SAAK,kBAAkB,OAAO;AAG9B,SAAK,KAAK,gBAAgB,KAAK;EACjC;EAEA,SAAM;AACJ,SAAK,kBAAkB,IAAI;AAG3B,SAAK,KAAK,aAAa,OAAO,EAAE;EAClC;;EAGA,gBAAa;AACX,SAAK,kBAAkB,KAAK,OAAO;EACrC;;EAGA,mBAAgB;AACd,SAAK,kBAAkB,IAAI;EAC7B;EAEQ,kBAAkB,SAA2B;AACnD,SAAK,gBAAgB,KAAK,gBAAgB,OAAO;AACjD,SAAK,iBAAiB;EACxB;;;;ACzLF,IAAM,SAAS,CAAC,WAAW,YAAY,aAAa;AAO9C,IAAO,YAAP,cAAyB,WAAU;EAAzC,cAAA;;AAI4C,SAAA,UAAU;AAKV,SAAA,SAAS;AAiBlC,SAAA,uBAAuB,IAAI,qBAC1C,MACA,KAAK,gBAAgB,KAAK,IAAI,CAAC;EA0DnC;EA3EE,IAAI,UAAO;AACT,WAAO,KAAK,qBAAqB;EACnC;EAEA,IAAI,QAAQ,SAAsB;AAChC,SAAK,qBAAqB,UAAU;EACtC;EAEA,IAAI,UAAO;AACT,WAAO,KAAK,qBAAqB;EACnC;EACA,IAAI,QAAQ,SAA2B;AACrC,SAAK,qBAAqB,UAAU;EACtC;EAOA,OAAO,SAAoB;AACzB,SAAK,qBAAqB,OAAO,OAAO;EAC1C;EAEA,SAAM;AACJ,SAAK,qBAAqB,OAAM;EAClC;EAES,oBAAiB;AACxB,UAAM,kBAAiB;AAGvB,SAAK,aAAa,eAAe,MAAM;EACzC;;EAGA,YAAY,OAAqB;AAC/B,QAAI,MAAM,qBAAqB,GAAG;AAGhC;IACF;AAEA,YAAQ,MAAM,MAAM;MAClB;AACE;MACF,KAAK;AACH,aAAK,UAAU,KAAK,SAAS,QAAQ,gBAAgB,KAAK;AAC1D;MACF,KAAK;MACL,KAAK;AACH,aAAK,UAAU;AACf;IACJ;AAEA,UAAM,qBAAqB,IAAI;EACjC;EAEQ,gBAAgB,MAA0B,MAAwB;AACxE,QAAI;AAAU;AAEd,eAAW,SAAS,QAAQ;AAC1B,YAAM,oBAAoB,OAAO,IAAI;AACrC,YAAM,iBAAiB,OAAO,IAAI;IACpC;EACF;EAES,OAAO,SAAkC;AAChD,QAAI,QAAQ,IAAI,SAAS,GAAG;AAG1B,WAAK,cAAc,IAAI,MAAM,oBAAoB,CAAC;IACpD;AACA,UAAM,OAAO,OAAO;EACtB;;AAjF0C,WAAA;EAAzC,SAAS,EAAC,MAAM,SAAS,SAAS,KAAI,CAAC;;AAKE,WAAA;EAAzC,SAAS,EAAC,MAAM,SAAS,SAAS,KAAI,CAAC;;AA+E1C,IAAM,wBAAwB,OAAO,oBAAoB;;;ACzGlD,IAAM,SAAS;;;;ACkBf,IAAM,cAAN,MAAMA,qBAAoB,UAAS;;AACxB,YAAA,SAA8B,CAAC,MAAM;AAD1C,cAAW,WAAA;EADvB,cAAc,eAAe;GACjB,WAAW;", "names": ["MdFocusRing"]}