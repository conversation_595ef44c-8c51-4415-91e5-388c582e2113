{"version": 3, "sources": ["../../@material/web/fab/internal/fab-branded-styles.ts", "../../@material/web/fab/branded-fab.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2024 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n// Generated stylesheet for ./fab/internal/fab-branded-styles.css.\nimport {css} from 'lit';\nexport const styles = css`:host{--_container-color: var(--md-fab-branded-container-color, var(--md-sys-color-surface-container-high, #ece6f0));--_container-elevation: var(--md-fab-branded-container-elevation, 3);--_container-height: var(--md-fab-branded-container-height, 56px);--_container-shadow-color: var(--md-fab-branded-container-shadow-color, var(--md-sys-color-shadow, #000));--_container-width: var(--md-fab-branded-container-width, 56px);--_focus-container-elevation: var(--md-fab-branded-focus-container-elevation, 3);--_hover-container-elevation: var(--md-fab-branded-hover-container-elevation, 4);--_hover-state-layer-color: var(--md-fab-branded-hover-state-layer-color, var(--md-sys-color-primary, #6750a4));--_hover-state-layer-opacity: var(--md-fab-branded-hover-state-layer-opacity, 0.08);--_icon-size: var(--md-fab-branded-icon-size, 36px);--_lowered-container-color: var(--md-fab-branded-lowered-container-color, var(--md-sys-color-surface-container-low, #f7f2fa));--_lowered-container-elevation: var(--md-fab-branded-lowered-container-elevation, 1);--_lowered-focus-container-elevation: var(--md-fab-branded-lowered-focus-container-elevation, 1);--_lowered-hover-container-elevation: var(--md-fab-branded-lowered-hover-container-elevation, 2);--_lowered-pressed-container-elevation: var(--md-fab-branded-lowered-pressed-container-elevation, 1);--_pressed-container-elevation: var(--md-fab-branded-pressed-container-elevation, 3);--_pressed-state-layer-color: var(--md-fab-branded-pressed-state-layer-color, var(--md-sys-color-primary, #6750a4));--_pressed-state-layer-opacity: var(--md-fab-branded-pressed-state-layer-opacity, 0.12);--_focus-label-text-color: var(--md-fab-branded-focus-label-text-color, var(--md-sys-color-primary, #6750a4));--_hover-label-text-color: var(--md-fab-branded-hover-label-text-color, var(--md-sys-color-primary, #6750a4));--_label-text-color: var(--md-fab-branded-label-text-color, var(--md-sys-color-on-surface, #1d1b20));--_label-text-font: var(--md-fab-branded-label-text-font, var(--md-sys-typescale-label-large-font, var(--md-ref-typeface-plain, Roboto)));--_label-text-size: var(--md-fab-branded-label-text-size, var(--md-sys-typescale-label-large-size, 0.875rem));--_label-text-line-height: var(--md-fab-branded-label-text-line-height, var(--md-sys-typescale-label-large-line-height, 1.25rem));--_label-text-weight: var(--md-fab-branded-label-text-weight, var(--md-sys-typescale-label-large-weight, var(--md-ref-typeface-weight-medium, 500)));--_large-container-height: var(--md-fab-branded-large-container-height, 96px);--_large-container-width: var(--md-fab-branded-large-container-width, 96px);--_large-icon-size: var(--md-fab-branded-large-icon-size, 48px);--_pressed-label-text-color: var(--md-fab-branded-pressed-label-text-color, var(--md-sys-color-primary, #6750a4));--_container-shape-start-start: var(--md-fab-branded-container-shape-start-start, var(--md-fab-branded-container-shape, var(--md-sys-shape-corner-large, 16px)));--_container-shape-start-end: var(--md-fab-branded-container-shape-start-end, var(--md-fab-branded-container-shape, var(--md-sys-shape-corner-large, 16px)));--_container-shape-end-end: var(--md-fab-branded-container-shape-end-end, var(--md-fab-branded-container-shape, var(--md-sys-shape-corner-large, 16px)));--_container-shape-end-start: var(--md-fab-branded-container-shape-end-start, var(--md-fab-branded-container-shape, var(--md-sys-shape-corner-large, 16px)));--_large-container-shape-start-start: var(--md-fab-branded-large-container-shape-start-start, var(--md-fab-branded-large-container-shape, var(--md-sys-shape-corner-extra-large, 28px)));--_large-container-shape-start-end: var(--md-fab-branded-large-container-shape-start-end, var(--md-fab-branded-large-container-shape, var(--md-sys-shape-corner-extra-large, 28px)));--_large-container-shape-end-end: var(--md-fab-branded-large-container-shape-end-end, var(--md-fab-branded-large-container-shape, var(--md-sys-shape-corner-extra-large, 28px)));--_large-container-shape-end-start: var(--md-fab-branded-large-container-shape-end-start, var(--md-fab-branded-large-container-shape, var(--md-sys-shape-corner-extra-large, 28px)))}\n`;\n", "/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {Fab, FabVariant} from './internal/fab.js';\nimport {styles} from './internal/fab-branded-styles.js';\nimport {styles as forcedColors} from './internal/forced-colors-styles.js';\nimport {styles as sharedStyles} from './internal/shared-styles.js';\n\nexport {type FabSize} from './internal/shared.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-branded-fab': MdBrandedFab;\n  }\n}\n\n/**\n * @summary Floating action buttons (FABs) help people take primary actions.\n * They’re used to represent the most important action on a screen, such as\n * Create or Reply.\n *\n * @description\n * __Emphasis:__ High emphasis – For the primary, most important, or most common\n * action on a screen\n *\n * __Rationale:__ The FAB remains the default component for a screen’s primary\n * action. It comes in three sizes: small FAB, FAB, and large FAB. The extended\n * FAB’s wider format and text label give it more visual prominence than a  FAB.\n * It’s often used on larger screens where a FAB would seem too small. Branded\n * FABs are used to specifically call attention to branded logo icons.\n *\n * __Example usages:__\n * - FAB\n *   - Create\n *   - Compose\n * - Extended FAB\n *   - Create\n *   - Compose\n *   - New Thread\n *   - New File\n *\n * @final\n * @suppress {visibility}\n */\n@customElement('md-branded-fab')\nexport class MdBrandedFab extends Fab {\n  /**\n   * Branded FABs have no variants\n   */\n  declare variant: FabVariant;\n\n  override getRenderClasses() {\n    return {\n      ...super.getRenderClasses(),\n      'primary': false,\n      'secondary': false,\n      'tertiary': false,\n      'small': false,\n    };\n  }\n  static override styles: CSSResultOrNative[] = [\n    sharedStyles,\n    styles,\n    forcedColors,\n  ];\n}\n"], "mappings": ";;;;;;;;;;;;;;AAOO,IAAMA,UAAS;;;;AC4Cf,IAAM,eAAN,MAAMC,sBAAqB,IAAG;EAM1B,mBAAgB;AACvB,WAAO;MACL,GAAG,MAAM,iBAAgB;MACzB,WAAW;MACX,aAAa;MACb,YAAY;MACZ,SAAS;;EAEb;;AACgB,aAAA,SAA8B;EAC5CC;EACAA;EACA;;AAlBS,eAAY,WAAA;EADxB,cAAc,gBAAgB;GAClB,YAAY;", "names": ["styles", "MdBrandedFab", "styles"]}