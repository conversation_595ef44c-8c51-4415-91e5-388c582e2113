{"version": 3, "sources": ["../../@material/web/labs/behaviors/element-internals.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {LitElement} from 'lit';\n\nimport {MixinBase, MixinReturn} from './mixin.js';\n\n/**\n * A unique symbol used for protected access to an instance's\n * `ElementInternals`.\n *\n * @example\n * ```ts\n * class MyElement extends mixinElementInternals(LitElement) {\n *   constructor() {\n *     super();\n *     this[internals].role = 'button';\n *   }\n * }\n * ```\n */\nexport const internals = Symbol('internals');\n\n/**\n * An instance with an `internals` symbol property for the component's\n * `ElementInternals`.\n *\n * Use this when protected access is needed for an instance's `ElementInternals`\n * from other files. A unique symbol is used to access the internals.\n */\nexport interface WithElementInternals {\n  /**\n   * An instance's `ElementInternals`.\n   */\n  [internals]: ElementInternals;\n}\n\n// Private symbols\nconst privateInternals = Symbol('privateInternals');\n\n/**\n * Mixes in an attached `ElementInternals` instance.\n *\n * This mixin is only needed when other shared code needs access to a\n * component's `ElementInternals`, such as form-associated mixins.\n *\n * @param base The class to mix functionality into.\n * @return The provided class with `WithElementInternals` mixed in.\n */\nexport function mixinElementInternals<T extends MixinBase<LitElement>>(\n  base: T,\n): MixinReturn<T, WithElementInternals> {\n  abstract class WithElementInternalsElement\n    extends base\n    implements WithElementInternals\n  {\n    get [internals]() {\n      // Create internals in getter so that it can be used in methods called on\n      // construction in `ReactiveElement`, such as `requestUpdate()`.\n      if (!this[privateInternals]) {\n        // Cast needed for closure\n        this[privateInternals] = (this as HTMLElement).attachInternals();\n      }\n\n      return this[privateInternals];\n    }\n\n    [privateInternals]?: ElementInternals;\n  }\n\n  return WithElementInternalsElement;\n}\n"], "mappings": ";AAwBO,IAAM,YAAY,OAAO,WAAW;AAiB3C,IAAM,mBAAmB,OAAO,kBAAkB;AAW5C,SAAU,sBACd,MAAO;EAEP,MAAe,oCACL,KAAI;IAGZ,KAAK,SAAS,IAAC;AAGb,UAAI,CAAC,KAAK,gBAAgB,GAAG;AAE3B,aAAK,gBAAgB,IAAK,KAAqB,gBAAe;MAChE;AAEA,aAAO,KAAK,gBAAgB;IAC9B;;AAKF,SAAO;AACT;", "names": []}