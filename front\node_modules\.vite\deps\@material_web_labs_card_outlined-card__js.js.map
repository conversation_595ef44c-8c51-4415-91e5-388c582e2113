{"version": 3, "sources": ["../../@material/web/labs/card/internal/outlined-styles.ts", "../../@material/web/labs/card/outlined-card.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2024 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n// Generated stylesheet for ./labs/card/internal/outlined-styles.css.\nimport {css} from 'lit';\nexport const styles = css`:host{--_container-color: var(--md-outlined-card-container-color, var(--md-sys-color-surface, #fef7ff));--_container-elevation: var(--md-outlined-card-container-elevation, 0);--_container-shadow-color: var(--md-outlined-card-container-shadow-color, var(--md-sys-color-shadow, #000));--_container-shape: var(--md-outlined-card-container-shape, var(--md-sys-shape-corner-medium, 12px));--_outline-color: var(--md-outlined-card-outline-color, var(--md-sys-color-outline-variant, #cac4d0));--_outline-width: var(--md-outlined-card-outline-width, 1px)}.outline{border-color:var(--_outline-color);border-width:var(--_outline-width)}\n`;\n", "/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {Card} from './internal/card.js';\nimport {styles as outlinedStyles} from './internal/outlined-styles.js';\nimport {styles as sharedStyles} from './internal/shared-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-outlined-card': MdOutlinedCard;\n  }\n}\n\n/**\n * @final\n * @suppress {visibility}\n */\n@customElement('md-outlined-card')\nexport class MdOutlinedCard extends Card {\n  static override styles: CSSResultOrNative[] = [sharedStyles, outlinedStyles];\n}\n"], "mappings": ";;;;;;;;;;;;;;;AAOO,IAAMA,UAAS;;;;ACiBf,IAAM,iBAAN,MAAMC,wBAAuB,KAAI;;AACtB,eAAA,SAA8B,CAAC,QAAcC,OAAc;AADhE,iBAAc,WAAA;EAD1B,cAAc,kBAAkB;GACpB,cAAc;", "names": ["styles", "MdOutlinedCard", "styles"]}