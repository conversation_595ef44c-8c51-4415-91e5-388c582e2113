{"version": 3, "sources": ["../../@material/web/field/internal/shared-styles.ts", "../../@material/web/field/internal/field.ts", "../../@material/web/labs/behaviors/on-report-validity.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2024 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n// Generated stylesheet for ./field/internal/shared-styles.css.\nimport {css} from 'lit';\nexport const styles = css`:host{display:inline-flex;resize:both}.field{display:flex;flex:1;flex-direction:column;writing-mode:horizontal-tb;max-width:100%}.container-overflow{border-start-start-radius:var(--_container-shape-start-start);border-start-end-radius:var(--_container-shape-start-end);border-end-end-radius:var(--_container-shape-end-end);border-end-start-radius:var(--_container-shape-end-start);display:flex;height:100%;position:relative}.container{align-items:center;border-radius:inherit;display:flex;flex:1;max-height:100%;min-height:100%;min-width:min-content;position:relative}.field,.container-overflow{resize:inherit}.resizable:not(.disabled) .container{resize:inherit;overflow:hidden}.disabled{pointer-events:none}slot[name=container]{border-radius:inherit}slot[name=container]::slotted(*){border-radius:inherit;inset:0;pointer-events:none;position:absolute}@layer styles{.start,.middle,.end{display:flex;box-sizing:border-box;height:100%;position:relative}.start{color:var(--_leading-content-color)}.end{color:var(--_trailing-content-color)}.start,.end{align-items:center;justify-content:center}.with-start .start{margin-inline:var(--_with-leading-content-leading-space) var(--_content-space)}.with-end .end{margin-inline:var(--_content-space) var(--_with-trailing-content-trailing-space)}.middle{align-items:stretch;align-self:baseline;flex:1}.content{color:var(--_content-color);display:flex;flex:1;opacity:0;transition:opacity 83ms cubic-bezier(0.2, 0, 0, 1)}.no-label .content,.focused .content,.populated .content{opacity:1;transition-delay:67ms}:is(.disabled,.disable-transitions) .content{transition:none}.content ::slotted(*){all:unset;color:currentColor;font-family:var(--_content-font);font-size:var(--_content-size);line-height:var(--_content-line-height);font-weight:var(--_content-weight);width:100%;overflow-wrap:revert;white-space:revert}.content ::slotted(:not(textarea)){padding-top:var(--_top-space);padding-bottom:var(--_bottom-space)}.content ::slotted(textarea){margin-top:var(--_top-space);margin-bottom:var(--_bottom-space)}:hover .content{color:var(--_hover-content-color)}:hover .start{color:var(--_hover-leading-content-color)}:hover .end{color:var(--_hover-trailing-content-color)}.focused .content{color:var(--_focus-content-color)}.focused .start{color:var(--_focus-leading-content-color)}.focused .end{color:var(--_focus-trailing-content-color)}.disabled .content{color:var(--_disabled-content-color)}.disabled.no-label .content,.disabled.focused .content,.disabled.populated .content{opacity:var(--_disabled-content-opacity)}.disabled .start{color:var(--_disabled-leading-content-color);opacity:var(--_disabled-leading-content-opacity)}.disabled .end{color:var(--_disabled-trailing-content-color);opacity:var(--_disabled-trailing-content-opacity)}.error .content{color:var(--_error-content-color)}.error .start{color:var(--_error-leading-content-color)}.error .end{color:var(--_error-trailing-content-color)}.error:hover .content{color:var(--_error-hover-content-color)}.error:hover .start{color:var(--_error-hover-leading-content-color)}.error:hover .end{color:var(--_error-hover-trailing-content-color)}.error.focused .content{color:var(--_error-focus-content-color)}.error.focused .start{color:var(--_error-focus-leading-content-color)}.error.focused .end{color:var(--_error-focus-trailing-content-color)}}@layer hcm{@media(forced-colors: active){.disabled :is(.start,.content,.end){color:GrayText;opacity:1}}}@layer styles{.label{box-sizing:border-box;color:var(--_label-text-color);overflow:hidden;max-width:100%;text-overflow:ellipsis;white-space:nowrap;z-index:1;font-family:var(--_label-text-font);font-size:var(--_label-text-size);line-height:var(--_label-text-line-height);font-weight:var(--_label-text-weight);width:min-content}.label-wrapper{inset:0;pointer-events:none;position:absolute}.label.resting{position:absolute;top:var(--_top-space)}.label.floating{font-size:var(--_label-text-populated-size);line-height:var(--_label-text-populated-line-height);transform-origin:top left}.label.hidden{opacity:0}.no-label .label{display:none}.label-wrapper{inset:0;position:absolute;text-align:initial}:hover .label{color:var(--_hover-label-text-color)}.focused .label{color:var(--_focus-label-text-color)}.disabled .label{color:var(--_disabled-label-text-color)}.disabled .label:not(.hidden){opacity:var(--_disabled-label-text-opacity)}.error .label{color:var(--_error-label-text-color)}.error:hover .label{color:var(--_error-hover-label-text-color)}.error.focused .label{color:var(--_error-focus-label-text-color)}}@layer hcm{@media(forced-colors: active){.disabled .label:not(.hidden){color:GrayText;opacity:1}}}@layer styles{.supporting-text{color:var(--_supporting-text-color);display:flex;font-family:var(--_supporting-text-font);font-size:var(--_supporting-text-size);line-height:var(--_supporting-text-line-height);font-weight:var(--_supporting-text-weight);gap:16px;justify-content:space-between;padding-inline-start:var(--_supporting-text-leading-space);padding-inline-end:var(--_supporting-text-trailing-space);padding-top:var(--_supporting-text-top-space)}.supporting-text :nth-child(2){flex-shrink:0}:hover .supporting-text{color:var(--_hover-supporting-text-color)}.focus .supporting-text{color:var(--_focus-supporting-text-color)}.disabled .supporting-text{color:var(--_disabled-supporting-text-color);opacity:var(--_disabled-supporting-text-opacity)}.error .supporting-text{color:var(--_error-supporting-text-color)}.error:hover .supporting-text{color:var(--_error-hover-supporting-text-color)}.error.focus .supporting-text{color:var(--_error-focus-supporting-text-color)}}@layer hcm{@media(forced-colors: active){.disabled .supporting-text{color:GrayText;opacity:1}}}\n`;\n", "/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {\n  html,\n  LitElement,\n  nothing,\n  PropertyValues,\n  render,\n  TemplateResult,\n} from 'lit';\nimport {property, query, queryAssignedElements, state} from 'lit/decorators.js';\nimport {classMap} from 'lit/directives/class-map.js';\n\nimport {EASING} from '../../internal/motion/animation.js';\n\n/**\n * A field component.\n */\nexport class Field extends LitElement {\n  @property({type: Boolean}) disabled = false;\n  @property({type: Boolean}) error = false;\n  @property({type: Boolean}) focused = false;\n  @property() label = '';\n  @property({type: Boolean, attribute: 'no-asterisk'}) noAsterisk = false;\n  @property({type: Boolean}) populated = false;\n  @property({type: Boolean}) required = false;\n  @property({type: Boolean}) resizable = false;\n  @property({attribute: 'supporting-text'}) supportingText = '';\n  @property({attribute: 'error-text'}) errorText = '';\n  @property({type: Number}) count = -1;\n  @property({type: Number}) max = -1;\n\n  /**\n   * Whether or not the field has leading content.\n   */\n  @property({type: Boolean, attribute: 'has-start'}) hasStart = false;\n\n  /**\n   * Whether or not the field has trailing content.\n   */\n  @property({type: Boolean, attribute: 'has-end'}) hasEnd = false;\n\n  @queryAssignedElements({slot: 'aria-describedby'})\n  private readonly slottedAriaDescribedBy!: HTMLElement[];\n\n  private get counterText() {\n    // Count and max are typed as number, but can be set to null when Lit removes\n    // their attributes. These getters coerce back to a number for calculations.\n    const countAsNumber = this.count ?? -1;\n    const maxAsNumber = this.max ?? -1;\n    // Counter does not show if count is negative, or max is negative or 0.\n    if (countAsNumber < 0 || maxAsNumber <= 0) {\n      return '';\n    }\n\n    return `${countAsNumber} / ${maxAsNumber}`;\n  }\n\n  private get supportingOrErrorText() {\n    return this.error && this.errorText ? this.errorText : this.supportingText;\n  }\n\n  @state() private isAnimating = false;\n  private labelAnimation?: Animation;\n  /**\n   * When set to true, the error text's `role=\"alert\"` will be removed, then\n   * re-added after an animation frame. This will re-announce an error message\n   * to screen readers.\n   */\n  @state() private refreshErrorAlert = false;\n  @state() private disableTransitions = false;\n  @query('.label.floating')\n  private readonly floatingLabelEl!: HTMLElement | null;\n  @query('.label.resting') private readonly restingLabelEl!: HTMLElement | null;\n  @query('.container') private readonly containerEl!: HTMLElement | null;\n\n  /**\n   * Re-announces the field's error supporting text to screen readers.\n   *\n   * Error text announces to screen readers anytime it is visible and changes.\n   * Use the method to re-announce the message when the text has not changed,\n   * but announcement is still needed (such as for `reportValidity()`).\n   */\n  reannounceError() {\n    this.refreshErrorAlert = true;\n  }\n\n  protected override update(props: PropertyValues<Field>) {\n    // Client-side property updates\n    const isDisabledChanging =\n      props.has('disabled') && props.get('disabled') !== undefined;\n    if (isDisabledChanging) {\n      this.disableTransitions = true;\n    }\n\n    // When disabling, remove focus styles if focused.\n    if (this.disabled && this.focused) {\n      props.set('focused', true);\n      this.focused = false;\n    }\n\n    // Animate if focused or populated change.\n    this.animateLabelIfNeeded({\n      wasFocused: props.get('focused'),\n      wasPopulated: props.get('populated'),\n    });\n\n    super.update(props);\n  }\n\n  protected override render() {\n    const floatingLabel = this.renderLabel(/*isFloating*/ true);\n    const restingLabel = this.renderLabel(/*isFloating*/ false);\n    const outline = this.renderOutline?.(floatingLabel);\n    const classes = {\n      'disabled': this.disabled,\n      'disable-transitions': this.disableTransitions,\n      'error': this.error && !this.disabled,\n      'focused': this.focused,\n      'with-start': this.hasStart,\n      'with-end': this.hasEnd,\n      'populated': this.populated,\n      'resizable': this.resizable,\n      'required': this.required,\n      'no-label': !this.label,\n    };\n\n    return html`\n      <div class=\"field ${classMap(classes)}\">\n        <div class=\"container-overflow\">\n          ${this.renderBackground?.()}\n          <slot name=\"container\"></slot>\n          ${this.renderStateLayer?.()} ${this.renderIndicator?.()} ${outline}\n          <div class=\"container\">\n            <div class=\"start\">\n              <slot name=\"start\"></slot>\n            </div>\n            <div class=\"middle\">\n              <div class=\"label-wrapper\">\n                ${restingLabel} ${outline ? nothing : floatingLabel}\n              </div>\n              <div class=\"content\">\n                <slot></slot>\n              </div>\n            </div>\n            <div class=\"end\">\n              <slot name=\"end\"></slot>\n            </div>\n          </div>\n        </div>\n        ${this.renderSupportingText()}\n      </div>\n    `;\n  }\n\n  protected override updated(changed: PropertyValues<Field>) {\n    if (\n      changed.has('supportingText') ||\n      changed.has('errorText') ||\n      changed.has('count') ||\n      changed.has('max')\n    ) {\n      this.updateSlottedAriaDescribedBy();\n    }\n\n    if (this.refreshErrorAlert) {\n      // The past render cycle removed the role=\"alert\" from the error message.\n      // Re-add it after an animation frame to re-announce the error.\n      requestAnimationFrame(() => {\n        this.refreshErrorAlert = false;\n      });\n    }\n\n    if (this.disableTransitions) {\n      requestAnimationFrame(() => {\n        this.disableTransitions = false;\n      });\n    }\n  }\n\n  protected renderBackground?(): TemplateResult;\n  protected renderStateLayer?(): TemplateResult;\n  protected renderIndicator?(): TemplateResult;\n  protected renderOutline?(floatingLabel: unknown): TemplateResult;\n\n  private renderSupportingText() {\n    const {supportingOrErrorText, counterText} = this;\n    if (!supportingOrErrorText && !counterText) {\n      return nothing;\n    }\n\n    // Always render the supporting text span so that our `space-around`\n    // container puts the counter at the end.\n    const start = html`<span>${supportingOrErrorText}</span>`;\n    // Conditionally render counter so we don't render the extra `gap`.\n    // TODO(b/244473435): add aria-label and announcements\n    const end = counterText\n      ? html`<span class=\"counter\">${counterText}</span>`\n      : nothing;\n\n    // Announce if there is an error and error text visible.\n    // If refreshErrorAlert is true, do not announce. This will remove the\n    // role=\"alert\" attribute. Another render cycle will happen after an\n    // animation frame to re-add the role.\n    const shouldErrorAnnounce =\n      this.error && this.errorText && !this.refreshErrorAlert;\n    const role = shouldErrorAnnounce ? 'alert' : nothing;\n    return html`\n      <div class=\"supporting-text\" role=${role}>${start}${end}</div>\n      <slot\n        name=\"aria-describedby\"\n        @slotchange=${this.updateSlottedAriaDescribedBy}></slot>\n    `;\n  }\n\n  private updateSlottedAriaDescribedBy() {\n    for (const element of this.slottedAriaDescribedBy) {\n      render(html`${this.supportingOrErrorText} ${this.counterText}`, element);\n      element.setAttribute('hidden', '');\n    }\n  }\n\n  private renderLabel(isFloating: boolean) {\n    if (!this.label) {\n      return nothing;\n    }\n\n    let visible: boolean;\n    if (isFloating) {\n      // Floating label is visible when focused/populated or when animating.\n      visible = this.focused || this.populated || this.isAnimating;\n    } else {\n      // Resting label is visible when unfocused. It is never visible while\n      // animating.\n      visible = !this.focused && !this.populated && !this.isAnimating;\n    }\n\n    const classes = {\n      'hidden': !visible,\n      'floating': isFloating,\n      'resting': !isFloating,\n    };\n\n    // Add '*' if a label is present and the field is required\n    const labelText = `${this.label}${\n      this.required && !this.noAsterisk ? '*' : ''\n    }`;\n\n    return html`\n      <span class=\"label ${classMap(classes)}\" aria-hidden=${!visible}\n        >${labelText}</span\n      >\n    `;\n  }\n\n  private animateLabelIfNeeded({\n    wasFocused,\n    wasPopulated,\n  }: {\n    wasFocused?: boolean;\n    wasPopulated?: boolean;\n  }) {\n    if (!this.label) {\n      return;\n    }\n\n    wasFocused ??= this.focused;\n    wasPopulated ??= this.populated;\n    const wasFloating = wasFocused || wasPopulated;\n    const shouldBeFloating = this.focused || this.populated;\n    if (wasFloating === shouldBeFloating) {\n      return;\n    }\n\n    this.isAnimating = true;\n    this.labelAnimation?.cancel();\n\n    // Only one label is visible at a time for clearer text rendering.\n    // The floating label is visible and used during animation. At the end of\n    // the animation, it will either remain visible (if floating) or hide and\n    // the resting label will be shown.\n    //\n    // We don't use forward filling because if the dimensions of the text field\n    // change (leading icon removed, density changes, etc), then the animation\n    // will be inaccurate.\n    //\n    // Re-calculating the animation each time will prevent any visual glitches\n    // from appearing.\n    // TODO(b/241113345): use animation tokens\n    this.labelAnimation = this.floatingLabelEl?.animate(\n      this.getLabelKeyframes(),\n      {duration: 150, easing: EASING.STANDARD},\n    );\n\n    this.labelAnimation?.addEventListener('finish', () => {\n      // At the end of the animation, update the visible label.\n      this.isAnimating = false;\n    });\n  }\n\n  private getLabelKeyframes() {\n    const {floatingLabelEl, restingLabelEl} = this;\n    if (!floatingLabelEl || !restingLabelEl) {\n      return [];\n    }\n\n    const {\n      x: floatingX,\n      y: floatingY,\n      height: floatingHeight,\n    } = floatingLabelEl.getBoundingClientRect();\n    const {\n      x: restingX,\n      y: restingY,\n      height: restingHeight,\n    } = restingLabelEl.getBoundingClientRect();\n    const floatingScrollWidth = floatingLabelEl.scrollWidth;\n    const restingScrollWidth = restingLabelEl.scrollWidth;\n    // Scale by width ratio instead of font size since letter-spacing will scale\n    // incorrectly. Using the width we can better approximate the adjusted\n    // scale and compensate for tracking and overflow.\n    // (use scrollWidth instead of width to account for clipped labels)\n    const scale = restingScrollWidth / floatingScrollWidth;\n    const xDelta = restingX - floatingX;\n    // The line-height of the resting and floating label are different. When\n    // we move the floating label down to the resting label's position, it won't\n    // exactly match because of this. We need to adjust by half of what the\n    // final scaled floating label's height will be.\n    const yDelta =\n      restingY -\n      floatingY +\n      Math.round((restingHeight - floatingHeight * scale) / 2);\n\n    // Create the two transforms: floating to resting (using the calculations\n    // above), and resting to floating (re-setting the transform to initial\n    // values).\n    const restTransform = `translateX(${xDelta}px) translateY(${yDelta}px) scale(${scale})`;\n    const floatTransform = `translateX(0) translateY(0) scale(1)`;\n\n    // Constrain the floating labels width to a scaled percentage of the\n    // resting label's width. This will prevent long clipped labels from\n    // overflowing the container.\n    const restingClientWidth = restingLabelEl.clientWidth;\n    const isRestingClipped = restingScrollWidth > restingClientWidth;\n    const width = isRestingClipped ? `${restingClientWidth / scale}px` : '';\n    if (this.focused || this.populated) {\n      return [\n        {transform: restTransform, width},\n        {transform: floatTransform, width},\n      ];\n    }\n\n    return [\n      {transform: floatTransform, width},\n      {transform: restTransform, width},\n    ];\n  }\n\n  getSurfacePositionClientRect() {\n    return this.containerEl!.getBoundingClientRect();\n  }\n}\n", "/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {LitElement, isServer} from 'lit';\n\nimport {ConstraintValidation} from './constraint-validation.js';\nimport {WithElementInternals, internals} from './element-internals.js';\nimport {MixinBase, MixinReturn} from './mixin.js';\n\n/**\n * A constraint validation element that has a callback for when the element\n * should report validity styles and error messages to the user.\n *\n * This is commonly used in text-field-like controls that display error styles\n * and error messages.\n */\nexport interface OnReportValidity extends ConstraintValidation {\n  /**\n   * A callback that is invoked when validity should be reported. Components\n   * that can display their own error state can use this and update their\n   * styles.\n   *\n   * If an invalid event is provided, the element is invalid. If `null`, the\n   * element is valid.\n   *\n   * The invalid event's `preventDefault()` may be called to stop the platform\n   * popup from displaying.\n   *\n   * @param invalidEvent The `invalid` event dispatched when an element is\n   *     invalid, or `null` if the element is valid.\n   */\n  [onReportValidity](invalidEvent: Event | null): void;\n\n  // `mixinOnReportValidity()` implements this optional method. If overriden,\n  // call `super.formAssociatedCallback(form)`.\n  // (inherit jsdoc from `FormAssociated`)\n  formAssociatedCallback(form: HTMLFormElement | null): void;\n}\n\n/**\n * A symbol property used for a callback when validity has been reported.\n */\nexport const onReportValidity = Symbol('onReportValidity');\n\n// Private symbol members, used to avoid name clashing.\nconst privateCleanupFormListeners = Symbol('privateCleanupFormListeners');\nconst privateDoNotReportInvalid = Symbol('privateDoNotReportInvalid');\nconst privateIsSelfReportingValidity = Symbol('privateIsSelfReportingValidity');\nconst privateCallOnReportValidity = Symbol('privateCallOnReportValidity');\n\n/**\n * Mixes in a callback for constraint validation when validity should be\n * styled and reported to the user.\n *\n * This is commonly used in text-field-like controls that display error styles\n * and error messages.\n *\n * @example\n * ```ts\n * const baseClass = mixinOnReportValidity(\n *   mixinConstraintValidation(\n *     mixinFormAssociated(mixinElementInternals(LitElement)),\n *   ),\n * );\n *\n * class MyField extends baseClass {\n *   \\@property({type: Boolean}) error = false;\n *   \\@property() errorMessage = '';\n *\n *   [onReportValidity](invalidEvent: Event | null) {\n *     this.error = !!invalidEvent;\n *     this.errorMessage = this.validationMessage;\n *\n *     // Optionally prevent platform popup from displaying\n *     invalidEvent?.preventDefault();\n *   }\n * }\n * ```\n *\n * @param base The class to mix functionality into.\n * @return The provided class with `OnReportValidity` mixed in.\n */\nexport function mixinOnReportValidity<\n  T extends MixinBase<LitElement & ConstraintValidation & WithElementInternals>,\n>(base: T): MixinReturn<T, OnReportValidity> {\n  abstract class OnReportValidityElement\n    extends base\n    implements OnReportValidity\n  {\n    /**\n     * Used to clean up event listeners when a new form is associated.\n     */\n    [privateCleanupFormListeners] = new AbortController();\n\n    /**\n     * Used to determine if an invalid event should report validity. Invalid\n     * events from `checkValidity()` do not trigger reporting.\n     */\n    [privateDoNotReportInvalid] = false;\n\n    /**\n     * Used to determine if the control is reporting validity from itself, or\n     * if a `<form>` is causing the validity report. Forms have different\n     * control focusing behavior.\n     */\n    [privateIsSelfReportingValidity] = false;\n\n    // Mixins must have a constructor with `...args: any[]`\n    // tslint:disable-next-line:no-any\n    constructor(...args: any[]) {\n      super(...args);\n      if (isServer) {\n        return;\n      }\n\n      this.addEventListener(\n        'invalid',\n        (invalidEvent) => {\n          // Listen for invalid events dispatched by a `<form>` when it tries to\n          // submit and the element is invalid. We ignore events dispatched when\n          // calling `checkValidity()` as well as untrusted events, since the\n          // `reportValidity()` and `<form>`-dispatched events are always\n          // trusted.\n          if (this[privateDoNotReportInvalid] || !invalidEvent.isTrusted) {\n            return;\n          }\n\n          this.addEventListener(\n            'invalid',\n            () => {\n              // A normal bubbling phase event listener. By adding it here, we\n              // ensure it's the last event listener that is called during the\n              // bubbling phase.\n              this[privateCallOnReportValidity](invalidEvent);\n            },\n            {once: true},\n          );\n        },\n        {\n          // Listen during the capture phase, which will happen before the\n          // bubbling phase. That way, we can add a final event listener that\n          // will run after other event listeners, and we can check if it was\n          // default prevented. This works because invalid does not bubble.\n          capture: true,\n        },\n      );\n    }\n\n    override checkValidity() {\n      this[privateDoNotReportInvalid] = true;\n      const valid = super.checkValidity();\n      this[privateDoNotReportInvalid] = false;\n      return valid;\n    }\n\n    override reportValidity() {\n      this[privateIsSelfReportingValidity] = true;\n      const valid = super.reportValidity();\n      // Constructor's invalid listener will handle reporting invalid events.\n      if (valid) {\n        this[privateCallOnReportValidity](null);\n      }\n\n      this[privateIsSelfReportingValidity] = false;\n      return valid;\n    }\n\n    [privateCallOnReportValidity](invalidEvent: Event | null) {\n      // Since invalid events do not bubble to parent listeners, and because\n      // our invalid listeners are added lazily after other listeners, we can\n      // reliably read `defaultPrevented` synchronously without worrying\n      // about waiting for another listener that could cancel it.\n      const wasCanceled = invalidEvent?.defaultPrevented;\n      if (wasCanceled) {\n        return;\n      }\n\n      this[onReportValidity](invalidEvent);\n\n      // If an implementation calls invalidEvent.preventDefault() to stop the\n      // platform popup from displaying, focusing is also prevented, so we need\n      // to manually focus.\n      const implementationCanceledFocus =\n        !wasCanceled && invalidEvent?.defaultPrevented;\n      if (!implementationCanceledFocus) {\n        return;\n      }\n\n      // The control should be focused when:\n      // - `control.reportValidity()` is called (self-reporting).\n      // - a form is reporting validity for its controls and this is the first\n      //   invalid control.\n      if (\n        this[privateIsSelfReportingValidity] ||\n        isFirstInvalidControlInForm(this[internals].form, this)\n      ) {\n        this.focus();\n      }\n    }\n\n    [onReportValidity](invalidEvent: Event | null) {\n      throw new Error('Implement [onReportValidity]');\n    }\n\n    override formAssociatedCallback(form: HTMLFormElement | null) {\n      // can't use super.formAssociatedCallback?.() due to closure\n      if (super.formAssociatedCallback) {\n        super.formAssociatedCallback(form);\n      }\n\n      // Clean up previous form listeners.\n      this[privateCleanupFormListeners].abort();\n      if (!form) {\n        return;\n      }\n\n      this[privateCleanupFormListeners] = new AbortController();\n\n      // Add a listener that fires when the form runs constraint validation and\n      // the control is valid, so that it may remove its error styles.\n      //\n      // This happens on `form.reportValidity()` and `form.requestSubmit()`\n      // (both when the submit fails and passes).\n      addFormReportValidListener(\n        this,\n        form,\n        () => {\n          this[privateCallOnReportValidity](null);\n        },\n        this[privateCleanupFormListeners].signal,\n      );\n    }\n  }\n\n  return OnReportValidityElement;\n}\n\n/**\n * Add a listener that fires when a form runs constraint validation on a control\n * and it is valid. This is needed to clear previously invalid styles.\n *\n * @param control The control of the form to listen for valid events.\n * @param form The control's form that can run constraint validation.\n * @param onControlValid A listener that is called when the form runs constraint\n *     validation and the control is valid.\n * @param cleanup A cleanup signal to remove the listener.\n */\nfunction addFormReportValidListener(\n  control: Element,\n  form: HTMLFormElement,\n  onControlValid: () => void,\n  cleanup: AbortSignal,\n) {\n  const validateHooks = getFormValidateHooks(form);\n\n  // When a form validates its controls, check if an invalid event is dispatched\n  // on the control. If it is not, then inform the control to report its valid\n  // state.\n  let controlFiredInvalid = false;\n  let cleanupInvalidListener: AbortController | undefined;\n  let isNextSubmitFromHook = false;\n  validateHooks.addEventListener(\n    'before',\n    () => {\n      isNextSubmitFromHook = true;\n      cleanupInvalidListener = new AbortController();\n      controlFiredInvalid = false;\n      control.addEventListener(\n        'invalid',\n        () => {\n          controlFiredInvalid = true;\n        },\n        {\n          signal: cleanupInvalidListener.signal,\n        },\n      );\n    },\n    {signal: cleanup},\n  );\n\n  validateHooks.addEventListener(\n    'after',\n    () => {\n      isNextSubmitFromHook = false;\n      cleanupInvalidListener?.abort();\n      if (controlFiredInvalid) {\n        return;\n      }\n\n      onControlValid();\n    },\n    {signal: cleanup},\n  );\n\n  // The above hooks handle imperatively submitting the form, but not\n  // declaratively submitting the form. This happens when:\n  // 1. A non-custom element `<button type=\"submit\">` is clicked.\n  // 2. Enter is pressed on a non-custom element text editable `<input>`.\n  form.addEventListener(\n    'submit',\n    () => {\n      // This submit was from `form.requestSubmit()`, which already calls the\n      // listener.\n      if (isNextSubmitFromHook) {\n        return;\n      }\n\n      onControlValid();\n    },\n    {\n      signal: cleanup,\n    },\n  );\n\n  // Note: it is a known limitation that we cannot detect if a form tries to\n  // submit declaratively, but fails to do so because an unrelated sibling\n  // control failed its constraint validation.\n  //\n  // Since we cannot detect when that happens, a previously invalid control may\n  // not clear its error styling when it becomes valid again.\n  //\n  // To work around this, call `form.reportValidity()` when submitting a form\n  // declaratively. This can be down on the `<button type=\"submit\">`'s click or\n  // the text editable `<input>`'s 'Enter' keydown.\n}\n\nconst FORM_VALIDATE_HOOKS = new WeakMap<HTMLFormElement, EventTarget>();\n\n/**\n * Get a hooks `EventTarget` that dispatches 'before' and 'after' events that\n * fire before a form runs constraint validation and immediately after it\n * finishes running constraint validation on its controls.\n *\n * This happens during `form.reportValidity()` and `form.requestSubmit()`.\n *\n * @param form The form to get or set up hooks for.\n * @return A hooks `EventTarget` to add listeners to.\n */\nfunction getFormValidateHooks(form: HTMLFormElement) {\n  if (!FORM_VALIDATE_HOOKS.has(form)) {\n    // Patch form methods to add event listener hooks. These are needed to react\n    // to form behaviors that do not dispatch events, such as a form asking its\n    // controls to report their validity.\n    //\n    // We should only patch the methods once, since multiple controls and other\n    // forces may want to patch this method. We cannot reliably clean it up if\n    // there are multiple patched and re-patched methods referring holding\n    // references to each other.\n    //\n    // Instead, we never clean up the patch but add and clean up event listeners\n    // added to the hooks after the patch.\n    const hooks = new EventTarget();\n    FORM_VALIDATE_HOOKS.set(form, hooks);\n\n    // Add hooks to support notifying before and after a form has run constraint\n    // validation on its controls.\n    // Note: `form.submit()` does not run constraint validation per spec.\n    for (const methodName of ['reportValidity', 'requestSubmit'] as const) {\n      const superMethod = form[methodName];\n      form[methodName] = function (this: HTMLFormElement) {\n        hooks.dispatchEvent(new Event('before'));\n        const result = Reflect.apply(superMethod, this, arguments);\n        hooks.dispatchEvent(new Event('after'));\n        return result;\n      };\n    }\n  }\n\n  return FORM_VALIDATE_HOOKS.get(form)!;\n}\n\n/**\n * Checks if a control is the first invalid control in a form.\n *\n * @param form The control's form. When `null`, the control doesn't have a form\n *     and the method returns true.\n * @param control The control to check.\n * @return True if there is no form or if the control is the form's first\n *     invalid control.\n */\nfunction isFirstInvalidControlInForm(\n  form: HTMLFormElement | null,\n  control: HTMLElement,\n) {\n  if (!form) {\n    return true;\n  }\n\n  let firstInvalidControl: Element | undefined;\n  for (const element of form.elements) {\n    if (element.matches(':invalid')) {\n      firstInvalidControl = element;\n      break;\n    }\n  }\n\n  return firstInvalidControl === control;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOO,IAAM,SAAS;;;;ACehB,IAAO,QAAP,cAAqB,WAAU;EAArC,cAAA;;AAC6B,SAAA,WAAW;AACX,SAAA,QAAQ;AACR,SAAA,UAAU;AACzB,SAAA,QAAQ;AACiC,SAAA,aAAa;AACvC,SAAA,YAAY;AACZ,SAAA,WAAW;AACX,SAAA,YAAY;AACG,SAAA,iBAAiB;AACtB,SAAA,YAAY;AACvB,SAAA,QAAQ;AACR,SAAA,MAAM;AAKmB,SAAA,WAAW;AAKb,SAAA,SAAS;AAsBzC,SAAA,cAAc;AAOd,SAAA,oBAAoB;AACpB,SAAA,qBAAqB;EAmSxC;EA5TE,IAAY,cAAW;AAGrB,UAAM,gBAAgB,KAAK,SAAS;AACpC,UAAM,cAAc,KAAK,OAAO;AAEhC,QAAI,gBAAgB,KAAK,eAAe,GAAG;AACzC,aAAO;IACT;AAEA,WAAO,GAAG,aAAa,MAAM,WAAW;EAC1C;EAEA,IAAY,wBAAqB;AAC/B,WAAO,KAAK,SAAS,KAAK,YAAY,KAAK,YAAY,KAAK;EAC9D;;;;;;;;EAuBA,kBAAe;AACb,SAAK,oBAAoB;EAC3B;EAEmB,OAAO,OAA4B;AAEpD,UAAM,qBACJ,MAAM,IAAI,UAAU,KAAK,MAAM,IAAI,UAAU,MAAM;AACrD,QAAI,oBAAoB;AACtB,WAAK,qBAAqB;IAC5B;AAGA,QAAI,KAAK,YAAY,KAAK,SAAS;AACjC,YAAM,IAAI,WAAW,IAAI;AACzB,WAAK,UAAU;IACjB;AAGA,SAAK,qBAAqB;MACxB,YAAY,MAAM,IAAI,SAAS;MAC/B,cAAc,MAAM,IAAI,WAAW;KACpC;AAED,UAAM,OAAO,KAAK;EACpB;EAEmB,SAAM;AACvB,UAAM,gBAAgB,KAAK;;MAA2B;IAAI;AAC1D,UAAM,eAAe,KAAK;;MAA2B;IAAK;AAC1D,UAAM,UAAU,KAAK,gBAAgB,aAAa;AAClD,UAAM,UAAU;MACd,YAAY,KAAK;MACjB,uBAAuB,KAAK;MAC5B,SAAS,KAAK,SAAS,CAAC,KAAK;MAC7B,WAAW,KAAK;MAChB,cAAc,KAAK;MACnB,YAAY,KAAK;MACjB,aAAa,KAAK;MAClB,aAAa,KAAK;MAClB,YAAY,KAAK;MACjB,YAAY,CAAC,KAAK;;AAGpB,WAAO;0BACe,SAAS,OAAO,CAAC;;YAE/B,KAAK,mBAAkB,CAAE;;YAEzB,KAAK,mBAAkB,CAAE,IAAI,KAAK,kBAAiB,CAAE,IAAI,OAAO;;;;;;;kBAO1D,YAAY,IAAI,UAAU,UAAU,aAAa;;;;;;;;;;;UAWzD,KAAK,qBAAoB,CAAE;;;EAGnC;EAEmB,QAAQ,SAA8B;AACvD,QACE,QAAQ,IAAI,gBAAgB,KAC5B,QAAQ,IAAI,WAAW,KACvB,QAAQ,IAAI,OAAO,KACnB,QAAQ,IAAI,KAAK,GACjB;AACA,WAAK,6BAA4B;IACnC;AAEA,QAAI,KAAK,mBAAmB;AAG1B,4BAAsB,MAAK;AACzB,aAAK,oBAAoB;MAC3B,CAAC;IACH;AAEA,QAAI,KAAK,oBAAoB;AAC3B,4BAAsB,MAAK;AACzB,aAAK,qBAAqB;MAC5B,CAAC;IACH;EACF;EAOQ,uBAAoB;AAC1B,UAAM,EAAC,uBAAuB,YAAW,IAAI;AAC7C,QAAI,CAAC,yBAAyB,CAAC,aAAa;AAC1C,aAAO;IACT;AAIA,UAAM,QAAQ,aAAa,qBAAqB;AAGhD,UAAM,MAAM,cACR,6BAA6B,WAAW,YACxC;AAMJ,UAAM,sBACJ,KAAK,SAAS,KAAK,aAAa,CAAC,KAAK;AACxC,UAAM,OAAO,sBAAsB,UAAU;AAC7C,WAAO;0CAC+B,IAAI,IAAI,KAAK,GAAG,GAAG;;;sBAGvC,KAAK,4BAA4B;;EAErD;EAEQ,+BAA4B;AAClC,eAAW,WAAW,KAAK,wBAAwB;AACjD,aAAO,OAAO,KAAK,qBAAqB,IAAI,KAAK,WAAW,IAAI,OAAO;AACvE,cAAQ,aAAa,UAAU,EAAE;IACnC;EACF;EAEQ,YAAY,YAAmB;AACrC,QAAI,CAAC,KAAK,OAAO;AACf,aAAO;IACT;AAEA,QAAI;AACJ,QAAI,YAAY;AAEd,gBAAU,KAAK,WAAW,KAAK,aAAa,KAAK;IACnD,OAAO;AAGL,gBAAU,CAAC,KAAK,WAAW,CAAC,KAAK,aAAa,CAAC,KAAK;IACtD;AAEA,UAAM,UAAU;MACd,UAAU,CAAC;MACX,YAAY;MACZ,WAAW,CAAC;;AAId,UAAM,YAAY,GAAG,KAAK,KAAK,GAC7B,KAAK,YAAY,CAAC,KAAK,aAAa,MAAM,EAC5C;AAEA,WAAO;2BACgB,SAAS,OAAO,CAAC,iBAAiB,CAAC,OAAO;WAC1D,SAAS;;;EAGlB;EAEQ,qBAAqB,EAC3B,YACA,aAAY,GAIb;AACC,QAAI,CAAC,KAAK,OAAO;AACf;IACF;AAEA,mBAAe,KAAK;AACpB,qBAAiB,KAAK;AACtB,UAAM,cAAc,cAAc;AAClC,UAAM,mBAAmB,KAAK,WAAW,KAAK;AAC9C,QAAI,gBAAgB,kBAAkB;AACpC;IACF;AAEA,SAAK,cAAc;AACnB,SAAK,gBAAgB,OAAM;AAc3B,SAAK,iBAAiB,KAAK,iBAAiB,QAC1C,KAAK,kBAAiB,GACtB,EAAC,UAAU,KAAK,QAAQ,OAAO,SAAQ,CAAC;AAG1C,SAAK,gBAAgB,iBAAiB,UAAU,MAAK;AAEnD,WAAK,cAAc;IACrB,CAAC;EACH;EAEQ,oBAAiB;AACvB,UAAM,EAAC,iBAAiB,eAAc,IAAI;AAC1C,QAAI,CAAC,mBAAmB,CAAC,gBAAgB;AACvC,aAAO,CAAA;IACT;AAEA,UAAM,EACJ,GAAG,WACH,GAAG,WACH,QAAQ,eAAc,IACpB,gBAAgB,sBAAqB;AACzC,UAAM,EACJ,GAAG,UACH,GAAG,UACH,QAAQ,cAAa,IACnB,eAAe,sBAAqB;AACxC,UAAM,sBAAsB,gBAAgB;AAC5C,UAAM,qBAAqB,eAAe;AAK1C,UAAM,QAAQ,qBAAqB;AACnC,UAAM,SAAS,WAAW;AAK1B,UAAM,SACJ,WACA,YACA,KAAK,OAAO,gBAAgB,iBAAiB,SAAS,CAAC;AAKzD,UAAM,gBAAgB,cAAc,MAAM,kBAAkB,MAAM,aAAa,KAAK;AACpF,UAAM,iBAAiB;AAKvB,UAAM,qBAAqB,eAAe;AAC1C,UAAM,mBAAmB,qBAAqB;AAC9C,UAAM,QAAQ,mBAAmB,GAAG,qBAAqB,KAAK,OAAO;AACrE,QAAI,KAAK,WAAW,KAAK,WAAW;AAClC,aAAO;QACL,EAAC,WAAW,eAAe,MAAK;QAChC,EAAC,WAAW,gBAAgB,MAAK;;IAErC;AAEA,WAAO;MACL,EAAC,WAAW,gBAAgB,MAAK;MACjC,EAAC,WAAW,eAAe,MAAK;;EAEpC;EAEA,+BAA4B;AAC1B,WAAO,KAAK,YAAa,sBAAqB;EAChD;;AArV2B,WAAA;EAA1B,SAAS,EAAC,MAAM,QAAO,CAAC;;AACE,WAAA;EAA1B,SAAS,EAAC,MAAM,QAAO,CAAC;;AACE,WAAA;EAA1B,SAAS,EAAC,MAAM,QAAO,CAAC;;AACb,WAAA;EAAX,SAAQ;;AAC4C,WAAA;EAApD,SAAS,EAAC,MAAM,SAAS,WAAW,cAAa,CAAC;;AACxB,WAAA;EAA1B,SAAS,EAAC,MAAM,QAAO,CAAC;;AACE,WAAA;EAA1B,SAAS,EAAC,MAAM,QAAO,CAAC;;AACE,WAAA;EAA1B,SAAS,EAAC,MAAM,QAAO,CAAC;;AACiB,WAAA;EAAzC,SAAS,EAAC,WAAW,kBAAiB,CAAC;;AACH,WAAA;EAApC,SAAS,EAAC,WAAW,aAAY,CAAC;;AACT,WAAA;EAAzB,SAAS,EAAC,MAAM,OAAM,CAAC;;AACE,WAAA;EAAzB,SAAS,EAAC,MAAM,OAAM,CAAC;;AAK2B,WAAA;EAAlD,SAAS,EAAC,MAAM,SAAS,WAAW,YAAW,CAAC;;AAKA,WAAA;EAAhD,SAAS,EAAC,MAAM,SAAS,WAAW,UAAS,CAAC;;AAG9B,WAAA;EADhB,sBAAsB,EAAC,MAAM,mBAAkB,CAAC;;AAoBhC,WAAA;EAAhB,MAAK;;AAOW,WAAA;EAAhB,MAAK;;AACW,WAAA;EAAhB,MAAK;;AAEW,WAAA;EADhB,MAAM,iBAAiB;;AAEkB,WAAA;EAAzC,MAAM,gBAAgB;;AACe,WAAA;EAArC,MAAM,YAAY;;;;ACjCd,IAAM,mBAAmB,OAAO,kBAAkB;AAGzD,IAAM,8BAA8B,OAAO,6BAA6B;AACxE,IAAM,4BAA4B,OAAO,2BAA2B;AACpE,IAAM,iCAAiC,OAAO,gCAAgC;AAC9E,IAAM,8BAA8B,OAAO,6BAA6B;AAkClE,SAAU,sBAEd,MAAO;;EACP,MAAe,gCACL,KAAI;;;IAuBZ,eAAe,MAAW;AACxB,YAAM,GAAG,IAAI;AAlBf,WAAA,EAAA,IAAgC,IAAI,gBAAe;AAMnD,WAAA,EAAA,IAA8B;AAO9B,WAAA,EAAA,IAAmC;AAMjC,UAAI,UAAU;AACZ;MACF;AAEA,WAAK,iBACH,WACA,CAAC,iBAAgB;AAMf,YAAI,KAAK,yBAAyB,KAAK,CAAC,aAAa,WAAW;AAC9D;QACF;AAEA,aAAK,iBACH,WACA,MAAK;AAIH,eAAK,2BAA2B,EAAE,YAAY;QAChD,GACA,EAAC,MAAM,KAAI,CAAC;MAEhB,GACA;;;;;QAKE,SAAS;OACV;IAEL;IAES,gBAAa;AACpB,WAAK,yBAAyB,IAAI;AAClC,YAAM,QAAQ,MAAM,cAAa;AACjC,WAAK,yBAAyB,IAAI;AAClC,aAAO;IACT;IAES,iBAAc;AACrB,WAAK,8BAA8B,IAAI;AACvC,YAAM,QAAQ,MAAM,eAAc;AAElC,UAAI,OAAO;AACT,aAAK,2BAA2B,EAAE,IAAI;MACxC;AAEA,WAAK,8BAA8B,IAAI;AACvC,aAAO;IACT;IAEA,EAAA,KA3EC,6BAA2B,KAM3B,2BAAyB,KAOzB,gCA8DA,4BAA2B,EAAE,cAA0B;AAKtD,YAAM,cAAc,cAAc;AAClC,UAAI,aAAa;AACf;MACF;AAEA,WAAK,gBAAgB,EAAE,YAAY;AAKnC,YAAM,8BACJ,CAAC,eAAe,cAAc;AAChC,UAAI,CAAC,6BAA6B;AAChC;MACF;AAMA,UACE,KAAK,8BAA8B,KACnC,4BAA4B,KAAK,SAAS,EAAE,MAAM,IAAI,GACtD;AACA,aAAK,MAAK;MACZ;IACF;IAEA,CAAC,gBAAgB,EAAE,cAA0B;AAC3C,YAAM,IAAI,MAAM,8BAA8B;IAChD;IAES,uBAAuB,MAA4B;AAE1D,UAAI,MAAM,wBAAwB;AAChC,cAAM,uBAAuB,IAAI;MACnC;AAGA,WAAK,2BAA2B,EAAE,MAAK;AACvC,UAAI,CAAC,MAAM;AACT;MACF;AAEA,WAAK,2BAA2B,IAAI,IAAI,gBAAe;AAOvD,iCACE,MACA,MACA,MAAK;AACH,aAAK,2BAA2B,EAAE,IAAI;MACxC,GACA,KAAK,2BAA2B,EAAE,MAAM;IAE5C;;AAGF,SAAO;AACT;AAYA,SAAS,2BACP,SACA,MACA,gBACA,SAAoB;AAEpB,QAAM,gBAAgB,qBAAqB,IAAI;AAK/C,MAAI,sBAAsB;AAC1B,MAAI;AACJ,MAAI,uBAAuB;AAC3B,gBAAc,iBACZ,UACA,MAAK;AACH,2BAAuB;AACvB,6BAAyB,IAAI,gBAAe;AAC5C,0BAAsB;AACtB,YAAQ,iBACN,WACA,MAAK;AACH,4BAAsB;IACxB,GACA;MACE,QAAQ,uBAAuB;KAChC;EAEL,GACA,EAAC,QAAQ,QAAO,CAAC;AAGnB,gBAAc,iBACZ,SACA,MAAK;AACH,2BAAuB;AACvB,4BAAwB,MAAK;AAC7B,QAAI,qBAAqB;AACvB;IACF;AAEA,mBAAc;EAChB,GACA,EAAC,QAAQ,QAAO,CAAC;AAOnB,OAAK,iBACH,UACA,MAAK;AAGH,QAAI,sBAAsB;AACxB;IACF;AAEA,mBAAc;EAChB,GACA;IACE,QAAQ;GACT;AAaL;AAEA,IAAM,sBAAsB,oBAAI,QAAO;AAYvC,SAAS,qBAAqB,MAAqB;AACjD,MAAI,CAAC,oBAAoB,IAAI,IAAI,GAAG;AAYlC,UAAM,QAAQ,IAAI,YAAW;AAC7B,wBAAoB,IAAI,MAAM,KAAK;AAKnC,eAAW,cAAc,CAAC,kBAAkB,eAAe,GAAY;AACrE,YAAM,cAAc,KAAK,UAAU;AACnC,WAAK,UAAU,IAAI,WAAA;AACjB,cAAM,cAAc,IAAI,MAAM,QAAQ,CAAC;AACvC,cAAM,SAAS,QAAQ,MAAM,aAAa,MAAM,SAAS;AACzD,cAAM,cAAc,IAAI,MAAM,OAAO,CAAC;AACtC,eAAO;MACT;IACF;EACF;AAEA,SAAO,oBAAoB,IAAI,IAAI;AACrC;AAWA,SAAS,4BACP,MACA,SAAoB;AAEpB,MAAI,CAAC,MAAM;AACT,WAAO;EACT;AAEA,MAAI;AACJ,aAAW,WAAW,KAAK,UAAU;AACnC,QAAI,QAAQ,QAAQ,UAAU,GAAG;AAC/B,4BAAsB;AACtB;IACF;EACF;AAEA,SAAO,wBAAwB;AACjC;", "names": []}