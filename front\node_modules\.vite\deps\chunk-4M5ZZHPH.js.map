{"version": 3, "sources": ["../../@material/web/tabs/internal/tab.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport '../../elevation/elevation.js';\nimport '../../focus/md-focus-ring.js';\nimport '../../ripple/ripple.js';\n\nimport {html, isServer, LitElement, nothing} from 'lit';\nimport {\n  property,\n  query,\n  queryAssignedElements,\n  queryAssignedNodes,\n  state,\n} from 'lit/decorators.js';\nimport {ClassInfo, classMap} from 'lit/directives/class-map.js';\n\nimport {EASING} from '../../internal/motion/animation.js';\nimport {mixinFocusable} from '../../labs/behaviors/focusable.js';\n\n/**\n * Symbol for tabs to use to animate their indicators based off another tab's\n * indicator.\n */\nconst INDICATOR = Symbol('indicator');\n\n/**\n * Symbol used by the tab bar to request a tab to animate its indicator from a\n * previously selected tab.\n */\nexport const ANIMATE_INDICATOR = Symbol('animateIndicator');\n\n// Separate variable needed for closure.\nconst tabBaseClass = mixinFocusable(LitElement);\n\n/**\n * Tab component.\n */\nexport class Tab extends tabBaseClass {\n  /**\n   * The attribute `md-tab` indicates that the element is a tab for the parent\n   * element, `<md-tabs>`. Make sure if you're implementing your own `md-tab`\n   * component that you have an `md-tab` attribute set.\n   */\n  @property({type: Boolean, reflect: true, attribute: 'md-tab'})\n  readonly isTab = true;\n\n  /**\n   * Whether or not the tab is selected.\n   **/\n  @property({type: Boolean, reflect: true}) active = false;\n\n  /**\n   * @deprecated use `active`\n   */\n  @property({type: Boolean})\n  get selected() {\n    return this.active;\n  }\n  set selected(active: boolean) {\n    this.active = active;\n  }\n\n  /**\n   * In SSR, set this to true when an icon is present.\n   */\n  @property({type: Boolean, attribute: 'has-icon'}) hasIcon = false;\n\n  /**\n   * In SSR, set this to true when there is no label and only an icon.\n   */\n  @property({type: Boolean, attribute: 'icon-only'}) iconOnly = false;\n\n  @query('.indicator') readonly [INDICATOR]!: HTMLElement | null;\n  @state() protected fullWidthIndicator = false;\n  @queryAssignedNodes({flatten: true})\n  private readonly assignedDefaultNodes!: Node[];\n  @queryAssignedElements({slot: 'icon', flatten: true})\n  private readonly assignedIcons!: HTMLElement[];\n  private readonly internals =\n    // Cast needed for closure\n    (this as HTMLElement).attachInternals();\n\n  constructor() {\n    super();\n    if (!isServer) {\n      this.internals.role = 'tab';\n      this.addEventListener('keydown', this.handleKeydown.bind(this));\n    }\n  }\n\n  protected override render() {\n    const indicator = html`<div class=\"indicator\"></div>`;\n    return html`<div\n      class=\"button\"\n      role=\"presentation\"\n      @click=${this.handleContentClick}>\n      <md-focus-ring part=\"focus-ring\" inward .control=${this}></md-focus-ring>\n      <md-elevation part=\"elevation\"></md-elevation>\n      <md-ripple .control=${this}></md-ripple>\n      <div\n        class=\"content ${classMap(this.getContentClasses())}\"\n        role=\"presentation\">\n        <slot name=\"icon\" @slotchange=${this.handleIconSlotChange}></slot>\n        <slot @slotchange=${this.handleSlotChange}></slot>\n        ${this.fullWidthIndicator ? nothing : indicator}\n      </div>\n      ${this.fullWidthIndicator ? indicator : nothing}\n    </div>`;\n  }\n\n  protected getContentClasses(): ClassInfo {\n    return {\n      'has-icon': this.hasIcon,\n      'has-label': !this.iconOnly,\n    };\n  }\n\n  protected override updated() {\n    this.internals.ariaSelected = String(this.active);\n  }\n\n  private async handleKeydown(event: KeyboardEvent) {\n    // Allow event to bubble.\n    await 0;\n    if (event.defaultPrevented) {\n      return;\n    }\n\n    if (event.key === 'Enter' || event.key === ' ') {\n      // Prevent default behavior such as scrolling when pressing spacebar.\n      event.preventDefault();\n      this.click();\n    }\n  }\n\n  private handleContentClick(event: Event) {\n    // Ensure the \"click\" target is always the tab, and not content, by stopping\n    // propagation of content clicks and re-clicking the host.\n    event.stopPropagation();\n    this.click();\n  }\n\n  [ANIMATE_INDICATOR](previousTab: Tab) {\n    if (!this[INDICATOR]) {\n      return;\n    }\n\n    this[INDICATOR].getAnimations().forEach((a) => {\n      a.cancel();\n    });\n    const frames = this.getKeyframes(previousTab);\n    if (frames !== null) {\n      this[INDICATOR].animate(frames, {\n        duration: 250,\n        easing: EASING.EMPHASIZED,\n      });\n    }\n  }\n\n  private getKeyframes(previousTab: Tab) {\n    const reduceMotion = shouldReduceMotion();\n    if (!this.active) {\n      return reduceMotion ? [{'opacity': 1}, {'transform': 'none'}] : null;\n    }\n\n    const from: Keyframe = {};\n    const fromRect =\n      previousTab[INDICATOR]?.getBoundingClientRect() ?? ({} as DOMRect);\n    const fromPos = fromRect.left;\n    const fromExtent = fromRect.width;\n    const toRect = this[INDICATOR]!.getBoundingClientRect();\n    const toPos = toRect.left;\n    const toExtent = toRect.width;\n    const scale = fromExtent / toExtent;\n    if (\n      !reduceMotion &&\n      fromPos !== undefined &&\n      toPos !== undefined &&\n      !isNaN(scale)\n    ) {\n      from['transform'] = `translateX(${(fromPos - toPos).toFixed(\n        4,\n      )}px) scaleX(${scale.toFixed(4)})`;\n    } else {\n      from['opacity'] = 0;\n    }\n    // note, including `transform: none` avoids quirky Safari behavior\n    // that can hide the animation.\n    return [from, {'transform': 'none'}];\n  }\n\n  private handleSlotChange() {\n    this.iconOnly = false;\n    // Check if there's any label text or elements. If not, then there is only\n    // an icon.\n    for (const node of this.assignedDefaultNodes) {\n      const hasTextContent =\n        node.nodeType === Node.TEXT_NODE &&\n        !!(node as Text).wholeText.match(/\\S/);\n      if (node.nodeType === Node.ELEMENT_NODE || hasTextContent) {\n        return;\n      }\n    }\n\n    this.iconOnly = true;\n  }\n\n  private handleIconSlotChange() {\n    this.hasIcon = this.assignedIcons.length > 0;\n  }\n}\n\nfunction shouldReduceMotion() {\n  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BA,IAAM,YAAY,OAAO,WAAW;AAM7B,IAAM,oBAAoB,OAAO,kBAAkB;AAG1D,IAAM,eAAe,eAAe,UAAU;AAKxC,IAAO,MAAP,cAAmB,aAAY;;;;EAkBnC,IAAI,WAAQ;AACV,WAAO,KAAK;EACd;EACA,IAAI,SAAS,QAAe;AAC1B,SAAK,SAAS;EAChB;EAsBA,cAAA;AACE,UAAK;AAvCE,SAAA,QAAQ;AAKyB,SAAA,SAAS;AAgBD,SAAA,UAAU;AAKT,SAAA,WAAW;AAG3C,SAAA,qBAAqB;AAKvB,SAAA;IAEd,KAAqB,gBAAe;AAIrC,QAAI,CAAC,UAAU;AACb,WAAK,UAAU,OAAO;AACtB,WAAK,iBAAiB,WAAW,KAAK,cAAc,KAAK,IAAI,CAAC;IAChE;EACF;EAEmB,SAAM;AACvB,UAAM,YAAY;AAClB,WAAO;;;eAGI,KAAK,kBAAkB;yDACmB,IAAI;;4BAEjC,IAAI;;yBAEP,SAAS,KAAK,kBAAiB,CAAE,CAAC;;wCAEnB,KAAK,oBAAoB;4BACrC,KAAK,gBAAgB;UACvC,KAAK,qBAAqB,UAAU,SAAS;;QAE/C,KAAK,qBAAqB,YAAY,OAAO;;EAEnD;EAEU,oBAAiB;AACzB,WAAO;MACL,YAAY,KAAK;MACjB,aAAa,CAAC,KAAK;;EAEvB;EAEmB,UAAO;AACxB,SAAK,UAAU,eAAe,OAAO,KAAK,MAAM;EAClD;EAEQ,MAAM,cAAc,OAAoB;AAE9C,UAAM;AACN,QAAI,MAAM,kBAAkB;AAC1B;IACF;AAEA,QAAI,MAAM,QAAQ,WAAW,MAAM,QAAQ,KAAK;AAE9C,YAAM,eAAc;AACpB,WAAK,MAAK;IACZ;EACF;EAEQ,mBAAmB,OAAY;AAGrC,UAAM,gBAAe;AACrB,SAAK,MAAK;EACZ;EAEA,EAAA,KAtE+B,WAsE9B,kBAAiB,EAAE,aAAgB;AAClC,QAAI,CAAC,KAAK,SAAS,GAAG;AACpB;IACF;AAEA,SAAK,SAAS,EAAE,cAAa,EAAG,QAAQ,CAAC,MAAK;AAC5C,QAAE,OAAM;IACV,CAAC;AACD,UAAM,SAAS,KAAK,aAAa,WAAW;AAC5C,QAAI,WAAW,MAAM;AACnB,WAAK,SAAS,EAAE,QAAQ,QAAQ;QAC9B,UAAU;QACV,QAAQ,OAAO;OAChB;IACH;EACF;EAEQ,aAAa,aAAgB;AACnC,UAAM,eAAe,mBAAkB;AACvC,QAAI,CAAC,KAAK,QAAQ;AAChB,aAAO,eAAe,CAAC,EAAC,WAAW,EAAC,GAAG,EAAC,aAAa,OAAM,CAAC,IAAI;IAClE;AAEA,UAAM,OAAiB,CAAA;AACvB,UAAM,WACJ,YAAY,SAAS,GAAG,sBAAqB,KAAO,CAAA;AACtD,UAAM,UAAU,SAAS;AACzB,UAAM,aAAa,SAAS;AAC5B,UAAM,SAAS,KAAK,SAAS,EAAG,sBAAqB;AACrD,UAAM,QAAQ,OAAO;AACrB,UAAM,WAAW,OAAO;AACxB,UAAM,QAAQ,aAAa;AAC3B,QACE,CAAC,gBACD,YAAY,UACZ,UAAU,UACV,CAAC,MAAM,KAAK,GACZ;AACA,WAAK,WAAW,IAAI,eAAe,UAAU,OAAO,QAClD,CAAC,CACF,cAAc,MAAM,QAAQ,CAAC,CAAC;IACjC,OAAO;AACL,WAAK,SAAS,IAAI;IACpB;AAGA,WAAO,CAAC,MAAM,EAAC,aAAa,OAAM,CAAC;EACrC;EAEQ,mBAAgB;AACtB,SAAK,WAAW;AAGhB,eAAW,QAAQ,KAAK,sBAAsB;AAC5C,YAAM,iBACJ,KAAK,aAAa,KAAK,aACvB,CAAC,CAAE,KAAc,UAAU,MAAM,IAAI;AACvC,UAAI,KAAK,aAAa,KAAK,gBAAgB,gBAAgB;AACzD;MACF;IACF;AAEA,SAAK,WAAW;EAClB;EAEQ,uBAAoB;AAC1B,SAAK,UAAU,KAAK,cAAc,SAAS;EAC7C;;AArKS,WAAA;EADR,SAAS,EAAC,MAAM,SAAS,SAAS,MAAM,WAAW,SAAQ,CAAC;;AAMnB,WAAA;EAAzC,SAAS,EAAC,MAAM,SAAS,SAAS,KAAI,CAAC;;AAMxC,WAAA;EADC,SAAS,EAAC,MAAM,QAAO,CAAC;;AAWyB,WAAA;EAAjD,SAAS,EAAC,MAAM,SAAS,WAAW,WAAU,CAAC;;AAKG,WAAA;EAAlD,SAAS,EAAC,MAAM,SAAS,WAAW,YAAW,CAAC;;AAEnB,WAAA;EAA7B,MAAM,YAAY;;AACA,WAAA;EAAlB,MAAK;;AAEW,WAAA;EADhB,mBAAmB,EAAC,SAAS,KAAI,CAAC;;AAGlB,WAAA;EADhB,sBAAsB,EAAC,MAAM,QAAQ,SAAS,KAAI,CAAC;;AAwItD,SAAS,qBAAkB;AACzB,SAAO,OAAO,WAAW,kCAAkC,EAAE;AAC/D;", "names": []}