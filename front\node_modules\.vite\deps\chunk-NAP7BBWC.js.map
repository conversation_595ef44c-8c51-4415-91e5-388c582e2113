{"version": 3, "sources": ["../../@material/web/ripple/internal/ripple.ts", "../../@material/web/ripple/internal/ripple-styles.ts", "../../@material/web/ripple/ripple.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2022 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {html, isServer, LitElement, PropertyValues} from 'lit';\nimport {property, query, state} from 'lit/decorators.js';\nimport {classMap} from 'lit/directives/class-map.js';\n\nimport {\n  Attachable,\n  AttachableController,\n} from '../../internal/controller/attachable-controller.js';\nimport {EASING} from '../../internal/motion/animation.js';\n\nconst PRESS_GROW_MS = 450;\nconst MINIMUM_PRESS_MS = 225;\nconst INITIAL_ORIGIN_SCALE = 0.2;\nconst PADDING = 10;\nconst SOFT_EDGE_MINIMUM_SIZE = 75;\nconst SOFT_EDGE_CONTAINER_RATIO = 0.35;\nconst PRESS_PSEUDO = '::after';\nconst ANIMATION_FILL = 'forwards';\n\n/**\n * Interaction states for the ripple.\n *\n * On Touch:\n *  - `INACTIVE -> TOUCH_DELAY -> WAITING_FOR_CLICK -> INACTIVE`\n *  - `INACTIVE -> TOUCH_DELAY -> HOLDING -> WAITING_FOR_CLICK -> INACTIVE`\n *\n * On Mouse or Pen:\n *   - `INACTIVE -> WAITING_FOR_CLICK -> INACTIVE`\n */\nenum State {\n  /**\n   * Initial state of the control, no touch in progress.\n   *\n   * Transitions:\n   *   - on touch down: transition to `TOUCH_DELAY`.\n   *   - on mouse down: transition to `WAITING_FOR_CLICK`.\n   */\n  INACTIVE,\n  /**\n   * Touch down has been received, waiting to determine if it's a swipe or\n   * scroll.\n   *\n   * Transitions:\n   *   - on touch up: begin press; transition to `WAITING_FOR_CLICK`.\n   *   - on cancel: transition to `INACTIVE`.\n   *   - after `TOUCH_DELAY_MS`: begin press; transition to `HOLDING`.\n   */\n  TOUCH_DELAY,\n  /**\n   * A touch has been deemed to be a press\n   *\n   * Transitions:\n   *  - on up: transition to `WAITING_FOR_CLICK`.\n   */\n  HOLDING,\n  /**\n   * The user touch has finished, transition into rest state.\n   *\n   * Transitions:\n   *   - on click end press; transition to `INACTIVE`.\n   */\n  WAITING_FOR_CLICK,\n}\n\n/**\n * Events that the ripple listens to.\n */\nconst EVENTS = [\n  'click',\n  'contextmenu',\n  'pointercancel',\n  'pointerdown',\n  'pointerenter',\n  'pointerleave',\n  'pointerup',\n];\n\n/**\n * Delay reacting to touch so that we do not show the ripple for a swipe or\n * scroll interaction.\n */\nconst TOUCH_DELAY_MS = 150;\n\n/**\n * Used to detect if HCM is active. Events do not process during HCM when the\n * ripple is not displayed.\n */\nconst FORCED_COLORS = isServer\n  ? null\n  : window.matchMedia('(forced-colors: active)');\n\n/**\n * A ripple component.\n */\nexport class Ripple extends LitElement implements Attachable {\n  /**\n   * Disables the ripple.\n   */\n  @property({type: Boolean, reflect: true}) disabled = false;\n\n  get htmlFor() {\n    return this.attachableController.htmlFor;\n  }\n\n  set htmlFor(htmlFor: string | null) {\n    this.attachableController.htmlFor = htmlFor;\n  }\n\n  get control() {\n    return this.attachableController.control;\n  }\n  set control(control: HTMLElement | null) {\n    this.attachableController.control = control;\n  }\n\n  @state() private hovered = false;\n  @state() private pressed = false;\n\n  @query('.surface') private readonly mdRoot!: HTMLElement | null;\n  private rippleSize = '';\n  private rippleScale = '';\n  private initialSize = 0;\n  private growAnimation?: Animation;\n  private state = State.INACTIVE;\n  private rippleStartEvent?: PointerEvent;\n  private checkBoundsAfterContextMenu = false;\n  private readonly attachableController = new AttachableController(\n    this,\n    this.onControlChange.bind(this),\n  );\n\n  attach(control: HTMLElement) {\n    this.attachableController.attach(control);\n  }\n\n  detach() {\n    this.attachableController.detach();\n  }\n\n  override connectedCallback() {\n    super.connectedCallback();\n    // Needed for VoiceOver, which will create a \"group\" if the element is a\n    // sibling to other content.\n    this.setAttribute('aria-hidden', 'true');\n  }\n\n  protected override render() {\n    const classes = {\n      'hovered': this.hovered,\n      'pressed': this.pressed,\n    };\n\n    return html`<div class=\"surface ${classMap(classes)}\"></div>`;\n  }\n\n  protected override update(changedProps: PropertyValues<Ripple>) {\n    if (changedProps.has('disabled') && this.disabled) {\n      this.hovered = false;\n      this.pressed = false;\n    }\n    super.update(changedProps);\n  }\n\n  /**\n   * TODO(b/269799771): make private\n   * @private only public for slider\n   */\n  handlePointerenter(event: PointerEvent) {\n    if (!this.shouldReactToEvent(event)) {\n      return;\n    }\n\n    this.hovered = true;\n  }\n\n  /**\n   * TODO(b/269799771): make private\n   * @private only public for slider\n   */\n  handlePointerleave(event: PointerEvent) {\n    if (!this.shouldReactToEvent(event)) {\n      return;\n    }\n\n    this.hovered = false;\n\n    // release a held mouse or pen press that moves outside the element\n    if (this.state !== State.INACTIVE) {\n      this.endPressAnimation();\n    }\n  }\n\n  private handlePointerup(event: PointerEvent) {\n    if (!this.shouldReactToEvent(event)) {\n      return;\n    }\n\n    if (this.state === State.HOLDING) {\n      this.state = State.WAITING_FOR_CLICK;\n      return;\n    }\n\n    if (this.state === State.TOUCH_DELAY) {\n      this.state = State.WAITING_FOR_CLICK;\n      this.startPressAnimation(this.rippleStartEvent);\n      return;\n    }\n  }\n\n  private async handlePointerdown(event: PointerEvent) {\n    if (!this.shouldReactToEvent(event)) {\n      return;\n    }\n\n    this.rippleStartEvent = event;\n    if (!this.isTouch(event)) {\n      this.state = State.WAITING_FOR_CLICK;\n      this.startPressAnimation(event);\n      return;\n    }\n\n    // after a longpress contextmenu event, an extra `pointerdown` can be\n    // dispatched to the pressed element. Check that the down is within\n    // bounds of the element in this case.\n    if (this.checkBoundsAfterContextMenu && !this.inBounds(event)) {\n      return;\n    }\n\n    this.checkBoundsAfterContextMenu = false;\n\n    // Wait for a hold after touch delay\n    this.state = State.TOUCH_DELAY;\n    await new Promise((resolve) => {\n      setTimeout(resolve, TOUCH_DELAY_MS);\n    });\n\n    if (this.state !== State.TOUCH_DELAY) {\n      return;\n    }\n\n    this.state = State.HOLDING;\n    this.startPressAnimation(event);\n  }\n\n  private handleClick() {\n    // Click is a MouseEvent in Firefox and Safari, so we cannot use\n    // `shouldReactToEvent`\n    if (this.disabled) {\n      return;\n    }\n\n    if (this.state === State.WAITING_FOR_CLICK) {\n      this.endPressAnimation();\n      return;\n    }\n\n    if (this.state === State.INACTIVE) {\n      // keyboard synthesized click event\n      this.startPressAnimation();\n      this.endPressAnimation();\n    }\n  }\n\n  private handlePointercancel(event: PointerEvent) {\n    if (!this.shouldReactToEvent(event)) {\n      return;\n    }\n\n    this.endPressAnimation();\n  }\n\n  private handleContextmenu() {\n    if (this.disabled) {\n      return;\n    }\n\n    this.checkBoundsAfterContextMenu = true;\n    this.endPressAnimation();\n  }\n\n  private determineRippleSize() {\n    const {height, width} = this.getBoundingClientRect();\n    const maxDim = Math.max(height, width);\n    const softEdgeSize = Math.max(\n      SOFT_EDGE_CONTAINER_RATIO * maxDim,\n      SOFT_EDGE_MINIMUM_SIZE,\n    );\n\n    const initialSize = Math.floor(maxDim * INITIAL_ORIGIN_SCALE);\n    const hypotenuse = Math.sqrt(width ** 2 + height ** 2);\n    const maxRadius = hypotenuse + PADDING;\n\n    this.initialSize = initialSize;\n    this.rippleScale = `${(maxRadius + softEdgeSize) / initialSize}`;\n    this.rippleSize = `${initialSize}px`;\n  }\n\n  private getNormalizedPointerEventCoords(pointerEvent: PointerEvent): {\n    x: number;\n    y: number;\n  } {\n    const {scrollX, scrollY} = window;\n    const {left, top} = this.getBoundingClientRect();\n    const documentX = scrollX + left;\n    const documentY = scrollY + top;\n    const {pageX, pageY} = pointerEvent;\n    return {x: pageX - documentX, y: pageY - documentY};\n  }\n\n  private getTranslationCoordinates(positionEvent?: Event) {\n    const {height, width} = this.getBoundingClientRect();\n    // end in the center\n    const endPoint = {\n      x: (width - this.initialSize) / 2,\n      y: (height - this.initialSize) / 2,\n    };\n\n    let startPoint;\n    if (positionEvent instanceof PointerEvent) {\n      startPoint = this.getNormalizedPointerEventCoords(positionEvent);\n    } else {\n      startPoint = {\n        x: width / 2,\n        y: height / 2,\n      };\n    }\n\n    // center around start point\n    startPoint = {\n      x: startPoint.x - this.initialSize / 2,\n      y: startPoint.y - this.initialSize / 2,\n    };\n\n    return {startPoint, endPoint};\n  }\n\n  private startPressAnimation(positionEvent?: Event) {\n    if (!this.mdRoot) {\n      return;\n    }\n\n    this.pressed = true;\n    this.growAnimation?.cancel();\n    this.determineRippleSize();\n    const {startPoint, endPoint} =\n      this.getTranslationCoordinates(positionEvent);\n    const translateStart = `${startPoint.x}px, ${startPoint.y}px`;\n    const translateEnd = `${endPoint.x}px, ${endPoint.y}px`;\n\n    this.growAnimation = this.mdRoot.animate(\n      {\n        top: [0, 0],\n        left: [0, 0],\n        height: [this.rippleSize, this.rippleSize],\n        width: [this.rippleSize, this.rippleSize],\n        transform: [\n          `translate(${translateStart}) scale(1)`,\n          `translate(${translateEnd}) scale(${this.rippleScale})`,\n        ],\n      },\n      {\n        pseudoElement: PRESS_PSEUDO,\n        duration: PRESS_GROW_MS,\n        easing: EASING.STANDARD,\n        fill: ANIMATION_FILL,\n      },\n    );\n  }\n\n  private async endPressAnimation() {\n    this.rippleStartEvent = undefined;\n    this.state = State.INACTIVE;\n    const animation = this.growAnimation;\n    let pressAnimationPlayState = Infinity;\n    if (typeof animation?.currentTime === 'number') {\n      pressAnimationPlayState = animation.currentTime;\n    } else if (animation?.currentTime) {\n      pressAnimationPlayState = animation.currentTime.to('ms').value;\n    }\n\n    if (pressAnimationPlayState >= MINIMUM_PRESS_MS) {\n      this.pressed = false;\n      return;\n    }\n\n    await new Promise((resolve) => {\n      setTimeout(resolve, MINIMUM_PRESS_MS - pressAnimationPlayState);\n    });\n\n    if (this.growAnimation !== animation) {\n      // A new press animation was started. The old animation was canceled and\n      // should not finish the pressed state.\n      return;\n    }\n\n    this.pressed = false;\n  }\n\n  /**\n   * Returns `true` if\n   *  - the ripple element is enabled\n   *  - the pointer is primary for the input type\n   *  - the pointer is the pointer that started the interaction, or will start\n   * the interaction\n   *  - the pointer is a touch, or the pointer state has the primary button\n   * held, or the pointer is hovering\n   */\n  private shouldReactToEvent(event: PointerEvent) {\n    if (this.disabled || !event.isPrimary) {\n      return false;\n    }\n\n    if (\n      this.rippleStartEvent &&\n      this.rippleStartEvent.pointerId !== event.pointerId\n    ) {\n      return false;\n    }\n\n    if (event.type === 'pointerenter' || event.type === 'pointerleave') {\n      return !this.isTouch(event);\n    }\n\n    const isPrimaryButton = event.buttons === 1;\n    return this.isTouch(event) || isPrimaryButton;\n  }\n\n  /**\n   * Check if the event is within the bounds of the element.\n   *\n   * This is only needed for the \"stuck\" contextmenu longpress on Chrome.\n   */\n  private inBounds({x, y}: PointerEvent) {\n    const {top, left, bottom, right} = this.getBoundingClientRect();\n    return x >= left && x <= right && y >= top && y <= bottom;\n  }\n\n  private isTouch({pointerType}: PointerEvent) {\n    return pointerType === 'touch';\n  }\n\n  /** @private */\n  async handleEvent(event: Event) {\n    if (FORCED_COLORS?.matches) {\n      // Skip event logic since the ripple is `display: none`.\n      return;\n    }\n\n    switch (event.type) {\n      case 'click':\n        this.handleClick();\n        break;\n      case 'contextmenu':\n        this.handleContextmenu();\n        break;\n      case 'pointercancel':\n        this.handlePointercancel(event as PointerEvent);\n        break;\n      case 'pointerdown':\n        await this.handlePointerdown(event as PointerEvent);\n        break;\n      case 'pointerenter':\n        this.handlePointerenter(event as PointerEvent);\n        break;\n      case 'pointerleave':\n        this.handlePointerleave(event as PointerEvent);\n        break;\n      case 'pointerup':\n        this.handlePointerup(event as PointerEvent);\n        break;\n      default:\n        break;\n    }\n  }\n\n  private onControlChange(prev: HTMLElement | null, next: HTMLElement | null) {\n    if (isServer) return;\n\n    for (const event of EVENTS) {\n      prev?.removeEventListener(event, this);\n      next?.addEventListener(event, this);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n// Generated stylesheet for ./ripple/internal/ripple-styles.css.\nimport {css} from 'lit';\nexport const styles = css`:host{display:flex;margin:auto;pointer-events:none}:host([disabled]){display:none}@media(forced-colors: active){:host{display:none}}:host,.surface{border-radius:inherit;position:absolute;inset:0;overflow:hidden}.surface{-webkit-tap-highlight-color:rgba(0,0,0,0)}.surface::before,.surface::after{content:\"\";opacity:0;position:absolute}.surface::before{background-color:var(--md-ripple-hover-color, var(--md-sys-color-on-surface, #1d1b20));inset:0;transition:opacity 15ms linear,background-color 15ms linear}.surface::after{background:radial-gradient(closest-side, var(--md-ripple-pressed-color, var(--md-sys-color-on-surface, #1d1b20)) max(100% - 70px, 65%), transparent 100%);transform-origin:center center;transition:opacity 375ms linear}.hovered::before{background-color:var(--md-ripple-hover-color, var(--md-sys-color-on-surface, #1d1b20));opacity:var(--md-ripple-hover-opacity, 0.08)}.pressed::after{opacity:var(--md-ripple-pressed-opacity, 0.12);transition-duration:105ms}\n`;\n", "/**\n * @license\n * Copyright 2022 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {Ripple} from './internal/ripple.js';\nimport {styles} from './internal/ripple-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-ripple': MdRipple;\n  }\n}\n\n/**\n * @summary Ripples, also known as state layers, are visual indicators used to\n * communicate the status of a component or interactive element.\n *\n * @description A state layer is a semi-transparent covering on an element that\n * indicates its state. State layers provide a systematic approach to\n * visualizing states by using opacity. A layer can be applied to an entire\n * element or in a circular shape and only one state layer can be applied at a\n * given time.\n *\n * @final\n * @suppress {visibility}\n */\n@customElement('md-ripple')\nexport class MdRipple extends Ripple {\n  static override styles: CSSResultOrNative[] = [styles];\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAgBA,IAAM,gBAAgB;AACtB,IAAM,mBAAmB;AACzB,IAAM,uBAAuB;AAC7B,IAAM,UAAU;AAChB,IAAM,yBAAyB;AAC/B,IAAM,4BAA4B;AAClC,IAAM,eAAe;AACrB,IAAM,iBAAiB;AAYvB,IAAK;CAAL,SAAKA,QAAK;AAQR,EAAAA,OAAAA,OAAA,UAAA,IAAA,CAAA,IAAA;AAUA,EAAAA,OAAAA,OAAA,aAAA,IAAA,CAAA,IAAA;AAOA,EAAAA,OAAAA,OAAA,SAAA,IAAA,CAAA,IAAA;AAOA,EAAAA,OAAAA,OAAA,mBAAA,IAAA,CAAA,IAAA;AACF,GAjCK,UAAA,QAAK,CAAA,EAAA;AAsCV,IAAM,SAAS;EACb;EACA;EACA;EACA;EACA;EACA;EACA;;AAOF,IAAM,iBAAiB;AAMvB,IAAM,gBAAgB,WAClB,OACA,OAAO,WAAW,yBAAyB;AAKzC,IAAO,SAAP,cAAsB,WAAU;EAAtC,cAAA;;AAI4C,SAAA,WAAW;AAiBpC,SAAA,UAAU;AACV,SAAA,UAAU;AAGnB,SAAA,aAAa;AACb,SAAA,cAAc;AACd,SAAA,cAAc;AAEd,SAAA,QAAQ,MAAM;AAEd,SAAA,8BAA8B;AACrB,SAAA,uBAAuB,IAAI,qBAC1C,MACA,KAAK,gBAAgB,KAAK,IAAI,CAAC;EAmWnC;EA/XE,IAAI,UAAO;AACT,WAAO,KAAK,qBAAqB;EACnC;EAEA,IAAI,QAAQ,SAAsB;AAChC,SAAK,qBAAqB,UAAU;EACtC;EAEA,IAAI,UAAO;AACT,WAAO,KAAK,qBAAqB;EACnC;EACA,IAAI,QAAQ,SAA2B;AACrC,SAAK,qBAAqB,UAAU;EACtC;EAkBA,OAAO,SAAoB;AACzB,SAAK,qBAAqB,OAAO,OAAO;EAC1C;EAEA,SAAM;AACJ,SAAK,qBAAqB,OAAM;EAClC;EAES,oBAAiB;AACxB,UAAM,kBAAiB;AAGvB,SAAK,aAAa,eAAe,MAAM;EACzC;EAEmB,SAAM;AACvB,UAAM,UAAU;MACd,WAAW,KAAK;MAChB,WAAW,KAAK;;AAGlB,WAAO,2BAA2B,SAAS,OAAO,CAAC;EACrD;EAEmB,OAAO,cAAoC;AAC5D,QAAI,aAAa,IAAI,UAAU,KAAK,KAAK,UAAU;AACjD,WAAK,UAAU;AACf,WAAK,UAAU;IACjB;AACA,UAAM,OAAO,YAAY;EAC3B;;;;;EAMA,mBAAmB,OAAmB;AACpC,QAAI,CAAC,KAAK,mBAAmB,KAAK,GAAG;AACnC;IACF;AAEA,SAAK,UAAU;EACjB;;;;;EAMA,mBAAmB,OAAmB;AACpC,QAAI,CAAC,KAAK,mBAAmB,KAAK,GAAG;AACnC;IACF;AAEA,SAAK,UAAU;AAGf,QAAI,KAAK,UAAU,MAAM,UAAU;AACjC,WAAK,kBAAiB;IACxB;EACF;EAEQ,gBAAgB,OAAmB;AACzC,QAAI,CAAC,KAAK,mBAAmB,KAAK,GAAG;AACnC;IACF;AAEA,QAAI,KAAK,UAAU,MAAM,SAAS;AAChC,WAAK,QAAQ,MAAM;AACnB;IACF;AAEA,QAAI,KAAK,UAAU,MAAM,aAAa;AACpC,WAAK,QAAQ,MAAM;AACnB,WAAK,oBAAoB,KAAK,gBAAgB;AAC9C;IACF;EACF;EAEQ,MAAM,kBAAkB,OAAmB;AACjD,QAAI,CAAC,KAAK,mBAAmB,KAAK,GAAG;AACnC;IACF;AAEA,SAAK,mBAAmB;AACxB,QAAI,CAAC,KAAK,QAAQ,KAAK,GAAG;AACxB,WAAK,QAAQ,MAAM;AACnB,WAAK,oBAAoB,KAAK;AAC9B;IACF;AAKA,QAAI,KAAK,+BAA+B,CAAC,KAAK,SAAS,KAAK,GAAG;AAC7D;IACF;AAEA,SAAK,8BAA8B;AAGnC,SAAK,QAAQ,MAAM;AACnB,UAAM,IAAI,QAAQ,CAAC,YAAW;AAC5B,iBAAW,SAAS,cAAc;IACpC,CAAC;AAED,QAAI,KAAK,UAAU,MAAM,aAAa;AACpC;IACF;AAEA,SAAK,QAAQ,MAAM;AACnB,SAAK,oBAAoB,KAAK;EAChC;EAEQ,cAAW;AAGjB,QAAI,KAAK,UAAU;AACjB;IACF;AAEA,QAAI,KAAK,UAAU,MAAM,mBAAmB;AAC1C,WAAK,kBAAiB;AACtB;IACF;AAEA,QAAI,KAAK,UAAU,MAAM,UAAU;AAEjC,WAAK,oBAAmB;AACxB,WAAK,kBAAiB;IACxB;EACF;EAEQ,oBAAoB,OAAmB;AAC7C,QAAI,CAAC,KAAK,mBAAmB,KAAK,GAAG;AACnC;IACF;AAEA,SAAK,kBAAiB;EACxB;EAEQ,oBAAiB;AACvB,QAAI,KAAK,UAAU;AACjB;IACF;AAEA,SAAK,8BAA8B;AACnC,SAAK,kBAAiB;EACxB;EAEQ,sBAAmB;AACzB,UAAM,EAAC,QAAQ,MAAK,IAAI,KAAK,sBAAqB;AAClD,UAAM,SAAS,KAAK,IAAI,QAAQ,KAAK;AACrC,UAAM,eAAe,KAAK,IACxB,4BAA4B,QAC5B,sBAAsB;AAGxB,UAAM,cAAc,KAAK,MAAM,SAAS,oBAAoB;AAC5D,UAAM,aAAa,KAAK,KAAK,SAAS,IAAI,UAAU,CAAC;AACrD,UAAM,YAAY,aAAa;AAE/B,SAAK,cAAc;AACnB,SAAK,cAAc,IAAI,YAAY,gBAAgB,WAAW;AAC9D,SAAK,aAAa,GAAG,WAAW;EAClC;EAEQ,gCAAgC,cAA0B;AAIhE,UAAM,EAAC,SAAS,QAAO,IAAI;AAC3B,UAAM,EAAC,MAAM,IAAG,IAAI,KAAK,sBAAqB;AAC9C,UAAM,YAAY,UAAU;AAC5B,UAAM,YAAY,UAAU;AAC5B,UAAM,EAAC,OAAO,MAAK,IAAI;AACvB,WAAO,EAAC,GAAG,QAAQ,WAAW,GAAG,QAAQ,UAAS;EACpD;EAEQ,0BAA0B,eAAqB;AACrD,UAAM,EAAC,QAAQ,MAAK,IAAI,KAAK,sBAAqB;AAElD,UAAM,WAAW;MACf,IAAI,QAAQ,KAAK,eAAe;MAChC,IAAI,SAAS,KAAK,eAAe;;AAGnC,QAAI;AACJ,QAAI,yBAAyB,cAAc;AACzC,mBAAa,KAAK,gCAAgC,aAAa;IACjE,OAAO;AACL,mBAAa;QACX,GAAG,QAAQ;QACX,GAAG,SAAS;;IAEhB;AAGA,iBAAa;MACX,GAAG,WAAW,IAAI,KAAK,cAAc;MACrC,GAAG,WAAW,IAAI,KAAK,cAAc;;AAGvC,WAAO,EAAC,YAAY,SAAQ;EAC9B;EAEQ,oBAAoB,eAAqB;AAC/C,QAAI,CAAC,KAAK,QAAQ;AAChB;IACF;AAEA,SAAK,UAAU;AACf,SAAK,eAAe,OAAM;AAC1B,SAAK,oBAAmB;AACxB,UAAM,EAAC,YAAY,SAAQ,IACzB,KAAK,0BAA0B,aAAa;AAC9C,UAAM,iBAAiB,GAAG,WAAW,CAAC,OAAO,WAAW,CAAC;AACzD,UAAM,eAAe,GAAG,SAAS,CAAC,OAAO,SAAS,CAAC;AAEnD,SAAK,gBAAgB,KAAK,OAAO,QAC/B;MACE,KAAK,CAAC,GAAG,CAAC;MACV,MAAM,CAAC,GAAG,CAAC;MACX,QAAQ,CAAC,KAAK,YAAY,KAAK,UAAU;MACzC,OAAO,CAAC,KAAK,YAAY,KAAK,UAAU;MACxC,WAAW;QACT,aAAa,cAAc;QAC3B,aAAa,YAAY,WAAW,KAAK,WAAW;;OAGxD;MACE,eAAe;MACf,UAAU;MACV,QAAQ,OAAO;MACf,MAAM;KACP;EAEL;EAEQ,MAAM,oBAAiB;AAC7B,SAAK,mBAAmB;AACxB,SAAK,QAAQ,MAAM;AACnB,UAAM,YAAY,KAAK;AACvB,QAAI,0BAA0B;AAC9B,QAAI,OAAO,WAAW,gBAAgB,UAAU;AAC9C,gCAA0B,UAAU;IACtC,WAAW,WAAW,aAAa;AACjC,gCAA0B,UAAU,YAAY,GAAG,IAAI,EAAE;IAC3D;AAEA,QAAI,2BAA2B,kBAAkB;AAC/C,WAAK,UAAU;AACf;IACF;AAEA,UAAM,IAAI,QAAQ,CAAC,YAAW;AAC5B,iBAAW,SAAS,mBAAmB,uBAAuB;IAChE,CAAC;AAED,QAAI,KAAK,kBAAkB,WAAW;AAGpC;IACF;AAEA,SAAK,UAAU;EACjB;;;;;;;;;;EAWQ,mBAAmB,OAAmB;AAC5C,QAAI,KAAK,YAAY,CAAC,MAAM,WAAW;AACrC,aAAO;IACT;AAEA,QACE,KAAK,oBACL,KAAK,iBAAiB,cAAc,MAAM,WAC1C;AACA,aAAO;IACT;AAEA,QAAI,MAAM,SAAS,kBAAkB,MAAM,SAAS,gBAAgB;AAClE,aAAO,CAAC,KAAK,QAAQ,KAAK;IAC5B;AAEA,UAAM,kBAAkB,MAAM,YAAY;AAC1C,WAAO,KAAK,QAAQ,KAAK,KAAK;EAChC;;;;;;EAOQ,SAAS,EAAC,GAAG,EAAC,GAAe;AACnC,UAAM,EAAC,KAAK,MAAM,QAAQ,MAAK,IAAI,KAAK,sBAAqB;AAC7D,WAAO,KAAK,QAAQ,KAAK,SAAS,KAAK,OAAO,KAAK;EACrD;EAEQ,QAAQ,EAAC,YAAW,GAAe;AACzC,WAAO,gBAAgB;EACzB;;EAGA,MAAM,YAAY,OAAY;AAC5B,QAAI,eAAe,SAAS;AAE1B;IACF;AAEA,YAAQ,MAAM,MAAM;MAClB,KAAK;AACH,aAAK,YAAW;AAChB;MACF,KAAK;AACH,aAAK,kBAAiB;AACtB;MACF,KAAK;AACH,aAAK,oBAAoB,KAAqB;AAC9C;MACF,KAAK;AACH,cAAM,KAAK,kBAAkB,KAAqB;AAClD;MACF,KAAK;AACH,aAAK,mBAAmB,KAAqB;AAC7C;MACF,KAAK;AACH,aAAK,mBAAmB,KAAqB;AAC7C;MACF,KAAK;AACH,aAAK,gBAAgB,KAAqB;AAC1C;MACF;AACE;IACJ;EACF;EAEQ,gBAAgB,MAA0B,MAAwB;AACxE,QAAI;AAAU;AAEd,eAAW,SAAS,QAAQ;AAC1B,YAAM,oBAAoB,OAAO,IAAI;AACrC,YAAM,iBAAiB,OAAO,IAAI;IACpC;EACF;;AAhY0C,WAAA;EAAzC,SAAS,EAAC,MAAM,SAAS,SAAS,KAAI,CAAC;;AAiBvB,WAAA;EAAhB,MAAK;;AACW,WAAA;EAAhB,MAAK;;AAE8B,WAAA;EAAnC,MAAM,UAAU;;;;ACrHZ,IAAM,SAAS;;;;ACyBf,IAAM,WAAN,MAAMC,kBAAiB,OAAM;;AAClB,SAAA,SAA8B,CAAC,MAAM;AAD1C,WAAQ,WAAA;EADpB,cAAc,WAAW;GACb,QAAQ;", "names": ["State", "MdRipple"]}