import {
  Card,
  styles
} from "./chunk-JAQOZDDJ.js";
import "./chunk-HODMDVFX.js";
import {
  customElement
} from "./chunk-T3WMJB5E.js";
import {
  css
} from "./chunk-4GZ3EDRH.js";
import {
  __decorate
} from "./chunk-HMZZ7KLC.js";
import "./chunk-G3PMV62Z.js";

// node_modules/@material/web/labs/card/internal/filled-styles.js
var styles2 = css`:host{--_container-color: var(--md-filled-card-container-color, var(--md-sys-color-surface-container-highest, #e6e0e9));--_container-elevation: var(--md-filled-card-container-elevation, 0);--_container-shadow-color: var(--md-filled-card-container-shadow-color, var(--md-sys-color-shadow, #000));--_container-shape: var(--md-filled-card-container-shape, var(--md-sys-shape-corner-medium, 12px))}
`;

// node_modules/@material/web/labs/card/filled-card.js
var MdFilledCard = class MdFilledCard2 extends Card {
};
MdFilledCard.styles = [styles, styles2];
MdFilledCard = __decorate([
  customElement("md-filled-card")
], MdFilledCard);
export {
  MdFilledCard
};
/*! Bundled license information:

@material/web/labs/card/internal/filled-styles.js:
  (**
   * @license
   * Copyright 2024 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   *)

@material/web/labs/card/filled-card.js:
  (**
   * @license
   * Copyright 2023 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   *)
*/
//# sourceMappingURL=@material_web_labs_card_filled-card__js.js.map
