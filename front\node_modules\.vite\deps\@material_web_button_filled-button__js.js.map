{"version": 3, "sources": ["../../@material/web/button/internal/filled-button.ts", "../../@material/web/button/internal/filled-styles.ts", "../../@material/web/button/filled-button.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport '../../elevation/elevation.js';\n\nimport {html} from 'lit';\n\nimport {Button} from './button.js';\n\n/**\n * A filled button component.\n */\nexport class FilledButton extends Button {\n  protected override renderElevationOrOutline() {\n    return html`<md-elevation part=\"elevation\"></md-elevation>`;\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n// Generated stylesheet for ./button/internal/filled-styles.css.\nimport {css} from 'lit';\nexport const styles = css`:host{--_container-color: var(--md-filled-button-container-color, var(--md-sys-color-primary, #6750a4));--_container-elevation: var(--md-filled-button-container-elevation, 0);--_container-height: var(--md-filled-button-container-height, 40px);--_container-shadow-color: var(--md-filled-button-container-shadow-color, var(--md-sys-color-shadow, #000));--_disabled-container-color: var(--md-filled-button-disabled-container-color, var(--md-sys-color-on-surface, #1d1b20));--_disabled-container-elevation: var(--md-filled-button-disabled-container-elevation, 0);--_disabled-container-opacity: var(--md-filled-button-disabled-container-opacity, 0.12);--_disabled-label-text-color: var(--md-filled-button-disabled-label-text-color, var(--md-sys-color-on-surface, #1d1b20));--_disabled-label-text-opacity: var(--md-filled-button-disabled-label-text-opacity, 0.38);--_focus-container-elevation: var(--md-filled-button-focus-container-elevation, 0);--_focus-label-text-color: var(--md-filled-button-focus-label-text-color, var(--md-sys-color-on-primary, #fff));--_hover-container-elevation: var(--md-filled-button-hover-container-elevation, 1);--_hover-label-text-color: var(--md-filled-button-hover-label-text-color, var(--md-sys-color-on-primary, #fff));--_hover-state-layer-color: var(--md-filled-button-hover-state-layer-color, var(--md-sys-color-on-primary, #fff));--_hover-state-layer-opacity: var(--md-filled-button-hover-state-layer-opacity, 0.08);--_label-text-color: var(--md-filled-button-label-text-color, var(--md-sys-color-on-primary, #fff));--_label-text-font: var(--md-filled-button-label-text-font, var(--md-sys-typescale-label-large-font, var(--md-ref-typeface-plain, Roboto)));--_label-text-line-height: var(--md-filled-button-label-text-line-height, var(--md-sys-typescale-label-large-line-height, 1.25rem));--_label-text-size: var(--md-filled-button-label-text-size, var(--md-sys-typescale-label-large-size, 0.875rem));--_label-text-weight: var(--md-filled-button-label-text-weight, var(--md-sys-typescale-label-large-weight, var(--md-ref-typeface-weight-medium, 500)));--_pressed-container-elevation: var(--md-filled-button-pressed-container-elevation, 0);--_pressed-label-text-color: var(--md-filled-button-pressed-label-text-color, var(--md-sys-color-on-primary, #fff));--_pressed-state-layer-color: var(--md-filled-button-pressed-state-layer-color, var(--md-sys-color-on-primary, #fff));--_pressed-state-layer-opacity: var(--md-filled-button-pressed-state-layer-opacity, 0.12);--_disabled-icon-color: var(--md-filled-button-disabled-icon-color, var(--md-sys-color-on-surface, #1d1b20));--_disabled-icon-opacity: var(--md-filled-button-disabled-icon-opacity, 0.38);--_focus-icon-color: var(--md-filled-button-focus-icon-color, var(--md-sys-color-on-primary, #fff));--_hover-icon-color: var(--md-filled-button-hover-icon-color, var(--md-sys-color-on-primary, #fff));--_icon-color: var(--md-filled-button-icon-color, var(--md-sys-color-on-primary, #fff));--_icon-size: var(--md-filled-button-icon-size, 18px);--_pressed-icon-color: var(--md-filled-button-pressed-icon-color, var(--md-sys-color-on-primary, #fff));--_container-shape-start-start: var(--md-filled-button-container-shape-start-start, var(--md-filled-button-container-shape, var(--md-sys-shape-corner-full, 9999px)));--_container-shape-start-end: var(--md-filled-button-container-shape-start-end, var(--md-filled-button-container-shape, var(--md-sys-shape-corner-full, 9999px)));--_container-shape-end-end: var(--md-filled-button-container-shape-end-end, var(--md-filled-button-container-shape, var(--md-sys-shape-corner-full, 9999px)));--_container-shape-end-start: var(--md-filled-button-container-shape-end-start, var(--md-filled-button-container-shape, var(--md-sys-shape-corner-full, 9999px)));--_leading-space: var(--md-filled-button-leading-space, 24px);--_trailing-space: var(--md-filled-button-trailing-space, 24px);--_with-leading-icon-leading-space: var(--md-filled-button-with-leading-icon-leading-space, 16px);--_with-leading-icon-trailing-space: var(--md-filled-button-with-leading-icon-trailing-space, 24px);--_with-trailing-icon-leading-space: var(--md-filled-button-with-trailing-icon-leading-space, 24px);--_with-trailing-icon-trailing-space: var(--md-filled-button-with-trailing-icon-trailing-space, 16px)}\n`;\n", "/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {FilledButton} from './internal/filled-button.js';\nimport {styles as filledStyles} from './internal/filled-styles.js';\nimport {styles as sharedElevationStyles} from './internal/shared-elevation-styles.js';\nimport {styles as sharedStyles} from './internal/shared-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-filled-button': MdFilledButton;\n  }\n}\n\n/**\n * @summary Buttons help people take action, such as sending an email, sharing a\n * document, or liking a comment.\n *\n * @description\n * __Emphasis:__ High emphasis – For the primary, most important, or most common\n * action on a screen\n *\n * __Rationale:__ The filled button’s contrasting surface color makes it the\n * most prominent button after the FAB. It’s used for final or unblocking\n * actions in a flow.\n *\n * __Example usages:__\n * - Save\n * - Confirm\n * - Done\n *\n * @final\n * @suppress {visibility}\n */\n@customElement('md-filled-button')\nexport class MdFilledButton extends FilledButton {\n  static override styles: CSSResultOrNative[] = [\n    sharedStyles,\n    sharedElevationStyles,\n    filledStyles,\n  ];\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAeM,IAAO,eAAP,cAA4B,OAAM;EACnB,2BAAwB;AACzC,WAAO;EACT;;;;ACXK,IAAMA,UAAS;;;;ACkCf,IAAM,iBAAN,MAAMC,wBAAuB,aAAY;;AAC9B,eAAA,SAA8B;EAC5CC;EACA;EACAA;;AAJS,iBAAc,WAAA;EAD1B,cAAc,kBAAkB;GACpB,cAAc;", "names": ["styles", "MdFilledButton", "styles"]}