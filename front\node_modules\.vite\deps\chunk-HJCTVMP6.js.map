{"version": 3, "sources": ["../../@material/web/chips/internal/assist-chip.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport '../../elevation/elevation.js';\n\nimport {html, nothing} from 'lit';\nimport {property} from 'lit/decorators.js';\n\nimport {ARIAMixinStrict} from '../../internal/aria/aria.js';\n\nimport {Chip} from './chip.js';\n\n/**\n * An assist chip component.\n */\nexport class AssistChip extends Chip {\n  @property({type: Boolean}) elevated = false;\n  @property() href = '';\n  /**\n   * The filename to use when downloading the linked resource.\n   * If not specified, the browser will determine a filename.\n   * This is only applicable when the chip is used as a link (`href` is set).\n   */\n  @property() download = '';\n  @property() target: '_blank' | '_parent' | '_self' | '_top' | '' = '';\n\n  protected get primaryId() {\n    return this.href ? 'link' : 'button';\n  }\n\n  protected override get rippleDisabled() {\n    // Link chips cannot be disabled\n    return !this.href && (this.disabled || this.softDisabled);\n  }\n\n  protected override getContainerClasses() {\n    return {\n      ...super.getContainerClasses(),\n      // Link chips cannot be disabled\n      disabled: !this.href && (this.disabled || this.softDisabled),\n      elevated: this.elevated,\n      link: !!this.href,\n    };\n  }\n\n  protected override renderPrimaryAction(content: unknown) {\n    const {ariaLabel} = this as ARIAMixinStrict;\n    if (this.href) {\n      return html`\n        <a\n          class=\"primary action\"\n          id=\"link\"\n          aria-label=${ariaLabel || nothing}\n          href=${this.href}\n          download=${this.download || nothing}\n          target=${this.target || nothing}\n          >${content}</a\n        >\n      `;\n    }\n\n    return html`\n      <button\n        class=\"primary action\"\n        id=\"button\"\n        aria-label=${ariaLabel || nothing}\n        aria-disabled=${this.softDisabled || nothing}\n        ?disabled=${this.disabled && !this.alwaysFocusable}\n        type=\"button\"\n        >${content}</button\n      >\n    `;\n  }\n\n  protected override renderOutline() {\n    if (this.elevated) {\n      return html`<md-elevation part=\"elevation\"></md-elevation>`;\n    }\n\n    return super.renderOutline();\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;AAkBM,IAAO,aAAP,cAA0B,KAAI;EAApC,cAAA;;AAC6B,SAAA,WAAW;AAC1B,SAAA,OAAO;AAMP,SAAA,WAAW;AACX,SAAA,SAAuD;EAyDrE;EAvDE,IAAc,YAAS;AACrB,WAAO,KAAK,OAAO,SAAS;EAC9B;EAEA,IAAuB,iBAAc;AAEnC,WAAO,CAAC,KAAK,SAAS,KAAK,YAAY,KAAK;EAC9C;EAEmB,sBAAmB;AACpC,WAAO;MACL,GAAG,MAAM,oBAAmB;;MAE5B,UAAU,CAAC,KAAK,SAAS,KAAK,YAAY,KAAK;MAC/C,UAAU,KAAK;MACf,MAAM,CAAC,CAAC,KAAK;;EAEjB;EAEmB,oBAAoB,SAAgB;AACrD,UAAM,EAAC,UAAS,IAAI;AACpB,QAAI,KAAK,MAAM;AACb,aAAO;;;;uBAIU,aAAa,OAAO;iBAC1B,KAAK,IAAI;qBACL,KAAK,YAAY,OAAO;mBAC1B,KAAK,UAAU,OAAO;aAC5B,OAAO;;;IAGhB;AAEA,WAAO;;;;qBAIU,aAAa,OAAO;wBACjB,KAAK,gBAAgB,OAAO;oBAChC,KAAK,YAAY,CAAC,KAAK,eAAe;;WAE/C,OAAO;;;EAGhB;EAEmB,gBAAa;AAC9B,QAAI,KAAK,UAAU;AACjB,aAAO;IACT;AAEA,WAAO,MAAM,cAAa;EAC5B;;AAhE2B,WAAA;EAA1B,SAAS,EAAC,MAAM,QAAO,CAAC;;AACb,WAAA;EAAX,SAAQ;;AAMG,WAAA;EAAX,SAAQ;;AACG,WAAA;EAAX,SAAQ;;", "names": []}