import {
  mixinDelegatesAria
} from "./chunk-GXE4MBY5.js";
import {
  classMap
} from "./chunk-SZQCPKZF.js";
import {
  __decorate,
  property
} from "./chunk-PZNDE6JX.js";
import {
  LitElement,
  html,
  nothing
} from "./chunk-4GZ3EDRH.js";

// node_modules/@material/web/progress/internal/progress.js
var progressBaseClass = mixinDelegatesAria(LitElement);
var Progress = class extends progressBaseClass {
  constructor() {
    super(...arguments);
    this.value = 0;
    this.max = 1;
    this.indeterminate = false;
    this.fourColor = false;
  }
  render() {
    const { ariaLabel } = this;
    return html`
      <div
        class="progress ${classMap(this.getRenderClasses())}"
        role="progressbar"
        aria-label="${ariaLabel || nothing}"
        aria-valuemin="0"
        aria-valuemax=${this.max}
        aria-valuenow=${this.indeterminate ? nothing : this.value}
        >${this.renderIndicator()}</div
      >
    `;
  }
  getRenderClasses() {
    return {
      "indeterminate": this.indeterminate,
      "four-color": this.fourColor
    };
  }
};
__decorate([
  property({ type: Number })
], Progress.prototype, "value", void 0);
__decorate([
  property({ type: Number })
], Progress.prototype, "max", void 0);
__decorate([
  property({ type: Boolean })
], Progress.prototype, "indeterminate", void 0);
__decorate([
  property({ type: Boolean, attribute: "four-color" })
], Progress.prototype, "fourColor", void 0);

export {
  Progress
};
/*! Bundled license information:

@material/web/progress/internal/progress.js:
  (**
   * @license
   * Copyright 2023 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   *)
*/
//# sourceMappingURL=chunk-XTBMA6Z5.js.map
