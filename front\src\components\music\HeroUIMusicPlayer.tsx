import React, { useState, useRef, useEffect } from 'react';
import {Card, CardBody, Button, Image, Progress} from "@heroui/react";
import {clsx} from "@heroui/shared-utils";
import {
  PlayIcon,
  PauseIcon,
  ForwardIcon,
  BackwardIcon,
  ArrowPathIcon,
  ArrowsRightLeftIcon,
  HeartIcon,
} from "@heroicons/react/24/solid";
import {HeartIcon as HeartOutlineIcon} from "@heroicons/react/24/outline";

interface Track {
  id: number;
  title: string;
  artist: string;
  duration: string;
  src: string;
  cover?: string;
}

const tracks: Track[] = [
  {
    id: 1,
    title: 'Neon Dreams',
    artist: 'Synthwave Collective',
    duration: '3:42',
    src: '/audio/neon-dreams.mp3',
    cover: '🌆'
  },
  {
    id: 2,
    title: 'Digital Horizon',
    artist: 'Cyber Orchestra',
    duration: '4:15',
    src: '/audio/digital-horizon.mp3',
    cover: '🌌'
  },
  {
    id: 3,
    title: 'Electric Pulse',
    artist: 'Future Beats',
    duration: '3:28',
    src: '/audio/electric-pulse.mp3',
    cover: '⚡'
  }
];

export interface MusicPlayerProps {
  className?: string;
  [key: string]: any;
}

const HeroUIMusicPlayer: React.FC<MusicPlayerProps> = ({className, ...otherProps}) => {
  const [currentTrack, setCurrentTrack] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [liked, setLiked] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const audioRef = useRef<HTMLAudioElement>(null);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const handleEnded = () => {
      if (currentTrack < tracks.length - 1) {
        setCurrentTrack(currentTrack + 1);
      } else {
        setIsPlaying(false);
      }
    };

    const handleTimeUpdate = () => {
      setCurrentTime(audio.currentTime);
    };

    const handleLoadedMetadata = () => {
      setDuration(audio.duration);
    };

    audio.addEventListener('ended', handleEnded);
    audio.addEventListener('timeupdate', handleTimeUpdate);
    audio.addEventListener('loadedmetadata', handleLoadedMetadata);

    return () => {
      audio.removeEventListener('ended', handleEnded);
      audio.removeEventListener('timeupdate', handleTimeUpdate);
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
    };
  }, [currentTrack]);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    audio.volume = 0.7;
    if (isPlaying) {
      audio.play().catch(console.error);
    } else {
      audio.pause();
    }
  }, [isPlaying, currentTrack]);

  const togglePlay = () => setIsPlaying(!isPlaying);

  const nextTrack = () => {
    setCurrentTrack((prev) => (prev + 1) % tracks.length);
  };

  const prevTrack = () => {
    setCurrentTrack((prev) => (prev - 1 + tracks.length) % tracks.length);
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const progressValue = duration > 0 ? (currentTime / duration) * 100 : 0;

  return (
    <Card
      isBlurred
      className={clsx("border-none bg-background/60 dark:bg-default-100/50 w-full max-w-none", className)}
      shadow="sm"
      {...otherProps}
    >
      <CardBody>
        <div className="flex flex-col gap-4">
          {/* Album Cover */}
          <div className="relative">
            <Image
              alt="Album cover"
              className="object-cover"
              classNames={{
                base: "shadow-black/20",
              }}
              height={160}
              shadow="lg"
              src="https://heroui.com/images/album-cover.png"
              width="100%"
            />
          </div>

          {/* Content Section */}
          <div className="flex flex-col">
            <div className="flex justify-between items-start">
              <div className="flex flex-col gap-0">
                <h3 className="font-semibold text-foreground/90">Daily Mix</h3>
                <p className="text-sm text-foreground/80">{tracks.length} Tracks</p>
                <h1 className="text-lg font-medium mt-2">{tracks[currentTrack]?.title}</h1>
              </div>
              <Button
                isIconOnly
                className="text-default-900/60 data-[hover=true]:bg-foreground/10 -translate-y-2 translate-x-2"
                radius="full"
                variant="light"
                onPress={() => setLiked((v) => !v)}
              >
                {liked ? (
                  <HeartIcon
                    className="w-5 h-5 text-red-500"
                    fill="currentColor"
                  />
                ) : (
                  <HeartOutlineIcon
                    className="w-5 h-5"
                    fill="none"
                  />
                )}
              </Button>
            </div>

            <div className="flex flex-col mt-3 gap-1">
              <Progress
                aria-label="Music progress"
                classNames={{
                  indicator: "bg-default-800 dark:bg-white",
                  track: "bg-default-500/30",
                }}
                color="default"
                size="sm"
                value={progressValue}
              />
              <div className="flex justify-between">
                <p className="text-sm">{formatTime(currentTime)}</p>
                <p className="text-sm text-foreground/50">{tracks[currentTrack]?.duration}</p>
              </div>
            </div>

            <div className="flex w-full items-center justify-center">
              <Button
                isIconOnly
                className="data-[hover=true]:bg-foreground/10"
                radius="full"
                variant="light"
              >
                <ArrowPathIcon className="w-5 h-5 text-foreground/80" />
              </Button>
              <Button
                isIconOnly
                className="data-[hover=true]:bg-foreground/10"
                radius="full"
                variant="light"
                onPress={prevTrack}
              >
                <BackwardIcon className="w-5 h-5" />
              </Button>
              <Button
                isIconOnly
                className="w-auto h-auto data-[hover=true]:bg-foreground/10"
                radius="full"
                variant="light"
                onPress={togglePlay}
              >
                {isPlaying ? (
                  <PauseIcon className="w-12 h-12" />
                ) : (
                  <PlayIcon className="w-12 h-12" />
                )}
              </Button>
              <Button
                isIconOnly
                className="data-[hover=true]:bg-foreground/10"
                radius="full"
                variant="light"
                onPress={nextTrack}
              >
                <ForwardIcon className="w-5 h-5" />
              </Button>
              <Button
                isIconOnly
                className="data-[hover=true]:bg-foreground/10"
                radius="full"
                variant="light"
              >
                <ArrowsRightLeftIcon className="w-5 h-5 text-foreground/80" />
              </Button>
            </div>
          </div>
        </div>
      </CardBody>

      <audio
        ref={audioRef}
        src={tracks[currentTrack]?.src}
        preload="metadata"
      />
    </Card>
  );
};

export default HeroUIMusicPlayer;
