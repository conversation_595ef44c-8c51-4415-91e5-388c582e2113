{"version": 3, "sources": ["../../@material/web/labs/item/internal/item.ts", "../../@material/web/labs/item/internal/item-styles.ts", "../../@material/web/labs/item/item.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {html, LitElement} from 'lit';\nimport {property, queryAll} from 'lit/decorators.js';\n\n/**\n * An item layout component.\n */\nexport class Item extends LitElement {\n  /**\n   * Only needed for SSR.\n   *\n   * Add this attribute when an item has two lines to avoid a Flash Of Unstyled\n   * Content. This attribute is not needed for single line items or items with\n   * three or more lines.\n   */\n  @property({type: Boolean, reflect: true}) multiline = false;\n\n  @queryAll('.text slot') private readonly textSlots!: HTMLSlotElement[];\n\n  override render() {\n    return html`\n      <slot name=\"container\"></slot>\n      <slot class=\"non-text\" name=\"start\"></slot>\n      <div class=\"text\">\n        <slot name=\"overline\" @slotchange=${this.handleTextSlotChange}></slot>\n        <slot\n          class=\"default-slot\"\n          @slotchange=${this.handleTextSlotChange}></slot>\n        <slot name=\"headline\" @slotchange=${this.handleTextSlotChange}></slot>\n        <slot\n          name=\"supporting-text\"\n          @slotchange=${this.handleTextSlotChange}></slot>\n      </div>\n      <slot class=\"non-text\" name=\"trailing-supporting-text\"></slot>\n      <slot class=\"non-text\" name=\"end\"></slot>\n    `;\n  }\n\n  private handleTextSlotChange() {\n    // Check if there's more than one text slot with content. If so, the item is\n    // multiline, which has a different min-height than single line items.\n    let isMultiline = false;\n    let slotsWithContent = 0;\n    for (const slot of this.textSlots) {\n      if (slotHasContent(slot)) {\n        slotsWithContent += 1;\n      }\n\n      if (slotsWithContent > 1) {\n        isMultiline = true;\n        break;\n      }\n    }\n\n    this.multiline = isMultiline;\n  }\n}\n\nfunction slotHasContent(slot: HTMLSlotElement) {\n  for (const node of slot.assignedNodes({flatten: true})) {\n    // Assume there's content if there's an element slotted in\n    const isElement = node.nodeType === Node.ELEMENT_NODE;\n    // If there's only text nodes for the default slot, check if there's\n    // non-whitespace.\n    const isTextWithContent =\n      node.nodeType === Node.TEXT_NODE && node.textContent?.match(/\\S/);\n    if (isElement || isTextWithContent) {\n      return true;\n    }\n  }\n\n  return false;\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n// Generated stylesheet for ./labs/item/internal/item-styles.css.\nimport {css} from 'lit';\nexport const styles = css`:host{color:var(--md-sys-color-on-surface, #1d1b20);font-family:var(--md-sys-typescale-body-large-font, var(--md-ref-typeface-plain, Roboto));font-size:var(--md-sys-typescale-body-large-size, 1rem);font-weight:var(--md-sys-typescale-body-large-weight, var(--md-ref-typeface-weight-regular, 400));line-height:var(--md-sys-typescale-body-large-line-height, 1.5rem);align-items:center;box-sizing:border-box;display:flex;gap:16px;min-height:56px;overflow:hidden;padding:12px 16px;position:relative;text-overflow:ellipsis}:host([multiline]){min-height:72px}[name=overline]{color:var(--md-sys-color-on-surface-variant, #49454f);font-family:var(--md-sys-typescale-label-small-font, var(--md-ref-typeface-plain, Roboto));font-size:var(--md-sys-typescale-label-small-size, 0.6875rem);font-weight:var(--md-sys-typescale-label-small-weight, var(--md-ref-typeface-weight-medium, 500));line-height:var(--md-sys-typescale-label-small-line-height, 1rem)}[name=supporting-text]{color:var(--md-sys-color-on-surface-variant, #49454f);font-family:var(--md-sys-typescale-body-medium-font, var(--md-ref-typeface-plain, Roboto));font-size:var(--md-sys-typescale-body-medium-size, 0.875rem);font-weight:var(--md-sys-typescale-body-medium-weight, var(--md-ref-typeface-weight-regular, 400));line-height:var(--md-sys-typescale-body-medium-line-height, 1.25rem)}[name=trailing-supporting-text]{color:var(--md-sys-color-on-surface-variant, #49454f);font-family:var(--md-sys-typescale-label-small-font, var(--md-ref-typeface-plain, Roboto));font-size:var(--md-sys-typescale-label-small-size, 0.6875rem);font-weight:var(--md-sys-typescale-label-small-weight, var(--md-ref-typeface-weight-medium, 500));line-height:var(--md-sys-typescale-label-small-line-height, 1rem)}[name=container]::slotted(*){inset:0;position:absolute}.default-slot{display:inline}.default-slot,.text ::slotted(*){overflow:hidden;text-overflow:ellipsis}.text{display:flex;flex:1;flex-direction:column;overflow:hidden}\n`;\n", "/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {Item} from './internal/item.js';\nimport {styles} from './internal/item-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-item': MdItem;\n  }\n}\n\n/**\n * An item layout component that can be used inside list items to give them\n * their customizable structure.\n *\n * `<md-item>` does not have any functionality, which must be added by the\n * component using it.\n *\n * All text will wrap unless `white-space: nowrap` is set on the item or any of\n * its children.\n *\n * Slots available:\n * - `<default>`: The headline, or custom content.\n * - `headline`: The first line.\n * - `supporting-text`: Supporting text lines underneath the headline.\n * - `trailing-supporting-text`: A small text snippet at the end of the item.\n * - `start`: Any leading content, such as icons, avatars, or checkboxes.\n * - `end`: Any trailing content, such as icons and buttons.\n * - `container`: Background container content, intended for adding additional\n *     styles, such as ripples or focus rings.\n *\n * @example\n * ```html\n * <md-item>Single line</md-item>\n *\n * <md-item>\n *   <div class=\"custom-content\">...</div>\n * </md-item>\n *\n * <!-- Classic 1 to 3+ line list items -->\n * <md-item>\n *   <md-icon slot=\"start\">image</md-icon>\n *   <div slot=\"overline\">Overline</div>\n *   <div slot=\"headline\">Headline</div>\n *   <div=\"supporting-text\">Supporting text</div>\n *   <div=\"trailing-supporting-text\">Trailing</div>\n *   <md-icon slot=\"end\">image</md-icon>\n * </md-item>\n * ```\n *\n * When wrapping `<md-item>`, forward the available slots to use the same slot\n * structure for the wrapping component (this is what `<md-list-item>` does).\n *\n * @example\n * ```html\n * <md-item>\n *   <slot></slot>\n *   <slot name=\"overline\" slot=\"overline\"></slot>\n *   <slot name=\"headline\" slot=\"headline\"></slot>\n *   <slot name=\"supporting-text\" slot=\"supporting-text\"></slot>\n *   <slot name=\"trailing-supporting-text\"\n *       slot=\"trailing-supporting-text\"></slot>\n *   <slot name=\"start\" slot=\"start\"></slot>\n *   <slot name=\"end\" slot=\"end\"></slot>\n * </md-item>\n * ```\n *\n * @final\n * @suppress {visibility}\n */\n@customElement('md-item')\nexport class MdItem extends Item {\n  static override styles: CSSResultOrNative[] = [styles];\n}\n"], "mappings": ";;;;;;;;;;;;;AAYM,IAAO,OAAP,cAAoB,WAAU;EAApC,cAAA;;AAQ4C,SAAA,YAAY;EAyCxD;EArCW,SAAM;AACb,WAAO;;;;4CAIiC,KAAK,oBAAoB;;;wBAG7C,KAAK,oBAAoB;4CACL,KAAK,oBAAoB;;;wBAG7C,KAAK,oBAAoB;;;;;EAK/C;EAEQ,uBAAoB;AAG1B,QAAI,cAAc;AAClB,QAAI,mBAAmB;AACvB,eAAW,QAAQ,KAAK,WAAW;AACjC,UAAI,eAAe,IAAI,GAAG;AACxB,4BAAoB;MACtB;AAEA,UAAI,mBAAmB,GAAG;AACxB,sBAAc;AACd;MACF;IACF;AAEA,SAAK,YAAY;EACnB;;AAxC0C,WAAA;EAAzC,SAAS,EAAC,MAAM,SAAS,SAAS,KAAI,CAAC;;AAEC,WAAA;EAAxC,SAAS,YAAY;;AAyCxB,SAAS,eAAe,MAAqB;AAC3C,aAAW,QAAQ,KAAK,cAAc,EAAC,SAAS,KAAI,CAAC,GAAG;AAEtD,UAAM,YAAY,KAAK,aAAa,KAAK;AAGzC,UAAM,oBACJ,KAAK,aAAa,KAAK,aAAa,KAAK,aAAa,MAAM,IAAI;AAClE,QAAI,aAAa,mBAAmB;AAClC,aAAO;IACT;EACF;AAEA,SAAO;AACT;;;ACtEO,IAAM,SAAS;;;;ACuEf,IAAM,SAAN,MAAMA,gBAAe,KAAI;;AACd,OAAA,SAA8B,CAAC,MAAM;AAD1C,SAAM,WAAA;EADlB,cAAc,SAAS;GACX,MAAM;", "names": ["MdItem"]}