{"version": 3, "sources": ["../../@material/web/dialog/internal/animations.ts", "../../@material/web/dialog/internal/dialog.ts", "../../@material/web/dialog/internal/dialog-styles.ts", "../../@material/web/dialog/dialog.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {EASING} from '../../internal/motion/animation.js';\n\n/**\n * A dialog animation's arguments. See `Element.prototype.animate`.\n */\nexport type DialogAnimationArgs = Parameters<Element['animate']>;\n\n/**\n * A collection of dialog animations. Each element of a dialog may have multiple\n * animations.\n */\nexport interface DialogAnimation {\n  /**\n   * Animations for the dialog itself.\n   */\n  dialog?: DialogAnimationArgs[];\n\n  /**\n   * Animations for the scrim backdrop.\n   */\n  scrim?: DialogAnimationArgs[];\n\n  /**\n   * Animations for the container of the dialog.\n   */\n  container?: DialogAnimationArgs[];\n\n  /**\n   * Animations for the headline section.\n   */\n  headline?: DialogAnimationArgs[];\n\n  /**\n   * Animations for the contents section.\n   */\n  content?: DialogAnimationArgs[];\n  /**\n   * Animations for the actions section.\n   */\n  actions?: DialogAnimationArgs[];\n}\n\n/**\n * The default dialog open animation.\n */\nexport const DIALOG_DEFAULT_OPEN_ANIMATION: DialogAnimation = {\n  dialog: [\n    [\n      // Dialog slide down\n      [{'transform': 'translateY(-50px)'}, {'transform': 'translateY(0)'}],\n      {duration: 500, easing: EASING.EMPHASIZED},\n    ],\n  ],\n  scrim: [\n    [\n      // Scrim fade in\n      [{'opacity': 0}, {'opacity': 0.32}],\n      {duration: 500, easing: 'linear'},\n    ],\n  ],\n  container: [\n    [\n      // Container fade in\n      [{'opacity': 0}, {'opacity': 1}],\n      {duration: 50, easing: 'linear', pseudoElement: '::before'},\n    ],\n    [\n      // Container grow\n      // Note: current spec says to grow from 0dp->100% and shrink from\n      // 100%->35%. We change this to 35%->100% to simplify the animation that\n      // is supposed to clip content as it grows. From 0dp it's possible to see\n      // text/actions appear before the container has fully grown.\n      [{'height': '35%'}, {'height': '100%'}],\n      {duration: 500, easing: EASING.EMPHASIZED, pseudoElement: '::before'},\n    ],\n  ],\n  headline: [\n    [\n      // Headline fade in\n      [{'opacity': 0}, {'opacity': 0, offset: 0.2}, {'opacity': 1}],\n      {duration: 250, easing: 'linear', fill: 'forwards'},\n    ],\n  ],\n  content: [\n    [\n      // Content fade in\n      [{'opacity': 0}, {'opacity': 0, offset: 0.2}, {'opacity': 1}],\n      {duration: 250, easing: 'linear', fill: 'forwards'},\n    ],\n  ],\n  actions: [\n    [\n      // Actions fade in\n      [{'opacity': 0}, {'opacity': 0, offset: 0.5}, {'opacity': 1}],\n      {duration: 300, easing: 'linear', fill: 'forwards'},\n    ],\n  ],\n};\n\n/**\n * The default dialog close animation.\n */\nexport const DIALOG_DEFAULT_CLOSE_ANIMATION: DialogAnimation = {\n  dialog: [\n    [\n      // Dialog slide up\n      [{'transform': 'translateY(0)'}, {'transform': 'translateY(-50px)'}],\n      {duration: 150, easing: EASING.EMPHASIZED_ACCELERATE},\n    ],\n  ],\n  scrim: [\n    [\n      // Scrim fade out\n      [{'opacity': 0.32}, {'opacity': 0}],\n      {duration: 150, easing: 'linear'},\n    ],\n  ],\n  container: [\n    [\n      // Container shrink\n      [{'height': '100%'}, {'height': '35%'}],\n      {\n        duration: 150,\n        easing: EASING.EMPHASIZED_ACCELERATE,\n        pseudoElement: '::before',\n      },\n    ],\n    [\n      // Container fade out\n      [{'opacity': '1'}, {'opacity': '0'}],\n      {delay: 100, duration: 50, easing: 'linear', pseudoElement: '::before'},\n    ],\n  ],\n  headline: [\n    [\n      // Headline fade out\n      [{'opacity': 1}, {'opacity': 0}],\n      {duration: 100, easing: 'linear', fill: 'forwards'},\n    ],\n  ],\n  content: [\n    [\n      // Content fade out\n      [{'opacity': 1}, {'opacity': 0}],\n      {duration: 100, easing: 'linear', fill: 'forwards'},\n    ],\n  ],\n  actions: [\n    [\n      // Actions fade out\n      [{'opacity': 1}, {'opacity': 0}],\n      {duration: 100, easing: 'linear', fill: 'forwards'},\n    ],\n  ],\n};\n", "/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport '../../divider/divider.js';\n\nimport {html, isServer, LitElement, nothing} from 'lit';\nimport {property, query, state} from 'lit/decorators.js';\nimport {classMap} from 'lit/directives/class-map.js';\n\nimport {ARIAMixinStrict} from '../../internal/aria/aria.js';\nimport {mixinDelegatesAria} from '../../internal/aria/delegate.js';\nimport {redispatchEvent} from '../../internal/events/redispatch-event.js';\n\nimport {\n  DIALOG_DEFAULT_CLOSE_ANIMATION,\n  DIALOG_DEFAULT_OPEN_ANIMATION,\n  DialogAnimation,\n  DialogAnimationArgs,\n} from './animations.js';\n\n// Separate variable needed for closure.\nconst dialogBaseClass = mixinDelegatesAria(LitElement);\n\n/**\n * A dialog component.\n *\n * @fires open {Event} Dispatched when the dialog is opening before any animations.\n * @fires opened {Event} Dispatched when the dialog has opened after any animations.\n * @fires close {Event} Dispatched when the dialog is closing before any animations.\n * @fires closed {Event} Dispatched when the dialog has closed after any animations.\n * @fires cancel {Event} Dispatched when the dialog has been canceled by clicking\n * on the scrim or pressing Escape.\n */\nexport class Dialog extends dialogBaseClass {\n  // We do not use `delegatesFocus: true` due to a Chromium bug with\n  // selecting text.\n  // See https://bugs.chromium.org/p/chromium/issues/detail?id=950357\n\n  /**\n   * Opens the dialog when set to `true` and closes it when set to `false`.\n   */\n  @property({type: Boolean})\n  get open() {\n    return this.isOpen;\n  }\n\n  set open(open: boolean) {\n    if (open === this.isOpen) {\n      return;\n    }\n\n    this.isOpen = open;\n    if (open) {\n      this.setAttribute('open', '');\n      this.show();\n    } else {\n      this.removeAttribute('open');\n      this.close();\n    }\n  }\n\n  /**\n   * Skips the opening and closing animations.\n   */\n  @property({type: Boolean}) quick = false;\n\n  /**\n   * Gets or sets the dialog's return value, usually to indicate which button\n   * a user pressed to close it.\n   *\n   * https://developer.mozilla.org/en-US/docs/Web/API/HTMLDialogElement/returnValue\n   */\n  @property({attribute: false}) returnValue = '';\n\n  /**\n   * The type of dialog for accessibility. Set this to `alert` to announce a\n   * dialog as an alert dialog.\n   */\n  @property() type?: 'alert';\n\n  /**\n   * Disables focus trapping, which by default keeps keyboard Tab navigation\n   * within the dialog.\n   *\n   * When disabled, after focusing the last element of a dialog, pressing Tab\n   * again will release focus from the window back to the browser (such as the\n   * URL bar).\n   *\n   * Focus trapping is recommended for accessibility, and should not typically\n   * be disabled. Only turn this off if the use case of a dialog is more\n   * accessible without focus trapping.\n   */\n  @property({type: Boolean, attribute: 'no-focus-trap'})\n  noFocusTrap = false;\n\n  /**\n   * Gets the opening animation for a dialog. Set to a new function to customize\n   * the animation.\n   */\n  getOpenAnimation = () => DIALOG_DEFAULT_OPEN_ANIMATION;\n\n  /**\n   * Gets the closing animation for a dialog. Set to a new function to customize\n   * the animation.\n   */\n  getCloseAnimation = () => DIALOG_DEFAULT_CLOSE_ANIMATION;\n\n  private isOpen = false;\n  private isOpening = false;\n  // getIsConnectedPromise() immediately sets the resolve property.\n  private isConnectedPromiseResolve!: () => void;\n  private isConnectedPromise = this.getIsConnectedPromise();\n  @query('dialog') private readonly dialog!: HTMLDialogElement | null;\n  @query('.scrim') private readonly scrim!: HTMLDialogElement | null;\n  @query('.container') private readonly container!: HTMLDialogElement | null;\n  @query('.headline') private readonly headline!: HTMLDialogElement | null;\n  @query('.content') private readonly content!: HTMLDialogElement | null;\n  @query('.actions') private readonly actions!: HTMLDialogElement | null;\n  @state() private isAtScrollTop = false;\n  @state() private isAtScrollBottom = false;\n  @query('.scroller') private readonly scroller!: HTMLElement | null;\n  @query('.top.anchor') private readonly topAnchor!: HTMLElement | null;\n  @query('.bottom.anchor') private readonly bottomAnchor!: HTMLElement | null;\n  @query('.focus-trap')\n  private readonly firstFocusTrap!: HTMLElement | null;\n  private nextClickIsFromContent = false;\n  private intersectionObserver?: IntersectionObserver;\n  // Dialogs should not be SSR'd while open, so we can just use runtime checks.\n  @state() private hasHeadline = false;\n  @state() private hasActions = false;\n  @state() private hasIcon = false;\n  private cancelAnimations?: AbortController;\n\n  // See https://bugs.chromium.org/p/chromium/issues/detail?id=1512224\n  // Chrome v120 has a bug where escape keys do not trigger cancels. If we get\n  // a dialog \"close\" event that is triggered without a \"cancel\" after an escape\n  // keydown, then we need to manually trigger our closing logic.\n  //\n  // This bug occurs when pressing escape to close a dialog without first\n  // interacting with the dialog's content.\n  //\n  // Cleanup tracking:\n  // https://github.com/material-components/material-web/issues/5330\n  // This can be removed when full CloseWatcher support added and the above bug\n  // in Chromium is fixed to fire 'cancel' with one escape press and close with\n  // multiple.\n  private escapePressedWithoutCancel = false;\n  // This TreeWalker is used to walk through a dialog's children to find\n  // focusable elements. TreeWalker is faster than `querySelectorAll('*')`.\n  // We check for isServer because there isn't a \"document\" during an SSR\n  // run.\n  private readonly treewalker = isServer\n    ? null\n    : document.createTreeWalker(this, NodeFilter.SHOW_ELEMENT);\n\n  constructor() {\n    super();\n    if (!isServer) {\n      this.addEventListener('submit', this.handleSubmit);\n    }\n  }\n\n  /**\n   * Opens the dialog and fires a cancelable `open` event. After a dialog's\n   * animation, an `opened` event is fired.\n   *\n   * Add an `autofocus` attribute to a child of the dialog that should\n   * receive focus after opening.\n   *\n   * @return A Promise that resolves after the animation is finished and the\n   *     `opened` event was fired.\n   */\n  async show() {\n    this.isOpening = true;\n    // Dialogs can be opened before being attached to the DOM, so we need to\n    // wait until we're connected before calling `showModal()`.\n    await this.isConnectedPromise;\n    await this.updateComplete;\n    const dialog = this.dialog!;\n    // Check if already opened or if `dialog.close()` was called while awaiting.\n    if (dialog.open || !this.isOpening) {\n      this.isOpening = false;\n      return;\n    }\n\n    const preventOpen = !this.dispatchEvent(\n      new Event('open', {cancelable: true}),\n    );\n    if (preventOpen) {\n      this.open = false;\n      this.isOpening = false;\n      return;\n    }\n\n    // All Material dialogs are modal.\n    dialog.showModal();\n    this.open = true;\n    // Reset scroll position if re-opening a dialog with the same content.\n    if (this.scroller) {\n      this.scroller.scrollTop = 0;\n    }\n    // Native modal dialogs ignore autofocus and instead force focus to the\n    // first focusable child. Override this behavior if there is a child with\n    // an autofocus attribute.\n    this.querySelector<HTMLElement>('[autofocus]')?.focus();\n\n    await this.animateDialog(this.getOpenAnimation());\n    this.dispatchEvent(new Event('opened'));\n    this.isOpening = false;\n  }\n\n  /**\n   * Closes the dialog and fires a cancelable `close` event. After a dialog's\n   * animation, a `closed` event is fired.\n   *\n   * @param returnValue A return value usually indicating which button was used\n   *     to close a dialog. If a dialog is canceled by clicking the scrim or\n   *     pressing Escape, it will not change the return value after closing.\n   * @return A Promise that resolves after the animation is finished and the\n   *     `closed` event was fired.\n   */\n  async close(returnValue = this.returnValue) {\n    this.isOpening = false;\n    if (!this.isConnected) {\n      // Disconnected dialogs do not fire close events or animate.\n      this.open = false;\n      return;\n    }\n\n    await this.updateComplete;\n    const dialog = this.dialog!;\n    // Check if already closed or if `dialog.show()` was called while awaiting.\n    if (!dialog.open || this.isOpening) {\n      this.open = false;\n      return;\n    }\n\n    const prevReturnValue = this.returnValue;\n    this.returnValue = returnValue;\n    const preventClose = !this.dispatchEvent(\n      new Event('close', {cancelable: true}),\n    );\n    if (preventClose) {\n      this.returnValue = prevReturnValue;\n      return;\n    }\n\n    await this.animateDialog(this.getCloseAnimation());\n    dialog.close(returnValue);\n    this.open = false;\n    this.dispatchEvent(new Event('closed'));\n  }\n\n  override connectedCallback() {\n    super.connectedCallback();\n    this.isConnectedPromiseResolve();\n  }\n\n  override disconnectedCallback() {\n    super.disconnectedCallback();\n    this.isConnectedPromise = this.getIsConnectedPromise();\n  }\n\n  protected override render() {\n    const scrollable =\n      this.open && !(this.isAtScrollTop && this.isAtScrollBottom);\n    const classes = {\n      'has-headline': this.hasHeadline,\n      'has-actions': this.hasActions,\n      'has-icon': this.hasIcon,\n      'scrollable': scrollable,\n      'show-top-divider': scrollable && !this.isAtScrollTop,\n      'show-bottom-divider': scrollable && !this.isAtScrollBottom,\n    };\n\n    // The focus trap sentinels are only added after the dialog opens, since\n    // dialog.showModal() will try to autofocus them, even with tabindex=\"-1\".\n    const showFocusTrap = this.open && !this.noFocusTrap;\n    const focusTrap = html`\n      <div\n        class=\"focus-trap\"\n        tabindex=\"0\"\n        aria-hidden=\"true\"\n        @focus=${this.handleFocusTrapFocus}></div>\n    `;\n\n    const {ariaLabel} = this as ARIAMixinStrict;\n    return html`\n      <div class=\"scrim\"></div>\n      <dialog\n        class=${classMap(classes)}\n        aria-label=${ariaLabel || nothing}\n        aria-labelledby=${this.hasHeadline ? 'headline' : nothing}\n        role=${this.type === 'alert' ? 'alertdialog' : nothing}\n        @cancel=${this.handleCancel}\n        @click=${this.handleDialogClick}\n        @close=${this.handleClose}\n        @keydown=${this.handleKeydown}\n        .returnValue=${this.returnValue || nothing}>\n        ${showFocusTrap ? focusTrap : nothing}\n        <div class=\"container\" @click=${this.handleContentClick}>\n          <div class=\"headline\">\n            <div class=\"icon\" aria-hidden=\"true\">\n              <slot name=\"icon\" @slotchange=${this.handleIconChange}></slot>\n            </div>\n            <h2 id=\"headline\" aria-hidden=${!this.hasHeadline || nothing}>\n              <slot\n                name=\"headline\"\n                @slotchange=${this.handleHeadlineChange}></slot>\n            </h2>\n            <md-divider></md-divider>\n          </div>\n          <div class=\"scroller\">\n            <div class=\"content\">\n              <div class=\"top anchor\"></div>\n              <slot name=\"content\"></slot>\n              <div class=\"bottom anchor\"></div>\n            </div>\n          </div>\n          <div class=\"actions\">\n            <md-divider></md-divider>\n            <slot name=\"actions\" @slotchange=${this.handleActionsChange}></slot>\n          </div>\n        </div>\n        ${showFocusTrap ? focusTrap : nothing}\n      </dialog>\n    `;\n  }\n\n  protected override firstUpdated() {\n    this.intersectionObserver = new IntersectionObserver(\n      (entries) => {\n        for (const entry of entries) {\n          this.handleAnchorIntersection(entry);\n        }\n      },\n      {root: this.scroller!},\n    );\n\n    this.intersectionObserver.observe(this.topAnchor!);\n    this.intersectionObserver.observe(this.bottomAnchor!);\n  }\n\n  private handleDialogClick() {\n    if (this.nextClickIsFromContent) {\n      // Avoid doing a layout calculation below if we know the click came from\n      // content.\n      this.nextClickIsFromContent = false;\n      return;\n    }\n\n    // Click originated on the backdrop. Native `<dialog>`s will not cancel,\n    // but Material dialogs do.\n    const preventDefault = !this.dispatchEvent(\n      new Event('cancel', {cancelable: true}),\n    );\n    if (preventDefault) {\n      return;\n    }\n\n    this.close();\n  }\n\n  private handleContentClick() {\n    this.nextClickIsFromContent = true;\n  }\n\n  private handleSubmit(event: SubmitEvent) {\n    const form = event.target as HTMLFormElement;\n    const {submitter} = event;\n    if (form.getAttribute('method') !== 'dialog' || !submitter) {\n      return;\n    }\n\n    // Close reason is the submitter's value attribute, or the dialog's\n    // `returnValue` if there is no attribute.\n    this.close(submitter.getAttribute('value') ?? this.returnValue);\n  }\n\n  private handleCancel(event: Event) {\n    if (event.target !== this.dialog) {\n      // Ignore any cancel events dispatched by content.\n      return;\n    }\n\n    this.escapePressedWithoutCancel = false;\n    const preventDefault = !redispatchEvent(this, event);\n    // We always prevent default on the original dialog event since we'll\n    // animate closing it before it actually closes.\n    event.preventDefault();\n    if (preventDefault) {\n      return;\n    }\n\n    this.close();\n  }\n\n  private handleClose() {\n    if (!this.escapePressedWithoutCancel) {\n      return;\n    }\n\n    this.escapePressedWithoutCancel = false;\n    this.dialog?.dispatchEvent(new Event('cancel', {cancelable: true}));\n  }\n\n  private handleKeydown(event: KeyboardEvent) {\n    if (event.key !== 'Escape') {\n      return;\n    }\n\n    // An escape key was pressed. If a \"close\" event fires next without a\n    // \"cancel\" event first, then we know we're in the Chrome v120 bug.\n    this.escapePressedWithoutCancel = true;\n    // Wait a full task for the cancel/close event listeners to fire, then\n    // reset the flag.\n    setTimeout(() => {\n      this.escapePressedWithoutCancel = false;\n    });\n  }\n\n  private async animateDialog(animation: DialogAnimation) {\n    // Always cancel the previous animations. Animations can include `fill`\n    // modes that need to be cleared when `quick` is toggled. If not, content\n    // that faded out will remain hidden when a `quick` dialog re-opens after\n    // previously opening and closing without `quick`.\n    this.cancelAnimations?.abort();\n    this.cancelAnimations = new AbortController();\n    if (this.quick) {\n      return;\n    }\n\n    const {dialog, scrim, container, headline, content, actions} = this;\n    if (!dialog || !scrim || !container || !headline || !content || !actions) {\n      return;\n    }\n\n    const {\n      container: containerAnimate,\n      dialog: dialogAnimate,\n      scrim: scrimAnimate,\n      headline: headlineAnimate,\n      content: contentAnimate,\n      actions: actionsAnimate,\n    } = animation;\n\n    const elementAndAnimation: Array<[Element, DialogAnimationArgs[]]> = [\n      [dialog, dialogAnimate ?? []],\n      [scrim, scrimAnimate ?? []],\n      [container, containerAnimate ?? []],\n      [headline, headlineAnimate ?? []],\n      [content, contentAnimate ?? []],\n      [actions, actionsAnimate ?? []],\n    ];\n\n    const animations: Animation[] = [];\n    for (const [element, animation] of elementAndAnimation) {\n      for (const animateArgs of animation) {\n        const animation = element.animate(...animateArgs);\n        this.cancelAnimations.signal.addEventListener('abort', () => {\n          animation.cancel();\n        });\n\n        animations.push(animation);\n      }\n    }\n\n    await Promise.all(\n      animations.map((animation) =>\n        animation.finished.catch(() => {\n          // Ignore intentional AbortErrors when calling `animation.cancel()`.\n        }),\n      ),\n    );\n  }\n\n  private handleHeadlineChange(event: Event) {\n    const slot = event.target as HTMLSlotElement;\n    this.hasHeadline = slot.assignedElements().length > 0;\n  }\n\n  private handleActionsChange(event: Event) {\n    const slot = event.target as HTMLSlotElement;\n    this.hasActions = slot.assignedElements().length > 0;\n  }\n\n  private handleIconChange(event: Event) {\n    const slot = event.target as HTMLSlotElement;\n    this.hasIcon = slot.assignedElements().length > 0;\n  }\n\n  private handleAnchorIntersection(entry: IntersectionObserverEntry) {\n    const {target, isIntersecting} = entry;\n    if (target === this.topAnchor) {\n      this.isAtScrollTop = isIntersecting;\n    }\n\n    if (target === this.bottomAnchor) {\n      this.isAtScrollBottom = isIntersecting;\n    }\n  }\n\n  private getIsConnectedPromise() {\n    return new Promise<void>((resolve) => {\n      this.isConnectedPromiseResolve = resolve;\n    });\n  }\n\n  private handleFocusTrapFocus(event: FocusEvent) {\n    const [firstFocusableChild, lastFocusableChild] =\n      this.getFirstAndLastFocusableChildren();\n    if (!firstFocusableChild || !lastFocusableChild) {\n      // When a dialog does not have focusable children, the dialog itself\n      // receives focus.\n      this.dialog?.focus();\n      return;\n    }\n\n    // To determine which child to focus, we need to know which focus trap\n    // received focus...\n    const isFirstFocusTrap = event.target === this.firstFocusTrap;\n    const isLastFocusTrap = !isFirstFocusTrap;\n    // ...and where the focus came from (what was previously focused).\n    const focusCameFromFirstChild = event.relatedTarget === firstFocusableChild;\n    const focusCameFromLastChild = event.relatedTarget === lastFocusableChild;\n    // Although this is a focus trap, focus can come from outside the trap.\n    // This can happen when elements are programmatically `focus()`'d. It also\n    // happens when focus leaves and returns to the window, such as clicking on\n    // the browser's URL bar and pressing Tab, or switching focus between\n    // iframes.\n    const focusCameFromOutsideDialog =\n      !focusCameFromFirstChild && !focusCameFromLastChild;\n\n    // Focus the dialog's first child when we reach the end of the dialog and\n    // focus is moving forward. Or, when focus is moving forwards into the\n    // dialog from outside of the window.\n    const shouldFocusFirstChild =\n      (isLastFocusTrap && focusCameFromLastChild) ||\n      (isFirstFocusTrap && focusCameFromOutsideDialog);\n    if (shouldFocusFirstChild) {\n      firstFocusableChild.focus();\n      return;\n    }\n\n    // Focus the dialog's last child when we reach the beginning of the dialog\n    // and focus is moving backward. Or, when focus is moving backwards into the\n    // dialog from outside of the window.\n    const shouldFocusLastChild =\n      (isFirstFocusTrap && focusCameFromFirstChild) ||\n      (isLastFocusTrap && focusCameFromOutsideDialog);\n    if (shouldFocusLastChild) {\n      lastFocusableChild.focus();\n      return;\n    }\n\n    // The booleans above are verbose for readability, but code executation\n    // won't actually reach here.\n  }\n\n  private getFirstAndLastFocusableChildren():\n    | [HTMLElement, HTMLElement]\n    | [null, null] {\n    if (!this.treewalker) {\n      return [null, null];\n    }\n\n    let firstFocusableChild: HTMLElement | null = null;\n    let lastFocusableChild: HTMLElement | null = null;\n\n    // Reset the current node back to the root host element.\n    this.treewalker.currentNode = this.treewalker.root;\n    while (this.treewalker.nextNode()) {\n      // Cast as Element since the TreeWalker filter only accepts Elements.\n      const nextChild = this.treewalker.currentNode as Element;\n      if (!isFocusable(nextChild)) {\n        continue;\n      }\n\n      if (!firstFocusableChild) {\n        firstFocusableChild = nextChild;\n      }\n\n      lastFocusableChild = nextChild;\n    }\n\n    // We set lastFocusableChild immediately after finding a\n    // firstFocusableChild, which means the pair is either both null or both\n    // non-null. Cast since TypeScript does not recognize this.\n    return [firstFocusableChild, lastFocusableChild] as\n      | [HTMLElement, HTMLElement]\n      | [null, null];\n  }\n}\n\nfunction isFocusable(element: Element): element is HTMLElement {\n  // Check if the element is a known built-in focusable element:\n  // - <a> and <area> with `href` attributes.\n  // - Form controls that are not disabled.\n  // - `contenteditable` elements.\n  // - Anything with a non-negative `tabindex`.\n  const knownFocusableElements =\n    ':is(button,input,select,textarea,object,:is(a,area)[href],[tabindex],[contenteditable=true])';\n  const notDisabled = ':not(:disabled,[disabled])';\n  const notNegativeTabIndex = ':not([tabindex^=\"-\"])';\n  if (\n    element.matches(knownFocusableElements + notDisabled + notNegativeTabIndex)\n  ) {\n    return true;\n  }\n\n  const isCustomElement = element.localName.includes('-');\n  if (!isCustomElement) {\n    return false;\n  }\n\n  // If a custom element does not have a tabindex, it may still be focusable\n  // if it delegates focus with a shadow root. We also need to check again if\n  // the custom element is a disabled form control.\n  if (!element.matches(notDisabled)) {\n    return false;\n  }\n\n  return element.shadowRoot?.delegatesFocus ?? false;\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n// Generated stylesheet for ./dialog/internal/dialog-styles.css.\nimport {css} from 'lit';\nexport const styles = css`:host{border-start-start-radius:var(--md-dialog-container-shape-start-start, var(--md-dialog-container-shape, var(--md-sys-shape-corner-extra-large, 28px)));border-start-end-radius:var(--md-dialog-container-shape-start-end, var(--md-dialog-container-shape, var(--md-sys-shape-corner-extra-large, 28px)));border-end-end-radius:var(--md-dialog-container-shape-end-end, var(--md-dialog-container-shape, var(--md-sys-shape-corner-extra-large, 28px)));border-end-start-radius:var(--md-dialog-container-shape-end-start, var(--md-dialog-container-shape, var(--md-sys-shape-corner-extra-large, 28px)));display:contents;margin:auto;max-height:min(560px,100% - 48px);max-width:min(560px,100% - 48px);min-height:140px;min-width:280px;position:fixed;height:fit-content;width:fit-content}dialog{background:rgba(0,0,0,0);border:none;border-radius:inherit;flex-direction:column;height:inherit;margin:inherit;max-height:inherit;max-width:inherit;min-height:inherit;min-width:inherit;outline:none;overflow:visible;padding:0;width:inherit}dialog[open]{display:flex}::backdrop{background:none}.scrim{background:var(--md-sys-color-scrim, #000);display:none;inset:0;opacity:32%;pointer-events:none;position:fixed;z-index:1}:host([open]) .scrim{display:flex}h2{all:unset;align-self:stretch}.headline{align-items:center;color:var(--md-dialog-headline-color, var(--md-sys-color-on-surface, #1d1b20));display:flex;flex-direction:column;font-family:var(--md-dialog-headline-font, var(--md-sys-typescale-headline-small-font, var(--md-ref-typeface-brand, Roboto)));font-size:var(--md-dialog-headline-size, var(--md-sys-typescale-headline-small-size, 1.5rem));line-height:var(--md-dialog-headline-line-height, var(--md-sys-typescale-headline-small-line-height, 2rem));font-weight:var(--md-dialog-headline-weight, var(--md-sys-typescale-headline-small-weight, var(--md-ref-typeface-weight-regular, 400)));position:relative}slot[name=headline]::slotted(*){align-items:center;align-self:stretch;box-sizing:border-box;display:flex;gap:8px;padding:24px 24px 0}.icon{display:flex}slot[name=icon]::slotted(*){color:var(--md-dialog-icon-color, var(--md-sys-color-secondary, #625b71));fill:currentColor;font-size:var(--md-dialog-icon-size, 24px);margin-top:24px;height:var(--md-dialog-icon-size, 24px);width:var(--md-dialog-icon-size, 24px)}.has-icon slot[name=headline]::slotted(*){justify-content:center;padding-top:16px}.scrollable slot[name=headline]::slotted(*){padding-bottom:16px}.scrollable.has-headline slot[name=content]::slotted(*){padding-top:8px}.container{border-radius:inherit;display:flex;flex-direction:column;flex-grow:1;overflow:hidden;position:relative;transform-origin:top}.container::before{background:var(--md-dialog-container-color, var(--md-sys-color-surface-container-high, #ece6f0));border-radius:inherit;content:\"\";inset:0;position:absolute}.scroller{display:flex;flex:1;flex-direction:column;overflow:hidden;z-index:1}.scrollable .scroller{overflow-y:scroll}.content{color:var(--md-dialog-supporting-text-color, var(--md-sys-color-on-surface-variant, #49454f));font-family:var(--md-dialog-supporting-text-font, var(--md-sys-typescale-body-medium-font, var(--md-ref-typeface-plain, Roboto)));font-size:var(--md-dialog-supporting-text-size, var(--md-sys-typescale-body-medium-size, 0.875rem));line-height:var(--md-dialog-supporting-text-line-height, var(--md-sys-typescale-body-medium-line-height, 1.25rem));flex:1;font-weight:var(--md-dialog-supporting-text-weight, var(--md-sys-typescale-body-medium-weight, var(--md-ref-typeface-weight-regular, 400)));height:min-content;position:relative}slot[name=content]::slotted(*){box-sizing:border-box;padding:24px}.anchor{position:absolute}.top.anchor{top:0}.bottom.anchor{bottom:0}.actions{position:relative}slot[name=actions]::slotted(*){box-sizing:border-box;display:flex;gap:8px;justify-content:flex-end;padding:16px 24px 24px}.has-actions slot[name=content]::slotted(*){padding-bottom:8px}md-divider{display:none;position:absolute}.has-headline.show-top-divider .headline md-divider,.has-actions.show-bottom-divider .actions md-divider{display:flex}.headline md-divider{bottom:0}.actions md-divider{top:0}@media(forced-colors: active){dialog{outline:2px solid WindowText}}\n`;\n", "/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: Apache-2.0\n */\n\nimport {CSSResultOrNative} from 'lit';\nimport {customElement} from 'lit/decorators.js';\n\nimport {Dialog} from './internal/dialog.js';\nimport {styles} from './internal/dialog-styles.js';\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'md-dialog': MdDialog;\n  }\n}\n\n/**\n * @summary Dialogs can require an action, communicate information, or help\n * users accomplish a task. There are two types of dialogs: basic and\n * full-screen.\n *\n * @description\n * A dialog is a modal window that appears in front of app content to provide\n * critical information or ask for a decision. Dialogs disable all app\n * functionality when they appear, and remain on screen until confirmed,\n * dismissed, or a required action has been taken.\n *\n * Dialogs are purposefully interruptive, so they should be used sparingly.\n * A less disruptive alternative is to use a menu, which provides options\n * without interrupting a user’s experience.\n *\n * On mobile devices only, complex dialogs should be displayed fullscreen.\n *\n * __Example usages:__\n * - Common use cases for basic dialogs include alerts, quick selection, and\n * confirmation.\n * - More complex dialogs may contain actions that require a series of tasks\n * to complete. One example is creating a calendar entry with the event title,\n * date, location, and time.\n *\n * @final\n * @suppress {visibility}\n */\n@customElement('md-dialog')\nexport class MdDialog extends Dialog {\n  static override styles: CSSResultOrNative[] = [styles];\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmDO,IAAM,gCAAiD;EAC5D,QAAQ;IACN;;MAEE,CAAC,EAAC,aAAa,oBAAmB,GAAG,EAAC,aAAa,gBAAe,CAAC;MACnE,EAAC,UAAU,KAAK,QAAQ,OAAO,WAAU;;;EAG7C,OAAO;IACL;;MAEE,CAAC,EAAC,WAAW,EAAC,GAAG,EAAC,WAAW,KAAI,CAAC;MAClC,EAAC,UAAU,KAAK,QAAQ,SAAQ;;;EAGpC,WAAW;IACT;;MAEE,CAAC,EAAC,WAAW,EAAC,GAAG,EAAC,WAAW,EAAC,CAAC;MAC/B,EAAC,UAAU,IAAI,QAAQ,UAAU,eAAe,WAAU;;IAE5D;;;;;;MAME,CAAC,EAAC,UAAU,MAAK,GAAG,EAAC,UAAU,OAAM,CAAC;MACtC,EAAC,UAAU,KAAK,QAAQ,OAAO,YAAY,eAAe,WAAU;;;EAGxE,UAAU;IACR;;MAEE,CAAC,EAAC,WAAW,EAAC,GAAG,EAAC,WAAW,GAAG,QAAQ,IAAG,GAAG,EAAC,WAAW,EAAC,CAAC;MAC5D,EAAC,UAAU,KAAK,QAAQ,UAAU,MAAM,WAAU;;;EAGtD,SAAS;IACP;;MAEE,CAAC,EAAC,WAAW,EAAC,GAAG,EAAC,WAAW,GAAG,QAAQ,IAAG,GAAG,EAAC,WAAW,EAAC,CAAC;MAC5D,EAAC,UAAU,KAAK,QAAQ,UAAU,MAAM,WAAU;;;EAGtD,SAAS;IACP;;MAEE,CAAC,EAAC,WAAW,EAAC,GAAG,EAAC,WAAW,GAAG,QAAQ,IAAG,GAAG,EAAC,WAAW,EAAC,CAAC;MAC5D,EAAC,UAAU,KAAK,QAAQ,UAAU,MAAM,WAAU;;;;AAQjD,IAAM,iCAAkD;EAC7D,QAAQ;IACN;;MAEE,CAAC,EAAC,aAAa,gBAAe,GAAG,EAAC,aAAa,oBAAmB,CAAC;MACnE,EAAC,UAAU,KAAK,QAAQ,OAAO,sBAAqB;;;EAGxD,OAAO;IACL;;MAEE,CAAC,EAAC,WAAW,KAAI,GAAG,EAAC,WAAW,EAAC,CAAC;MAClC,EAAC,UAAU,KAAK,QAAQ,SAAQ;;;EAGpC,WAAW;IACT;;MAEE,CAAC,EAAC,UAAU,OAAM,GAAG,EAAC,UAAU,MAAK,CAAC;MACtC;QACE,UAAU;QACV,QAAQ,OAAO;QACf,eAAe;;;IAGnB;;MAEE,CAAC,EAAC,WAAW,IAAG,GAAG,EAAC,WAAW,IAAG,CAAC;MACnC,EAAC,OAAO,KAAK,UAAU,IAAI,QAAQ,UAAU,eAAe,WAAU;;;EAG1E,UAAU;IACR;;MAEE,CAAC,EAAC,WAAW,EAAC,GAAG,EAAC,WAAW,EAAC,CAAC;MAC/B,EAAC,UAAU,KAAK,QAAQ,UAAU,MAAM,WAAU;;;EAGtD,SAAS;IACP;;MAEE,CAAC,EAAC,WAAW,EAAC,GAAG,EAAC,WAAW,EAAC,CAAC;MAC/B,EAAC,UAAU,KAAK,QAAQ,UAAU,MAAM,WAAU;;;EAGtD,SAAS;IACP;;MAEE,CAAC,EAAC,WAAW,EAAC,GAAG,EAAC,WAAW,EAAC,CAAC;MAC/B,EAAC,UAAU,KAAK,QAAQ,UAAU,MAAM,WAAU;;;;;;ACrIxD,IAAM,kBAAkB,mBAAmB,UAAU;AAY/C,IAAO,SAAP,cAAsB,gBAAe;;;;;;;EASzC,IAAI,OAAI;AACN,WAAO,KAAK;EACd;EAEA,IAAI,KAAK,MAAa;AACpB,QAAI,SAAS,KAAK,QAAQ;AACxB;IACF;AAEA,SAAK,SAAS;AACd,QAAI,MAAM;AACR,WAAK,aAAa,QAAQ,EAAE;AAC5B,WAAK,KAAI;IACX,OAAO;AACL,WAAK,gBAAgB,MAAM;AAC3B,WAAK,MAAK;IACZ;EACF;EAgGA,cAAA;AACE,UAAK;AA5FoB,SAAA,QAAQ;AAQL,SAAA,cAAc;AAqB5C,SAAA,cAAc;AAMd,SAAA,mBAAmB,MAAM;AAMzB,SAAA,oBAAoB,MAAM;AAElB,SAAA,SAAS;AACT,SAAA,YAAY;AAGZ,SAAA,qBAAqB,KAAK,sBAAqB;AAOtC,SAAA,gBAAgB;AAChB,SAAA,mBAAmB;AAM5B,SAAA,yBAAyB;AAGhB,SAAA,cAAc;AACd,SAAA,aAAa;AACb,SAAA,UAAU;AAgBnB,SAAA,6BAA6B;AAKpB,SAAA,aAAa,WAC1B,OACA,SAAS,iBAAiB,MAAM,WAAW,YAAY;AAIzD,QAAI,CAAC,UAAU;AACb,WAAK,iBAAiB,UAAU,KAAK,YAAY;IACnD;EACF;;;;;;;;;;;EAYA,MAAM,OAAI;AACR,SAAK,YAAY;AAGjB,UAAM,KAAK;AACX,UAAM,KAAK;AACX,UAAM,SAAS,KAAK;AAEpB,QAAI,OAAO,QAAQ,CAAC,KAAK,WAAW;AAClC,WAAK,YAAY;AACjB;IACF;AAEA,UAAM,cAAc,CAAC,KAAK,cACxB,IAAI,MAAM,QAAQ,EAAC,YAAY,KAAI,CAAC,CAAC;AAEvC,QAAI,aAAa;AACf,WAAK,OAAO;AACZ,WAAK,YAAY;AACjB;IACF;AAGA,WAAO,UAAS;AAChB,SAAK,OAAO;AAEZ,QAAI,KAAK,UAAU;AACjB,WAAK,SAAS,YAAY;IAC5B;AAIA,SAAK,cAA2B,aAAa,GAAG,MAAK;AAErD,UAAM,KAAK,cAAc,KAAK,iBAAgB,CAAE;AAChD,SAAK,cAAc,IAAI,MAAM,QAAQ,CAAC;AACtC,SAAK,YAAY;EACnB;;;;;;;;;;;EAYA,MAAM,MAAM,cAAc,KAAK,aAAW;AACxC,SAAK,YAAY;AACjB,QAAI,CAAC,KAAK,aAAa;AAErB,WAAK,OAAO;AACZ;IACF;AAEA,UAAM,KAAK;AACX,UAAM,SAAS,KAAK;AAEpB,QAAI,CAAC,OAAO,QAAQ,KAAK,WAAW;AAClC,WAAK,OAAO;AACZ;IACF;AAEA,UAAM,kBAAkB,KAAK;AAC7B,SAAK,cAAc;AACnB,UAAM,eAAe,CAAC,KAAK,cACzB,IAAI,MAAM,SAAS,EAAC,YAAY,KAAI,CAAC,CAAC;AAExC,QAAI,cAAc;AAChB,WAAK,cAAc;AACnB;IACF;AAEA,UAAM,KAAK,cAAc,KAAK,kBAAiB,CAAE;AACjD,WAAO,MAAM,WAAW;AACxB,SAAK,OAAO;AACZ,SAAK,cAAc,IAAI,MAAM,QAAQ,CAAC;EACxC;EAES,oBAAiB;AACxB,UAAM,kBAAiB;AACvB,SAAK,0BAAyB;EAChC;EAES,uBAAoB;AAC3B,UAAM,qBAAoB;AAC1B,SAAK,qBAAqB,KAAK,sBAAqB;EACtD;EAEmB,SAAM;AACvB,UAAM,aACJ,KAAK,QAAQ,EAAE,KAAK,iBAAiB,KAAK;AAC5C,UAAM,UAAU;MACd,gBAAgB,KAAK;MACrB,eAAe,KAAK;MACpB,YAAY,KAAK;MACjB,cAAc;MACd,oBAAoB,cAAc,CAAC,KAAK;MACxC,uBAAuB,cAAc,CAAC,KAAK;;AAK7C,UAAM,gBAAgB,KAAK,QAAQ,CAAC,KAAK;AACzC,UAAM,YAAY;;;;;iBAKL,KAAK,oBAAoB;;AAGtC,UAAM,EAAC,UAAS,IAAI;AACpB,WAAO;;;gBAGK,SAAS,OAAO,CAAC;qBACZ,aAAa,OAAO;0BACf,KAAK,cAAc,aAAa,OAAO;eAClD,KAAK,SAAS,UAAU,gBAAgB,OAAO;kBAC5C,KAAK,YAAY;iBAClB,KAAK,iBAAiB;iBACtB,KAAK,WAAW;mBACd,KAAK,aAAa;uBACd,KAAK,eAAe,OAAO;UACxC,gBAAgB,YAAY,OAAO;wCACL,KAAK,kBAAkB;;;8CAGjB,KAAK,gBAAgB;;4CAEvB,CAAC,KAAK,eAAe,OAAO;;;8BAG1C,KAAK,oBAAoB;;;;;;;;;;;;;+CAaR,KAAK,mBAAmB;;;UAG7D,gBAAgB,YAAY,OAAO;;;EAG3C;EAEmB,eAAY;AAC7B,SAAK,uBAAuB,IAAI,qBAC9B,CAAC,YAAW;AACV,iBAAW,SAAS,SAAS;AAC3B,aAAK,yBAAyB,KAAK;MACrC;IACF,GACA,EAAC,MAAM,KAAK,SAAS,CAAC;AAGxB,SAAK,qBAAqB,QAAQ,KAAK,SAAU;AACjD,SAAK,qBAAqB,QAAQ,KAAK,YAAa;EACtD;EAEQ,oBAAiB;AACvB,QAAI,KAAK,wBAAwB;AAG/B,WAAK,yBAAyB;AAC9B;IACF;AAIA,UAAM,iBAAiB,CAAC,KAAK,cAC3B,IAAI,MAAM,UAAU,EAAC,YAAY,KAAI,CAAC,CAAC;AAEzC,QAAI,gBAAgB;AAClB;IACF;AAEA,SAAK,MAAK;EACZ;EAEQ,qBAAkB;AACxB,SAAK,yBAAyB;EAChC;EAEQ,aAAa,OAAkB;AACrC,UAAM,OAAO,MAAM;AACnB,UAAM,EAAC,UAAS,IAAI;AACpB,QAAI,KAAK,aAAa,QAAQ,MAAM,YAAY,CAAC,WAAW;AAC1D;IACF;AAIA,SAAK,MAAM,UAAU,aAAa,OAAO,KAAK,KAAK,WAAW;EAChE;EAEQ,aAAa,OAAY;AAC/B,QAAI,MAAM,WAAW,KAAK,QAAQ;AAEhC;IACF;AAEA,SAAK,6BAA6B;AAClC,UAAM,iBAAiB,CAAC,gBAAgB,MAAM,KAAK;AAGnD,UAAM,eAAc;AACpB,QAAI,gBAAgB;AAClB;IACF;AAEA,SAAK,MAAK;EACZ;EAEQ,cAAW;AACjB,QAAI,CAAC,KAAK,4BAA4B;AACpC;IACF;AAEA,SAAK,6BAA6B;AAClC,SAAK,QAAQ,cAAc,IAAI,MAAM,UAAU,EAAC,YAAY,KAAI,CAAC,CAAC;EACpE;EAEQ,cAAc,OAAoB;AACxC,QAAI,MAAM,QAAQ,UAAU;AAC1B;IACF;AAIA,SAAK,6BAA6B;AAGlC,eAAW,MAAK;AACd,WAAK,6BAA6B;IACpC,CAAC;EACH;EAEQ,MAAM,cAAc,WAA0B;AAKpD,SAAK,kBAAkB,MAAK;AAC5B,SAAK,mBAAmB,IAAI,gBAAe;AAC3C,QAAI,KAAK,OAAO;AACd;IACF;AAEA,UAAM,EAAC,QAAQ,OAAO,WAAW,UAAU,SAAS,QAAO,IAAI;AAC/D,QAAI,CAAC,UAAU,CAAC,SAAS,CAAC,aAAa,CAAC,YAAY,CAAC,WAAW,CAAC,SAAS;AACxE;IACF;AAEA,UAAM,EACJ,WAAW,kBACX,QAAQ,eACR,OAAO,cACP,UAAU,iBACV,SAAS,gBACT,SAAS,eAAc,IACrB;AAEJ,UAAM,sBAA+D;MACnE,CAAC,QAAQ,iBAAiB,CAAA,CAAE;MAC5B,CAAC,OAAO,gBAAgB,CAAA,CAAE;MAC1B,CAAC,WAAW,oBAAoB,CAAA,CAAE;MAClC,CAAC,UAAU,mBAAmB,CAAA,CAAE;MAChC,CAAC,SAAS,kBAAkB,CAAA,CAAE;MAC9B,CAAC,SAAS,kBAAkB,CAAA,CAAE;;AAGhC,UAAM,aAA0B,CAAA;AAChC,eAAW,CAAC,SAASA,UAAS,KAAK,qBAAqB;AACtD,iBAAW,eAAeA,YAAW;AACnC,cAAMA,aAAY,QAAQ,QAAQ,GAAG,WAAW;AAChD,aAAK,iBAAiB,OAAO,iBAAiB,SAAS,MAAK;AAC1D,UAAAA,WAAU,OAAM;QAClB,CAAC;AAED,mBAAW,KAAKA,UAAS;MAC3B;IACF;AAEA,UAAM,QAAQ,IACZ,WAAW,IAAI,CAACA,eACdA,WAAU,SAAS,MAAM,MAAK;IAE9B,CAAC,CAAC,CACH;EAEL;EAEQ,qBAAqB,OAAY;AACvC,UAAM,OAAO,MAAM;AACnB,SAAK,cAAc,KAAK,iBAAgB,EAAG,SAAS;EACtD;EAEQ,oBAAoB,OAAY;AACtC,UAAM,OAAO,MAAM;AACnB,SAAK,aAAa,KAAK,iBAAgB,EAAG,SAAS;EACrD;EAEQ,iBAAiB,OAAY;AACnC,UAAM,OAAO,MAAM;AACnB,SAAK,UAAU,KAAK,iBAAgB,EAAG,SAAS;EAClD;EAEQ,yBAAyB,OAAgC;AAC/D,UAAM,EAAC,QAAQ,eAAc,IAAI;AACjC,QAAI,WAAW,KAAK,WAAW;AAC7B,WAAK,gBAAgB;IACvB;AAEA,QAAI,WAAW,KAAK,cAAc;AAChC,WAAK,mBAAmB;IAC1B;EACF;EAEQ,wBAAqB;AAC3B,WAAO,IAAI,QAAc,CAAC,YAAW;AACnC,WAAK,4BAA4B;IACnC,CAAC;EACH;EAEQ,qBAAqB,OAAiB;AAC5C,UAAM,CAAC,qBAAqB,kBAAkB,IAC5C,KAAK,iCAAgC;AACvC,QAAI,CAAC,uBAAuB,CAAC,oBAAoB;AAG/C,WAAK,QAAQ,MAAK;AAClB;IACF;AAIA,UAAM,mBAAmB,MAAM,WAAW,KAAK;AAC/C,UAAM,kBAAkB,CAAC;AAEzB,UAAM,0BAA0B,MAAM,kBAAkB;AACxD,UAAM,yBAAyB,MAAM,kBAAkB;AAMvD,UAAM,6BACJ,CAAC,2BAA2B,CAAC;AAK/B,UAAM,wBACH,mBAAmB,0BACnB,oBAAoB;AACvB,QAAI,uBAAuB;AACzB,0BAAoB,MAAK;AACzB;IACF;AAKA,UAAM,uBACH,oBAAoB,2BACpB,mBAAmB;AACtB,QAAI,sBAAsB;AACxB,yBAAmB,MAAK;AACxB;IACF;EAIF;EAEQ,mCAAgC;AAGtC,QAAI,CAAC,KAAK,YAAY;AACpB,aAAO,CAAC,MAAM,IAAI;IACpB;AAEA,QAAI,sBAA0C;AAC9C,QAAI,qBAAyC;AAG7C,SAAK,WAAW,cAAc,KAAK,WAAW;AAC9C,WAAO,KAAK,WAAW,SAAQ,GAAI;AAEjC,YAAM,YAAY,KAAK,WAAW;AAClC,UAAI,CAAC,YAAY,SAAS,GAAG;AAC3B;MACF;AAEA,UAAI,CAAC,qBAAqB;AACxB,8BAAsB;MACxB;AAEA,2BAAqB;IACvB;AAKA,WAAO,CAAC,qBAAqB,kBAAkB;EAGjD;;AAriBA,WAAA;EADC,SAAS,EAAC,MAAM,QAAO,CAAC;;AAuBE,WAAA;EAA1B,SAAS,EAAC,MAAM,QAAO,CAAC;;AAQK,WAAA;EAA7B,SAAS,EAAC,WAAW,MAAK,CAAC;;AAMhB,WAAA;EAAX,SAAQ;;AAeT,WAAA;EADC,SAAS,EAAC,MAAM,SAAS,WAAW,gBAAe,CAAC;;AAoBnB,WAAA;EAAjC,MAAM,QAAQ;;AACmB,WAAA;EAAjC,MAAM,QAAQ;;AACuB,WAAA;EAArC,MAAM,YAAY;;AACkB,WAAA;EAApC,MAAM,WAAW;;AACkB,WAAA;EAAnC,MAAM,UAAU;;AACmB,WAAA;EAAnC,MAAM,UAAU;;AACA,WAAA;EAAhB,MAAK;;AACW,WAAA;EAAhB,MAAK;;AAC+B,WAAA;EAApC,MAAM,WAAW;;AACqB,WAAA;EAAtC,MAAM,aAAa;;AACsB,WAAA;EAAzC,MAAM,gBAAgB;;AAEN,WAAA;EADhB,MAAM,aAAa;;AAKH,WAAA;EAAhB,MAAK;;AACW,WAAA;EAAhB,MAAK;;AACW,WAAA;EAAhB,MAAK;;AAgdR,SAAS,YAAY,SAAgB;AAMnC,QAAM,yBACJ;AACF,QAAM,cAAc;AACpB,QAAM,sBAAsB;AAC5B,MACE,QAAQ,QAAQ,yBAAyB,cAAc,mBAAmB,GAC1E;AACA,WAAO;EACT;AAEA,QAAM,kBAAkB,QAAQ,UAAU,SAAS,GAAG;AACtD,MAAI,CAAC,iBAAiB;AACpB,WAAO;EACT;AAKA,MAAI,CAAC,QAAQ,QAAQ,WAAW,GAAG;AACjC,WAAO;EACT;AAEA,SAAO,QAAQ,YAAY,kBAAkB;AAC/C;;;AC3mBO,IAAM,SAAS;;;;ACuCf,IAAM,WAAN,MAAMC,kBAAiB,OAAM;;AAClB,SAAA,SAA8B,CAAC,MAAM;AAD1C,WAAQ,WAAA;EADpB,cAAc,WAAW;GACb,QAAQ;", "names": ["animation", "MdDialog"]}