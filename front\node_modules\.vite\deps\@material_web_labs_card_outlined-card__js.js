import {
  Card,
  styles
} from "./chunk-JAQOZDDJ.js";
import "./chunk-NQOB77XV.js";
import {
  __decorate,
  customElement
} from "./chunk-PZNDE6JX.js";
import {
  css
} from "./chunk-4GZ3EDRH.js";
import "./chunk-5WRI5ZAA.js";

// node_modules/@material/web/labs/card/internal/outlined-styles.js
var styles2 = css`:host{--_container-color: var(--md-outlined-card-container-color, var(--md-sys-color-surface, #fef7ff));--_container-elevation: var(--md-outlined-card-container-elevation, 0);--_container-shadow-color: var(--md-outlined-card-container-shadow-color, var(--md-sys-color-shadow, #000));--_container-shape: var(--md-outlined-card-container-shape, var(--md-sys-shape-corner-medium, 12px));--_outline-color: var(--md-outlined-card-outline-color, var(--md-sys-color-outline-variant, #cac4d0));--_outline-width: var(--md-outlined-card-outline-width, 1px)}.outline{border-color:var(--_outline-color);border-width:var(--_outline-width)}
`;

// node_modules/@material/web/labs/card/outlined-card.js
var MdOutlinedCard = class MdOutlinedCard2 extends Card {
};
MdOutlinedCard.styles = [styles, styles2];
MdOutlinedCard = __decorate([
  customElement("md-outlined-card")
], MdOutlinedCard);
export {
  MdOutlinedCard
};
/*! Bundled license information:

@material/web/labs/card/internal/outlined-styles.js:
  (**
   * @license
   * Copyright 2024 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   *)

@material/web/labs/card/outlined-card.js:
  (**
   * @license
   * Copyright 2023 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   *)
*/
//# sourceMappingURL=@material_web_labs_card_outlined-card__js.js.map
