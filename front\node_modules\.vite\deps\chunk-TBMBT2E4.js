import {
  MenuItemController,
  styles
} from "./chunk-5GK7SOHM.js";
import {
  mixinDelegatesAria
} from "./chunk-GXE4MBY5.js";
import {
  classMap
} from "./chunk-SZQCPKZF.js";
import {
  __decorate,
  customElement,
  property,
  query,
  queryAssignedElements,
  queryAssignedNodes
} from "./chunk-PZNDE6JX.js";
import {
  LitElement,
  html,
  nothing
} from "./chunk-4GZ3EDRH.js";

// node_modules/@material/web/select/internal/selectoption/selectOptionController.js
function createRequestSelectionEvent() {
  return new Event("request-selection", {
    bubbles: true,
    composed: true
  });
}
function createRequestDeselectionEvent() {
  return new Event("request-deselection", {
    bubbles: true,
    composed: true
  });
}
var SelectOptionController = class {
  /**
   * The recommended role of the select option.
   */
  get role() {
    return this.menuItemController.role;
  }
  /**
   * The text that is selectable via typeahead. If not set, defaults to the
   * innerText of the item slotted into the `"headline"` slot, and if there are
   * no slotted elements into headline, then it checks the _default_ slot, and
   * then the `"supporting-text"` slot if nothing is in _default_.
   */
  get typeaheadText() {
    return this.menuItemController.typeaheadText;
  }
  setTypeaheadText(text) {
    this.menuItemController.setTypeaheadText(text);
  }
  /**
   * The text that is displayed in the select field when selected. If not set,
   * defaults to the textContent of the item slotted into the `"headline"` slot,
   * and if there are no slotted elements into headline, then it checks the
   * _default_ slot, and then the `"supporting-text"` slot if nothing is in
   * _default_.
   */
  get displayText() {
    if (this.internalDisplayText !== null) {
      return this.internalDisplayText;
    }
    return this.menuItemController.typeaheadText;
  }
  setDisplayText(text) {
    this.internalDisplayText = text;
  }
  /**
   * @param host The SelectOption in which to attach this controller to.
   * @param config The object that configures this controller's behavior.
   */
  constructor(host, config) {
    this.host = host;
    this.internalDisplayText = null;
    this.firstUpdate = true;
    this.onClick = () => {
      this.menuItemController.onClick();
    };
    this.onKeydown = (e) => {
      this.menuItemController.onKeydown(e);
    };
    this.lastSelected = this.host.selected;
    this.menuItemController = new MenuItemController(host, config);
    host.addController(this);
  }
  hostUpdate() {
    if (this.lastSelected !== this.host.selected) {
      this.host.ariaSelected = this.host.selected ? "true" : "false";
    }
  }
  hostUpdated() {
    if (this.lastSelected !== this.host.selected && !this.firstUpdate) {
      if (this.host.selected) {
        this.host.dispatchEvent(createRequestSelectionEvent());
      } else {
        this.host.dispatchEvent(createRequestDeselectionEvent());
      }
    }
    this.lastSelected = this.host.selected;
    this.firstUpdate = false;
  }
};

// node_modules/@material/web/select/internal/selectoption/select-option.js
var selectOptionBaseClass = mixinDelegatesAria(LitElement);
var SelectOptionEl = class extends selectOptionBaseClass {
  constructor() {
    super(...arguments);
    this.disabled = false;
    this.isMenuItem = true;
    this.selected = false;
    this.value = "";
    this.type = "option";
    this.selectOptionController = new SelectOptionController(this, {
      getHeadlineElements: () => {
        return this.headlineElements;
      },
      getSupportingTextElements: () => {
        return this.supportingTextElements;
      },
      getDefaultElements: () => {
        return this.defaultElements;
      },
      getInteractiveElement: () => this.listItemRoot
    });
  }
  /**
   * The text that is selectable via typeahead. If not set, defaults to the
   * innerText of the item slotted into the `"headline"` slot.
   */
  get typeaheadText() {
    return this.selectOptionController.typeaheadText;
  }
  set typeaheadText(text) {
    this.selectOptionController.setTypeaheadText(text);
  }
  /**
   * The text that is displayed in the select field when selected. If not set,
   * defaults to the textContent of the item slotted into the `"headline"` slot.
   */
  get displayText() {
    return this.selectOptionController.displayText;
  }
  set displayText(text) {
    this.selectOptionController.setDisplayText(text);
  }
  render() {
    return this.renderListItem(html`
      <md-item>
        <div slot="container">
          ${this.renderRipple()} ${this.renderFocusRing()}
        </div>
        <slot name="start" slot="start"></slot>
        <slot name="end" slot="end"></slot>
        ${this.renderBody()}
      </md-item>
    `);
  }
  /**
   * Renders the root list item.
   *
   * @param content the child content of the list item.
   */
  renderListItem(content) {
    return html`
      <li
        id="item"
        tabindex=${this.disabled ? -1 : 0}
        role=${this.selectOptionController.role}
        aria-label=${this.ariaLabel || nothing}
        aria-selected=${this.ariaSelected || nothing}
        aria-checked=${this.ariaChecked || nothing}
        aria-expanded=${this.ariaExpanded || nothing}
        aria-haspopup=${this.ariaHasPopup || nothing}
        class="list-item ${classMap(this.getRenderClasses())}"
        @click=${this.selectOptionController.onClick}
        @keydown=${this.selectOptionController.onKeydown}
        >${content}</li
      >
    `;
  }
  /**
   * Handles rendering of the ripple element.
   */
  renderRipple() {
    return html` <md-ripple
      part="ripple"
      for="item"
      ?disabled=${this.disabled}></md-ripple>`;
  }
  /**
   * Handles rendering of the focus ring.
   */
  renderFocusRing() {
    return html` <md-focus-ring
      part="focus-ring"
      for="item"
      inward></md-focus-ring>`;
  }
  /**
   * Classes applied to the list item root.
   */
  getRenderClasses() {
    return {
      "disabled": this.disabled,
      "selected": this.selected
    };
  }
  /**
   * Handles rendering the headline and supporting text.
   */
  renderBody() {
    return html`
      <slot></slot>
      <slot name="overline" slot="overline"></slot>
      <slot name="headline" slot="headline"></slot>
      <slot name="supporting-text" slot="supporting-text"></slot>
      <slot
        name="trailing-supporting-text"
        slot="trailing-supporting-text"></slot>
    `;
  }
  focus() {
    this.listItemRoot?.focus();
  }
};
SelectOptionEl.shadowRootOptions = {
  ...LitElement.shadowRootOptions,
  delegatesFocus: true
};
__decorate([
  property({ type: Boolean, reflect: true })
], SelectOptionEl.prototype, "disabled", void 0);
__decorate([
  property({ type: Boolean, attribute: "md-menu-item", reflect: true })
], SelectOptionEl.prototype, "isMenuItem", void 0);
__decorate([
  property({ type: Boolean })
], SelectOptionEl.prototype, "selected", void 0);
__decorate([
  property()
], SelectOptionEl.prototype, "value", void 0);
__decorate([
  query(".list-item")
], SelectOptionEl.prototype, "listItemRoot", void 0);
__decorate([
  queryAssignedElements({ slot: "headline" })
], SelectOptionEl.prototype, "headlineElements", void 0);
__decorate([
  queryAssignedElements({ slot: "supporting-text" })
], SelectOptionEl.prototype, "supportingTextElements", void 0);
__decorate([
  queryAssignedNodes({ slot: "" })
], SelectOptionEl.prototype, "defaultElements", void 0);
__decorate([
  property({ attribute: "typeahead-text" })
], SelectOptionEl.prototype, "typeaheadText", null);
__decorate([
  property({ attribute: "display-text" })
], SelectOptionEl.prototype, "displayText", null);

// node_modules/@material/web/select/select-option.js
var MdSelectOption = class MdSelectOption2 extends SelectOptionEl {
};
MdSelectOption.styles = [styles];
MdSelectOption = __decorate([
  customElement("md-select-option")
], MdSelectOption);

export {
  MdSelectOption
};
/*! Bundled license information:

@material/web/select/internal/selectoption/selectOptionController.js:
@material/web/select/internal/selectoption/select-option.js:
@material/web/select/select-option.js:
  (**
   * @license
   * Copyright 2023 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   *)
*/
//# sourceMappingURL=chunk-TBMBT2E4.js.map
